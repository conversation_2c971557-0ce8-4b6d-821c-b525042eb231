import { z } from 'zod';
import { TOUR_CATEGORIES, TOUR_STATUS, TOUR_VISIBILITY, NIGERIAN_STATES } from '@/lib/constants';

// Tour creation/update validation schema
export const tourSchema = z.object({
  title: z
    .string()
    .min(3, 'Title must be at least 3 characters long')
    .max(100, 'Title must be less than 100 characters'),
  description: z
    .string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
  category: z
    .string()
    .min(1, 'Please select a category')
    .refine(
      (category) => TOUR_CATEGORIES.some(cat => cat.id === category),
      'Please select a valid category'
    ),
  tags: z
    .array(z.string())
    .max(10, 'Maximum 10 tags allowed')
    .optional(),
  location: z
    .string()
    .max(100, 'Location must be less than 100 characters')
    .optional(),
  latitude: z
    .number()
    .min(-90, 'Invalid latitude')
    .max(90, 'Invalid latitude')
    .optional(),
  longitude: z
    .number()
    .min(-180, 'Invalid longitude')
    .max(180, 'Invalid longitude')
    .optional(),
  address: z
    .string()
    .max(200, 'Address must be less than 200 characters')
    .optional(),
  price: z
    .number()
    .min(0, 'Price must be a positive number')
    .optional(),
  currency: z
    .string()
    .length(3, 'Currency must be a 3-letter code')
    .default('NGN'),
  status: z
    .enum([TOUR_STATUS.DRAFT, TOUR_STATUS.PUBLISHED, TOUR_STATUS.ARCHIVED])
    .default(TOUR_STATUS.DRAFT),
  visibility: z
    .enum([TOUR_VISIBILITY.PUBLIC, TOUR_VISIBILITY.PRIVATE, TOUR_VISIBILITY.UNLISTED])
    .default(TOUR_VISIBILITY.PUBLIC),
  seoTitle: z
    .string()
    .max(60, 'SEO title must be less than 60 characters')
    .optional(),
  seoDescription: z
    .string()
    .max(160, 'SEO description must be less than 160 characters')
    .optional(),
  seoKeywords: z
    .array(z.string())
    .max(10, 'Maximum 10 SEO keywords allowed')
    .optional(),
});

// Scene creation/update validation schema
export const sceneSchema = z.object({
  title: z
    .string()
    .min(2, 'Title must be at least 2 characters long')
    .max(100, 'Title must be less than 100 characters'),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  mediaId: z
    .string()
    .min(1, 'Please select a media file'),
  orderIndex: z
    .number()
    .min(0, 'Order index must be a positive number'),
  isStartingScene: z
    .boolean()
    .default(false),
  position: z
    .object({
      x: z.number(),
      y: z.number(),
      z: z.number(),
    })
    .optional(),
  rotation: z
    .object({
      x: z.number(),
      y: z.number(),
      z: z.number(),
    })
    .optional(),
  settings: z
    .record(z.any())
    .optional(),
});

// Hotspot creation/update validation schema
export const hotspotSchema = z.object({
  type: z
    .enum(['navigation', 'info', 'media', 'link', 'whatsapp', 'product'])
    .refine((type) => type !== undefined, 'Please select a hotspot type'),
  title: z
    .string()
    .max(100, 'Title must be less than 100 characters')
    .optional(),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  position: z.object({
    x: z.number().min(-1).max(1),
    y: z.number().min(-1).max(1),
    z: z.number().min(-1).max(1),
  }),
  rotation: z
    .object({
      x: z.number(),
      y: z.number(),
      z: z.number(),
    })
    .optional(),
  scale: z
    .object({
      x: z.number().min(0.1).max(5),
      y: z.number().min(0.1).max(5),
      z: z.number().min(0.1).max(5),
    })
    .default({ x: 1, y: 1, z: 1 }),
  targetSceneId: z
    .string()
    .optional(),
  targetUrl: z
    .string()
    .url('Please enter a valid URL')
    .optional()
    .or(z.literal('')),
  mediaId: z
    .string()
    .optional(),
  whatsappNumber: z
    .string()
    .optional()
    .refine(
      (phone) => {
        if (!phone) return true;
        const cleaned = phone.replace(/\D/g, '');
        return /^(234|0)?[789][01]\d{8}$/.test(cleaned);
      },
      'Please enter a valid Nigerian phone number'
    ),
  whatsappMessage: z
    .string()
    .max(500, 'WhatsApp message must be less than 500 characters')
    .optional(),
  productData: z
    .object({
      name: z.string().optional(),
      price: z.number().optional(),
      description: z.string().optional(),
      image: z.string().optional(),
      url: z.string().url().optional(),
    })
    .optional(),
  styleConfig: z
    .object({
      color: z.string().optional(),
      size: z.number().min(0.5).max(3).optional(),
      opacity: z.number().min(0).max(1).optional(),
      animation: z.string().optional(),
    })
    .optional(),
  animationConfig: z
    .object({
      type: z.string().optional(),
      duration: z.number().min(0).max(5000).optional(),
      delay: z.number().min(0).max(5000).optional(),
      loop: z.boolean().optional(),
    })
    .optional(),
  isVisible: z
    .boolean()
    .default(true),
});

// Tour search/filter validation schema
export const tourSearchSchema = z.object({
  query: z
    .string()
    .max(100, 'Search query must be less than 100 characters')
    .optional(),
  category: z
    .string()
    .optional(),
  location: z
    .string()
    .optional(),
  state: z
    .string()
    .optional()
    .refine(
      (state) => {
        if (!state) return true;
        return NIGERIAN_STATES.includes(state as any);
      },
      'Please select a valid Nigerian state'
    ),
  minPrice: z
    .number()
    .min(0, 'Minimum price must be positive')
    .optional(),
  maxPrice: z
    .number()
    .min(0, 'Maximum price must be positive')
    .optional(),
  featured: z
    .boolean()
    .optional(),
  sortBy: z
    .enum(['created_at', 'updated_at', 'views', 'likes', 'title'])
    .default('created_at'),
  sortOrder: z
    .enum(['asc', 'desc'])
    .default('desc'),
  page: z
    .number()
    .min(1, 'Page must be at least 1')
    .default(1),
  limit: z
    .number()
    .min(1, 'Limit must be at least 1')
    .max(50, 'Limit must be at most 50')
    .default(12),
}).refine((data) => {
  if (data.minPrice && data.maxPrice) {
    return data.minPrice <= data.maxPrice;
  }
  return true;
}, {
  message: 'Minimum price must be less than or equal to maximum price',
  path: ['maxPrice'],
});

// Tour analytics query validation schema
export const tourAnalyticsSchema = z.object({
  tourId: z.string().min(1, 'Tour ID is required'),
  startDate: z
    .string()
    .datetime('Invalid start date format')
    .optional(),
  endDate: z
    .string()
    .datetime('Invalid end date format')
    .optional(),
  granularity: z
    .enum(['hour', 'day', 'week', 'month'])
    .default('day'),
  metrics: z
    .array(z.enum(['views', 'likes', 'shares', 'time_spent', 'interactions']))
    .default(['views']),
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Start date must be before end date',
  path: ['endDate'],
});

// Tour sharing validation schema
export const tourSharingSchema = z.object({
  platform: z.enum(['facebook', 'twitter', 'linkedin', 'whatsapp', 'telegram', 'email', 'link']),
  tourId: z.string().min(1, 'Tour ID is required'),
  customMessage: z
    .string()
    .max(500, 'Custom message must be less than 500 characters')
    .optional(),
});

// Tour embedding validation schema
export const tourEmbedSchema = z.object({
  tourId: z.string().min(1, 'Tour ID is required'),
  width: z
    .number()
    .min(300, 'Width must be at least 300px')
    .max(2000, 'Width must be at most 2000px')
    .default(800),
  height: z
    .number()
    .min(200, 'Height must be at least 200px')
    .max(1500, 'Height must be at most 1500px')
    .default(600),
  autoplay: z
    .boolean()
    .default(false),
  showControls: z
    .boolean()
    .default(true),
  showInfo: z
    .boolean()
    .default(true),
  theme: z
    .enum(['light', 'dark'])
    .default('light'),
});

// Tour duplication validation schema
export const tourDuplicateSchema = z.object({
  tourId: z.string().min(1, 'Tour ID is required'),
  newTitle: z
    .string()
    .min(3, 'Title must be at least 3 characters long')
    .max(100, 'Title must be less than 100 characters'),
  includeScenes: z
    .boolean()
    .default(true),
  includeHotspots: z
    .boolean()
    .default(true),
  includeMedia: z
    .boolean()
    .default(false),
});

// Bulk operations validation schema
export const bulkOperationSchema = z.object({
  operation: z.enum(['delete', 'archive', 'publish', 'unpublish', 'feature', 'unfeature']),
  tourIds: z
    .array(z.string())
    .min(1, 'At least one tour must be selected')
    .max(50, 'Maximum 50 tours can be processed at once'),
});

// Export types
export type TourInput = z.infer<typeof tourSchema>;
export type SceneInput = z.infer<typeof sceneSchema>;
export type HotspotInput = z.infer<typeof hotspotSchema>;
export type TourSearchInput = z.infer<typeof tourSearchSchema>;
export type TourAnalyticsInput = z.infer<typeof tourAnalyticsSchema>;
export type TourSharingInput = z.infer<typeof tourSharingSchema>;
export type TourEmbedInput = z.infer<typeof tourEmbedSchema>;
export type TourDuplicateInput = z.infer<typeof tourDuplicateSchema>;
export type BulkOperationInput = z.infer<typeof bulkOperationSchema>;
