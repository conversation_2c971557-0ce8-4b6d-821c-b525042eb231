import { supabase, createClient } from './client';
import { createServerSupabaseClient, createClient as createServerClient, serverDb } from './server';
import type { Database } from '@/types/supabase';

// Type aliases for better readability
type Tour = Database['public']['Tables']['tours']['Row'];
type Scene = Database['public']['Tables']['scenes']['Row'];
type Hotspot = Database['public']['Tables']['hotspots']['Row'];
type User = Database['public']['Tables']['users']['Row'];

// Client-side operations (for use in components)
export const clientOperations = {
  // Tour operations
  tours: {
    async getPublished(limit = 20, offset = 0) {
      const { data, error } = await supabase
        .from('tours')
        .select(`
          *,
          users:user_id (
            full_name,
            avatar_url,
            company
          ),
          scenes:scenes (
            id,
            title,
            thumbnail_url
          )
        `)
        .eq('status', 'published')
        .eq('is_public', true)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;
      return data;
    },

    async getBySlug(slug: string) {
      const { data, error } = await supabase
        .from('tours')
        .select(`
          *,
          users:user_id (
            full_name,
            avatar_url,
            company
          ),
          scenes:scenes (
            *,
            hotspots:hotspots (*)
          )
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .eq('is_public', true)
        .single();

      if (error) throw error;
      return data;
    },

    async getUserTours(userId: string) {
      const { data, error } = await supabase
        .from('tours')
        .select(`
          *,
          scenes:scenes(count)
        `)
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });

      if (error) throw error;
      return data;
    },

    async create(tourData: Partial<Tour>) {
      const { data, error } = await supabase
        .from('tours')
        .insert(tourData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    async update(id: string, updates: Partial<Tour>) {
      const { data, error } = await supabase
        .from('tours')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    async delete(id: string) {
      const { error } = await supabase
        .from('tours')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },

    async incrementViews(id: string) {
      const { error } = await supabase.rpc('increment_tour_views', {
        tour_id: id,
      });

      if (error) throw error;
    },
  },

  // Scene operations
  scenes: {
    async getByTourId(tourId: string) {
      const { data, error } = await supabase
        .from('scenes')
        .select(`
          *,
          hotspots:hotspots (*)
        `)
        .eq('tour_id', tourId)
        .order('order_index');

      if (error) throw error;
      return data;
    },

    async create(sceneData: Partial<Scene>) {
      const { data, error } = await supabase
        .from('scenes')
        .insert(sceneData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    async update(id: string, updates: Partial<Scene>) {
      const { data, error } = await supabase
        .from('scenes')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    async delete(id: string) {
      const { error } = await supabase
        .from('scenes')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },

    async reorder(tourId: string, sceneOrders: { id: string; order_index: number }[]) {
      const updates = sceneOrders.map(({ id, order_index }) =>
        supabase
          .from('scenes')
          .update({ order_index })
          .eq('id', id)
      );

      const results = await Promise.all(updates);
      const errors = results.filter(result => result.error);

      if (errors.length > 0) {
        throw new Error(`Failed to reorder scenes: ${errors[0].error?.message}`);
      }
    },
  },

  // Hotspot operations
  hotspots: {
    async getBySceneId(sceneId: string) {
      const { data, error } = await supabase
        .from('hotspots')
        .select('*')
        .eq('scene_id', sceneId)
        .eq('is_active', true);

      if (error) throw error;
      return data;
    },

    async create(hotspotData: Partial<Hotspot>) {
      const { data, error } = await supabase
        .from('hotspots')
        .insert(hotspotData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    async update(id: string, updates: Partial<Hotspot>) {
      const { data, error } = await supabase
        .from('hotspots')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    async delete(id: string) {
      const { error } = await supabase
        .from('hotspots')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
  },

  // User operations
  users: {
    async getProfile() {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      return data;
    },

    async updateProfile(updates: Partial<User>) {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
  },

  // Analytics operations
  analytics: {
    async trackEvent(tourId: string, eventType: string, eventData?: any) {
      const { error } = await supabase
        .from('tour_analytics')
        .insert({
          tour_id: tourId,
          event_type: eventType,
          event_data: eventData || {},
          created_at: new Date().toISOString(),
        });

      if (error) console.error('Analytics tracking failed:', error);
    },

    async getTourStats(tourId: string, startDate?: string, endDate?: string) {
      let query = supabase
        .from('tour_analytics')
        .select('event_type, created_at, event_data')
        .eq('tour_id', tourId);

      if (startDate) query = query.gte('created_at', startDate);
      if (endDate) query = query.lte('created_at', endDate);

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  },
};

// Server-side operations (for use in API routes and server components)
export const serverOperations = {
  // Authentication helpers
  auth: {
    async requireAuth() {
      const supabase = createServerSupabaseClient();
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        throw new Error('Authentication required');
      }

      return user;
    },

    async requireRole(role: string) {
      const user = await this.requireAuth();
      const profile = await serverDb.getUserProfile(user.id);

      if (!profile || profile.role !== role) {
        throw new Error(`Role '${role}' required`);
      }

      return { user, profile };
    },
  },

  // File upload operations
  storage: {
    async uploadTourMedia(file: File, userId: string, tourId: string) {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `tours/${tourId}/${fileName}`;

      const { data, error } = await supabase.storage
        .from('media')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
        });

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('media')
        .getPublicUrl(filePath);

      // Save media record to database
      const { data: mediaRecord, error: dbError } = await supabase
        .from('media_files')
        .insert({
          user_id: userId,
          filename: fileName,
          original_filename: file.name,
          file_path: filePath,
          file_size: file.size,
          mime_type: file.type,
          metadata: {
            tour_id: tourId,
            upload_date: new Date().toISOString(),
          },
        })
        .select()
        .single();

      if (dbError) throw dbError;

      return {
        ...mediaRecord,
        public_url: publicUrl,
      };
    },

    async deleteMedia(mediaId: string, userId: string) {
      // Get media record
      const { data: media, error: fetchError } = await supabase
        .from('media_files')
        .select('*')
        .eq('id', mediaId)
        .eq('user_id', userId)
        .single();

      if (fetchError) throw fetchError;

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('media')
        .remove([media.file_path]);

      if (storageError) throw storageError;

      // Delete from database
      const { error: dbError } = await supabase
        .from('media_files')
        .delete()
        .eq('id', mediaId);

      if (dbError) throw dbError;
    },
  },
};

// Real-time subscriptions
export const subscriptions = {
  // Subscribe to tour changes
  subscribeTourChanges(tourId: string, callback: (payload: any) => void) {
    const channel = supabase
      .channel(`tour_${tourId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tours',
          filter: `id=eq.${tourId}`,
        },
        callback
      )
      .subscribe();

    return () => supabase.removeChannel(channel);
  },

  // Subscribe to scene changes in a tour
  subscribeSceneChanges(tourId: string, callback: (payload: any) => void) {
    const channel = supabase
      .channel(`scenes_${tourId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'scenes',
          filter: `tour_id=eq.${tourId}`,
        },
        callback
      )
      .subscribe();

    return () => supabase.removeChannel(channel);
  },

  // Subscribe to hotspot changes in a scene
  subscribeHotspotChanges(sceneId: string, callback: (payload: any) => void) {
    const channel = supabase
      .channel(`hotspots_${sceneId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'hotspots',
          filter: `scene_id=eq.${sceneId}`,
        },
        callback
      )
      .subscribe();

    return () => supabase.removeChannel(channel);
  },
};
