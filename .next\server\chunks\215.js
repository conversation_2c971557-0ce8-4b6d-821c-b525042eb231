"use strict";exports.id=215,exports.ids=[215],exports.modules={10590:(e,a,s)=>{s.d(a,{Header:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\header.tsx","Header")},22230:(e,a,s)=>{s.d(a,{Header:()=>P});var t=s(60687),r=s(85814),l=s.n(r),d=s(16189),n=s(29523),o=s(43210),i=s(99895),c=s(14952),m=s(13964),f=s(65822),x=s(4780);let u=i.bL,p=i.l9;i.YJ,i.ZL,i.Pb,i.z6,o.forwardRef(({className:e,inset:a,children:s,...r},l)=>(0,t.jsxs)(i.ZP,{ref:l,className:(0,x.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",e),...r,children:[s,(0,t.jsx)(c.A,{className:"ml-auto h-4 w-4"})]})).displayName=i.ZP.displayName,o.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.G5,{ref:s,className:(0,x.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})).displayName=i.G5.displayName;let h=o.forwardRef(({className:e,sideOffset:a=4,...s},r)=>(0,t.jsx)(i.ZL,{children:(0,t.jsx)(i.UC,{ref:r,sideOffset:a,className:(0,x.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));h.displayName=i.UC.displayName;let N=o.forwardRef(({className:e,inset:a,...s},r)=>(0,t.jsx)(i.q7,{ref:r,className:(0,x.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a&&"pl-8",e),...s}));N.displayName=i.q7.displayName,o.forwardRef(({className:e,children:a,checked:s,...r},l)=>(0,t.jsxs)(i.H_,{ref:l,className:(0,x.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(i.VF,{children:(0,t.jsx)(m.A,{className:"h-4 w-4"})})}),a]})).displayName=i.H_.displayName,o.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(i.hN,{ref:r,className:(0,x.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(i.VF,{children:(0,t.jsx)(f.A,{className:"h-2 w-2 fill-current"})})}),a]})).displayName=i.hN.displayName;let j=o.forwardRef(({className:e,inset:a,...s},r)=>(0,t.jsx)(i.JU,{ref:r,className:(0,x.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",e),...s}));j.displayName=i.JU.displayName;let b=o.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.wv,{ref:s,className:(0,x.cn)("-mx-1 my-1 h-px bg-muted",e),...a}));b.displayName=i.wv.displayName;var g=s(11096);let y=o.forwardRef(({className:e,...a},s)=>(0,t.jsx)(g.Root,{ref:s,className:(0,x.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...a}));y.displayName=g.Root.displayName;let v=o.forwardRef(({className:e,...a},s)=>(0,t.jsx)(g.Image,{ref:s,className:(0,x.cn)("aspect-square h-full w-full",e),...a}));v.displayName=g.Image.displayName;let w=o.forwardRef(({className:e,...a},s)=>(0,t.jsx)(g.Fallback,{ref:s,className:(0,x.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...a}));w.displayName=g.Fallback.displayName;var C=s(96834),R=s(5909),A=s(13861),k=s(96474),_=s(58869),z=s(84027),F=s(40083),H=s(12941);let U=[{href:"/tours",label:"Tours"},{href:"/pricing",label:"Pricing"},{href:"/about",label:"About"},{href:"/contact",label:"Contact"}];function P({variant:e="public"}){let a=(0,d.usePathname)(),{user:s,loading:r,signOut:o}=(0,R.A)(),i=async()=>{try{await o()}catch(e){console.error("Error signing out:",e)}};return(0,t.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 h-16 flex items-center justify-between",children:[(0,t.jsxs)(l(),{href:"/",className:"flex items-center space-x-2",children:[(0,t.jsx)(A.A,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("span",{className:"text-2xl font-bold",children:"VirtualRealTour"})]}),"public"===e&&(0,t.jsx)("nav",{className:"hidden md:flex items-center space-x-6",children:U.map(e=>(0,t.jsx)(l(),{href:e.href,className:(0,x.cn)("text-sm font-medium transition-colors hover:text-primary",a===e.href?"text-foreground":"text-muted-foreground"),children:e.label},e.href))}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:["dashboard"===e&&s&&(0,t.jsx)(n.$,{asChild:!0,children:(0,t.jsxs)(l(),{href:"/dashboard/tours/new",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Create Tour"]})}),!r&&(0,t.jsx)(t.Fragment,{children:s?(0,t.jsxs)(u,{children:[(0,t.jsx)(p,{asChild:!0,children:(0,t.jsx)(n.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,t.jsxs)(y,{className:"h-8 w-8",children:[(0,t.jsx)(v,{src:s.user_metadata?.avatar_url,alt:s.user_metadata?.full_name}),(0,t.jsx)(w,{children:(s.user_metadata?.full_name||s.email||"U").split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2)})]})})}),(0,t.jsxs)(h,{className:"w-56",align:"end",forceMount:!0,children:[(0,t.jsx)(j,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium leading-none",children:s.user_metadata?.full_name||"User"}),(0,t.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:s.email}),(0,t.jsx)("div",{className:"flex items-center space-x-2 pt-1",children:(0,t.jsx)(C.E,{variant:"secondary",className:"text-xs",children:"Free Plan"})})]})}),(0,t.jsx)(b,{}),(0,t.jsx)(N,{asChild:!0,children:(0,t.jsxs)(l(),{href:"/dashboard",children:[(0,t.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),(0,t.jsx)(N,{asChild:!0,children:(0,t.jsxs)(l(),{href:"/profile",children:[(0,t.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"Profile"]})}),(0,t.jsx)(N,{asChild:!0,children:(0,t.jsxs)(l(),{href:"/settings",children:[(0,t.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Settings"]})}),(0,t.jsx)(b,{}),(0,t.jsxs)(N,{onClick:i,children:[(0,t.jsx)(F.A,{className:"mr-2 h-4 w-4"}),"Sign out"]})]})]}):(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.$,{variant:"ghost",asChild:!0,children:(0,t.jsx)(l(),{href:"/auth/signin",children:"Sign In"})}),(0,t.jsx)(n.$,{asChild:!0,children:(0,t.jsx)(l(),{href:"/auth/signup",children:"Get Started"})})]})}),(0,t.jsx)(n.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,t.jsx)(H.A,{className:"h-5 w-5"})})]})]})})}},78963:(e,a,s)=>{s.d(a,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>d,aR:()=>n});var t=s(37413),r=s(61120),l=s(10974);let d=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...a}));d.displayName="Card";let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...a}));n.displayName="CardHeader";let o=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("font-semibold leading-none tracking-tight",e),...a}));o.displayName="CardTitle";let i=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...a}));i.displayName="CardDescription";let c=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",e),...a}));c.displayName="CardContent",r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},96834:(e,a,s)=>{s.d(a,{E:()=>n});var t=s(60687);s(43210);var r=s(24224),l=s(4780);let d=(0,r.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:a,...s}){return(0,t.jsx)("div",{className:(0,l.cn)(d({variant:a}),e),...s})}}};