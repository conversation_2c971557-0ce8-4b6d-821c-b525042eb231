(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[630],{2376:(e,t,s)=>{"use strict";s.d(t,{TourViewer:()=>g});var a=s(5155),r=s(2115),i=s(3816),n=s(4932),l=s(3264),o=s(285),d=s(6695),c=s(2178),m=s(5690),h=s(133),x=s(2152),u=s(5273),w=s(7918),f=s(6766);let p=(0,s(5028).default)(()=>s.e(558).then(s.bind(s,7558)).then(e=>({default:e.<PERSON>})),{loadableGenerated:{webpack:()=>[7558]},ssr:!1,loading:()=>(0,a.jsx)("div",{className:"w-full h-full bg-black flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-white text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,a.jsx)("p",{children:"Loading 3D viewer..."})]})})});function b(e){let{imageUrl:t}=e,s=(0,r.useRef)(null),n=(0,i.F)(l.Tap,t);return(0,i.C)(()=>{s.current}),(0,a.jsxs)("mesh",{ref:s,scale:[-1,1,1],children:[(0,a.jsx)("sphereGeometry",{args:[500,60,40]}),(0,a.jsx)("meshBasicMaterial",{map:n,side:l.hsX})]})}function v(){return(0,a.jsxs)("mesh",{children:[(0,a.jsx)("sphereGeometry",{args:[1,32,32]}),(0,a.jsx)("meshBasicMaterial",{color:"#333",wireframe:!0})]})}function j(e){let{imageUrl:t}=e;return(0,a.jsxs)("div",{className:"relative w-full h-full bg-black rounded-lg overflow-hidden",children:[(0,a.jsx)(f.default,{src:t,alt:"360\xb0 Tour Preview",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-white text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"360\xb0 Experience"}),(0,a.jsx)("p",{className:"text-sm opacity-80",children:"Loading immersive view..."})]})})]})}function g(e){let{imageUrl:t,title:s,autoRotate:i=!1,showControls:l=!0}=e,[f,g]=(0,r.useState)(i),[N,k]=(0,r.useState)(!0),[C,R]=(0,r.useState)(!0),y=(0,r.useRef)(null),{isTouch:z,isVRCapable:E}=function(){let[e,t]=(0,r.useState)({type:"desktop",os:"unknown",browser:"unknown",isTouch:!1,isVRCapable:!1,screenSize:{width:0,height:0},orientation:"landscape",pixelRatio:1});return(0,r.useEffect)(()=>{let e=()=>{let e=navigator.userAgent.toLowerCase(),s=window.innerWidth,a=window.innerHeight,r="desktop";s<=768?r="mobile":s<=1024&&(r="tablet");let i="unknown";/iphone|ipad|ipod/.test(e)?i="ios":/android/.test(e)?i="android":/windows/.test(e)?i="windows":/macintosh|mac os x/.test(e)?i="macos":/linux/.test(e)&&(i="linux");let n="unknown";/chrome/.test(e)&&!/edge/.test(e)?n="chrome":/firefox/.test(e)?n="firefox":/safari/.test(e)&&!/chrome/.test(e)?n="safari":/edge/.test(e)?n="edge":/opera/.test(e)&&(n="opera");let l="ontouchstart"in window||navigator.maxTouchPoints>0;t({type:r,os:i,browser:n,isTouch:l,isVRCapable:!!(navigator.getVRDisplays||navigator.xr||window.DeviceOrientationEvent),screenSize:{width:s,height:a},orientation:s>a?"landscape":"portrait",pixelRatio:window.devicePixelRatio||1})};e();let s=()=>{e()},a=()=>{setTimeout(e,100)};return window.addEventListener("resize",s),window.addEventListener("orientationchange",a),()=>{window.removeEventListener("resize",s),window.removeEventListener("orientationchange",a)}},[]),e}();return(0,a.jsxs)("div",{ref:y,className:"relative w-full h-[600px] bg-black rounded-lg overflow-hidden",children:[C?(0,a.jsx)(p,{camera:{position:[0,0,.1],fov:75,near:.1,far:1e3},gl:{antialias:!0,alpha:!1,powerPreference:"high-performance"},onCreated:()=>console.log("3D Canvas created"),onError:()=>{console.error("3D Canvas failed, falling back to 2D"),R(!1)},children:(0,a.jsxs)(r.Suspense,{fallback:(0,a.jsx)(v,{}),children:[(0,a.jsx)(b,{imageUrl:t}),(0,a.jsx)(n.N,{enableZoom:!0,enablePan:!1,enableDamping:!0,dampingFactor:.1,autoRotate:f,autoRotateSpeed:.5,minDistance:.1,maxDistance:10,enableRotate:!0,rotateSpeed:z?.5:1,zoomSpeed:z?.5:1})]})}):(0,a.jsx)(j,{imageUrl:t}),s&&(0,a.jsx)("div",{className:"absolute top-4 left-4 z-10",children:(0,a.jsx)(d.Zp,{className:"bg-black/50 backdrop-blur-sm border-white/20",children:(0,a.jsx)("div",{className:"p-3",children:(0,a.jsx)("h3",{className:"text-white font-medium",children:s})})})}),l&&(0,a.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10",children:(0,a.jsx)(d.Zp,{className:"bg-black/50 backdrop-blur-sm border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-2",children:[(0,a.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>{g(!f)},className:"text-white hover:bg-white/20",children:f?(0,a.jsx)(c.A,{className:"h-4 w-4"}):(0,a.jsx)(m.A,{className:"h-4 w-4"})}),(0,a.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>{},className:"text-white hover:bg-white/20",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>{k(!N)},className:"text-white hover:bg-white/20",children:N?(0,a.jsx)(x.A,{className:"h-4 w-4"}):(0,a.jsx)(u.A,{className:"h-4 w-4"})}),(0,a.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>{if(document.fullscreenElement)document.exitFullscreen();else{var e;null==(e=y.current)||e.requestFullscreen()}},className:"text-white hover:bg-white/20",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})}),E&&(0,a.jsx)(o.$,{size:"sm",variant:"ghost",className:"text-white hover:bg-white/20",children:"VR"})]})})}),z&&C&&(0,a.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 pointer-events-none",children:(0,a.jsx)(d.Zp,{className:"bg-black/50 backdrop-blur-sm border-white/20 animate-pulse",children:(0,a.jsx)("div",{className:"p-4 text-center",children:(0,a.jsx)("p",{className:"text-white text-sm",children:"Drag to look around • Pinch to zoom"})})})}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50 z-0",children:(0,a.jsxs)("div",{className:"text-center text-white",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,a.jsx)("p",{children:"Loading immersive experience..."})]})})]})}},2391:(e,t,s)=>{Promise.resolve().then(s.bind(s,4011)),Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,2376)),Promise.resolve().then(s.bind(s,6033))},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>l});var a=s(5155),r=s(2115),i=s(9434);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...r})});n.displayName="Card";let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",s),...r})});l.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});d.displayName="CardDescription";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",s),...r})});c.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"}},e=>{var t=t=>e(e.s=t);e.O(0,[367,831,413,874,455,594,663,579,33,441,684,358],()=>t(2391)),_N_E=e.O()}]);