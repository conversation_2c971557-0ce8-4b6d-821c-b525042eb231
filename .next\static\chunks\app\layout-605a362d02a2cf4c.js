(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},5569:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,1666,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,9716))},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(2596),a=r(9688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},9716:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>D,A:()=>_});var s=r(5155),a=r(2115),o=r(3057),n=r(2085),i=r(4416),l=r(9434);let d=o.Kq,u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(o.LM,{ref:t,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...a})});u.displayName=o.LM.displayName;let c=(0,n.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=a.forwardRef((e,t)=>{let{className:r,variant:a,...n}=e;return(0,s.jsx)(o.bL,{ref:t,className:(0,l.cn)(c({variant:a}),r),...n})});f.displayName=o.bL.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(o.rc,{ref:t,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...a})}).displayName=o.rc.displayName;let m=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(o.bm,{ref:t,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...a,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})});m.displayName=o.bm.displayName;let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(o.hE,{ref:t,className:(0,l.cn)("text-sm font-semibold",r),...a})});p.displayName=o.hE.displayName;let h=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(o.VY,{ref:t,className:(0,l.cn)("text-sm opacity-90",r),...a})});h.displayName=o.VY.displayName;let v=0,g=new Map,x=e=>{if(g.has(e))return;let t=setTimeout(()=>{g.delete(e),S({type:"REMOVE_TOAST",toastId:e})},1e6);g.set(e,t)},b=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?x(r):e.toasts.forEach(e=>{x(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},y=[],w={toasts:[]};function S(e){w=b(w,e),y.forEach(e=>{e(w)})}function N(e){let{...t}=e,r=(v=(v+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>S({type:"DISMISS_TOAST",toastId:r});return S({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>S({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function I(){let{toasts:e}=function(){let[e,t]=a.useState(w);return a.useEffect(()=>(y.push(t),()=>{let e=y.indexOf(t);e>-1&&y.splice(e,1)}),[e]),{...e,toast:N,dismiss:e=>S({type:"DISMISS_TOAST",toastId:e})}}();return(0,s.jsxs)(d,{children:[e.map(function(e){let{id:t,title:r,description:a,action:o,...n}=e;return(0,s.jsxs)(f,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[r&&(0,s.jsx)(p,{children:r}),a&&(0,s.jsx)(h,{children:a})]}),o,(0,s.jsx)(m,{})]},t)}),(0,s.jsx)(u,{})]})}let j=(0,a.createContext)(void 0);function E(e){let{children:t}=e,[r,o]=(0,a.useState)("system");return(0,a.useEffect)(()=>{let e=localStorage.getItem("theme");e&&o(e)},[]),(0,a.useEffect)(()=>{localStorage.setItem("theme",r);let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===r){let t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(t)}else e.classList.add(r)},[r]),(0,s.jsx)(j.Provider,{value:{theme:r,setTheme:o},children:t})}let A=(0,a.createContext)(void 0);function _(){let e=(0,a.useContext)(A);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}function T(e){let{children:t}=e,[o,n]=(0,a.useState)(null),[i,l]=(0,a.useState)(!0),d="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.yaD0ot54tD5tj5Bz2SMZXgp6x6fzVXsAz2xjvB-bFFo";(0,a.useEffect)(()=>{if(!d){console.warn("Supabase environment variables not configured. Running in demo mode."),l(!1);return}let e=(async()=>{try{let{createClient:e}=await Promise.all([r.e(365),r.e(643)]).then(r.bind(r,2643)),t=e();(async()=>{try{let{data:{user:e}}=await t.auth.getUser();n(e)}catch(e){console.error("Error getting user:",e)}finally{l(!1)}})();let{data:{subscription:s}}=t.auth.onAuthStateChange(async(e,t)=>{var r;n(null!=(r=null==t?void 0:t.user)?r:null),l(!1)});return()=>s.unsubscribe()}catch(e){console.error("Error initializing Supabase client:",e),l(!1)}})();return()=>{e.then(e=>null==e?void 0:e())}},[d]);let u=async()=>{if(!d)return void console.warn("Cannot sign out: Supabase not configured");try{let{createClient:e}=await Promise.all([r.e(365),r.e(643)]).then(r.bind(r,2643)),t=e();await t.auth.signOut()}catch(e){console.error("Error signing out:",e)}};return(0,s.jsx)(A.Provider,{value:{user:o,loading:i,signOut:u},children:t})}function O(e){let{children:t}=e;return(0,s.jsxs)(s.Fragment,{children:[t,(0,s.jsx)(I,{})]})}function D(e){let{children:t}=e;return(0,s.jsx)(E,{children:(0,s.jsx)(T,{children:(0,s.jsx)(O,{children:t})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[258,455,594,441,684,358],()=>t(5569)),_N_E=e.O()}]);