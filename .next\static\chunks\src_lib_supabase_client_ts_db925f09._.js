(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/supabase/client.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@supabase_node-fetch_browser_1d4b9f33.js",
  "static/chunks/node_modules_142a2162._.js",
  "static/chunks/src_lib_supabase_client_ts_5ab78e6c._.js",
  "static/chunks/src_lib_supabase_client_ts_97335fac._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/supabase/client.ts [app-client] (ecmascript)");
    });
});
}}),
}]);