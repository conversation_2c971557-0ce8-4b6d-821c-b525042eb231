(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@supabase/auth-helpers-nextjs/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@supabase_node-fetch_browser_0664e38c.js",
  "static/chunks/node_modules_c00ed20e._.js",
  "static/chunks/node_modules_@supabase_auth-helpers-nextjs_dist_index_97335fac.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@supabase/auth-helpers-nextjs/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);