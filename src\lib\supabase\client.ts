import { createClient as createBrowserClient } from '@supabase/supabase-js';
import { createBrowserClient as createSSRClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// Client-side Supabase client for use in components (legacy)
export const supabase = createBrowserClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// New SSR-compatible client (recommended)
export const createClient = () => createSSRClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Alternative client creation for specific use cases
export function createSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables');
  }

  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
  });
}

// Storage bucket names
export const STORAGE_BUCKETS = {
  MEDIA: 'media',
  AVATARS: 'avatars',
  THUMBNAILS: 'thumbnails',
  TEMP: 'temp',
} as const;

// Helper functions for storage operations
export const storage = {
  /**
   * Upload file to storage bucket
   */
  async uploadFile(
    bucket: string,
    path: string,
    file: File,
    options?: {
      cacheControl?: string;
      contentType?: string;
      upsert?: boolean;
    }
  ) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: options?.cacheControl || '3600',
        contentType: options?.contentType || file.type,
        upsert: options?.upsert || false,
      });

    if (error) {
      throw new Error(`Upload failed: ${error.message}`);
    }

    return data;
  },

  /**
   * Get public URL for a file
   */
  getPublicUrl(bucket: string, path: string) {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  },

  /**
   * Create signed URL for private files
   */
  async createSignedUrl(
    bucket: string,
    path: string,
    expiresIn: number = 3600
  ) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(path, expiresIn);

    if (error) {
      throw new Error(`Failed to create signed URL: ${error.message}`);
    }

    return data.signedUrl;
  },

  /**
   * Delete file from storage
   */
  async deleteFile(bucket: string, path: string) {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) {
      throw new Error(`Delete failed: ${error.message}`);
    }
  },

  /**
   * List files in a bucket
   */
  async listFiles(bucket: string, path?: string) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .list(path);

    if (error) {
      throw new Error(`List failed: ${error.message}`);
    }

    return data;
  },

  /**
   * Move file within storage
   */
  async moveFile(
    bucket: string,
    fromPath: string,
    toPath: string
  ) {
    const { error } = await supabase.storage
      .from(bucket)
      .move(fromPath, toPath);

    if (error) {
      throw new Error(`Move failed: ${error.message}`);
    }
  },

  /**
   * Copy file within storage
   */
  async copyFile(
    bucket: string,
    fromPath: string,
    toPath: string
  ) {
    const { error } = await supabase.storage
      .from(bucket)
      .copy(fromPath, toPath);

    if (error) {
      throw new Error(`Copy failed: ${error.message}`);
    }
  },
};

// Helper functions for database operations
export const db = {
  /**
   * Generic select with error handling
   */
  async select<T>(
    table: string,
    columns?: string,
    filters?: Record<string, any>
  ): Promise<T[]> {
    let query = supabase.from(table).select(columns || '*');

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value);
      });
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Select failed: ${error.message}`);
    }

    return data as T[];
  },

  /**
   * Generic insert with error handling
   */
  async insert<T>(table: string, data: Partial<T>): Promise<T> {
    const { data: result, error } = await supabase
      .from(table)
      .insert(data)
      .select()
      .single();

    if (error) {
      throw new Error(`Insert failed: ${error.message}`);
    }

    return result as T;
  },

  /**
   * Generic update with error handling
   */
  async update<T>(
    table: string,
    id: string,
    data: Partial<T>
  ): Promise<T> {
    const { data: result, error } = await supabase
      .from(table)
      .update(data)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Update failed: ${error.message}`);
    }

    return result as T;
  },

  /**
   * Generic delete with error handling
   */
  async delete(table: string, id: string): Promise<void> {
    const { error } = await supabase
      .from(table)
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Delete failed: ${error.message}`);
    }
  },

  /**
   * Count records in a table
   */
  async count(
    table: string,
    filters?: Record<string, any>
  ): Promise<number> {
    let query = supabase
      .from(table)
      .select('*', { count: 'exact', head: true });

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value);
      });
    }

    const { count, error } = await query;

    if (error) {
      throw new Error(`Count failed: ${error.message}`);
    }

    return count || 0;
  },
};

// Real-time subscription helpers
export const realtime = {
  /**
   * Subscribe to table changes
   */
  subscribeToTable(
    table: string,
    callback: (payload: any) => void,
    filter?: string
  ) {
    const channel = supabase
      .channel(`${table}_changes`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table,
          filter,
        },
        callback
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  },

  /**
   * Subscribe to specific record changes
   */
  subscribeToRecord(
    table: string,
    id: string,
    callback: (payload: any) => void
  ) {
    return this.subscribeToTable(table, callback, `id=eq.${id}`);
  },

  /**
   * Subscribe to user's records
   */
  subscribeToUserRecords(
    table: string,
    userId: string,
    callback: (payload: any) => void
  ) {
    return this.subscribeToTable(table, callback, `user_id=eq.${userId}`);
  },
};

// Auth helpers
export const auth = {
  /**
   * Get current user
   */
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      throw new Error(`Failed to get user: ${error.message}`);
    }

    return user;
  },

  /**
   * Get current session
   */
  async getCurrentSession() {
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      throw new Error(`Failed to get session: ${error.message}`);
    }

    return session;
  },

  /**
   * Sign out user
   */
  async signOut() {
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw new Error(`Sign out failed: ${error.message}`);
    }
  },

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: any) => void) {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(callback);

    return () => {
      subscription.unsubscribe();
    };
  },
};
