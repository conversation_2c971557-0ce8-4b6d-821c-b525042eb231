import { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { PublicLayout } from "@/components/layout/public-layout";
import { TourViewer } from "@/components/3d/tour-viewer";
import { 
  Eye, 
  Heart, 
  Share2, 
  MapPin, 
  Calendar, 
  User, 
  MessageCircle,
  ExternalLink,
  Download
} from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";

// This would normally come from your database
const sampleTour = {
  id: "1",
  title: "Luxury 3-Bedroom Apartment in Victoria Island",
  description: "Experience this stunning luxury apartment featuring modern amenities, breathtaking views of Lagos lagoon, and premium finishes throughout. Perfect for executives and families looking for upscale living in the heart of Lagos.",
  slug: "luxury-apartment-victoria-island",
  category: "Real Estate",
  location: "Victoria Island, Lagos",
  address: "123 Ahmadu Bello Way, Victoria Island, Lagos State",
  price: 2500000,
  currency: "NGN",
  views: 2847,
  likes: 89,
  shares: 12,
  createdAt: "2024-01-15",
  user: {
    id: "user1",
    name: "John Doe",
    avatar: "/avatars/john.jpg",
    company: "Premium Properties Lagos",
  },
  scenes: [
    {
      id: "scene1",
      title: "Living Room",
      imageUrl: "/sample-360/living-room.jpg",
      isStarting: true,
    },
    {
      id: "scene2", 
      title: "Master Bedroom",
      imageUrl: "/sample-360/bedroom.jpg",
      isStarting: false,
    },
    {
      id: "scene3",
      title: "Kitchen",
      imageUrl: "/sample-360/kitchen.jpg", 
      isStarting: false,
    },
  ],
  features: [
    "3 Bedrooms, 2 Bathrooms",
    "Fully Furnished",
    "24/7 Security",
    "Swimming Pool",
    "Gym Access",
    "Parking Space",
    "Generator Backup",
    "High-Speed Internet",
  ],
};

interface TourPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: TourPageProps): Promise<Metadata> {
  // In a real app, you'd fetch the tour data here
  const tour = sampleTour;
  
  if (!tour) {
    return {
      title: "Tour Not Found",
    };
  }

  return {
    title: tour.title,
    description: tour.description,
    openGraph: {
      title: tour.title,
      description: tour.description,
      type: "website",
      images: [
        {
          url: tour.scenes[0]?.imageUrl || "/og-image.jpg",
          width: 1200,
          height: 630,
          alt: tour.title,
        },
      ],
    },
  };
}

export default function TourPage({ params }: TourPageProps) {
  // In a real app, you'd fetch the tour data based on the slug
  const tour = sampleTour;
  
  if (!tour || tour.slug !== params.slug) {
    notFound();
  }

  const currentScene = tour.scenes.find(scene => scene.isStarting) || tour.scenes[0];

  return (
    <PublicLayout>
      <div className="py-8">
        <div className="container mx-auto px-4">
          {/* Tour Viewer */}
          <div className="mb-8">
            <TourViewer
              imageUrl="/sample-360.jpg" // Placeholder 360° image
              title={currentScene.title}
              autoRotate={false}
              showControls={true}
            />
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Tour Info */}
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <Badge variant="secondary" className="mb-2">
                        {tour.category}
                      </Badge>
                      <CardTitle className="text-2xl">{tour.title}</CardTitle>
                      <CardDescription className="flex items-center mt-2">
                        <MapPin className="h-4 w-4 mr-1" />
                        {tour.location}
                      </CardDescription>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary">
                        ₦{tour.price.toLocaleString()}
                      </div>
                      <div className="text-sm text-muted-foreground">per month</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{tour.description}</p>
                  
                  {/* Stats */}
                  <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                    <span className="flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      {tour.views.toLocaleString()} views
                    </span>
                    <span className="flex items-center">
                      <Heart className="h-4 w-4 mr-1" />
                      {tour.likes} likes
                    </span>
                    <span className="flex items-center">
                      <Share2 className="h-4 w-4 mr-1" />
                      {tour.shares} shares
                    </span>
                    <span className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {new Date(tour.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Features */}
              <Card>
                <CardHeader>
                  <CardTitle>Features & Amenities</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2">
                    {tour.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm">
                        <div className="w-2 h-2 bg-primary rounded-full mr-2" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Scenes */}
              <Card>
                <CardHeader>
                  <CardTitle>Tour Scenes</CardTitle>
                  <CardDescription>
                    Explore different areas of this property
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {tour.scenes.map((scene) => (
                      <div
                        key={scene.id}
                        className={`relative aspect-video bg-muted rounded-lg overflow-hidden cursor-pointer border-2 transition-colors ${
                          scene.isStarting ? 'border-primary' : 'border-transparent hover:border-primary/50'
                        }`}
                      >
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Eye className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <div className="absolute bottom-2 left-2 right-2">
                          <div className="bg-black/50 backdrop-blur-sm rounded px-2 py-1">
                            <p className="text-white text-sm font-medium">{scene.title}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Creator Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Created by
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-3 mb-4">
                    <Avatar>
                      <AvatarImage src={tour.user.avatar} alt={tour.user.name} />
                      <AvatarFallback>
                        {tour.user.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{tour.user.name}</p>
                      <p className="text-sm text-muted-foreground">{tour.user.company}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Button className="w-full">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Contact via WhatsApp
                    </Button>
                    <Button variant="outline" className="w-full">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View Profile
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" className="w-full">
                    <Heart className="h-4 w-4 mr-2" />
                    Save to Favorites
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Tour
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Download VR
                  </Button>
                </CardContent>
              </Card>

              {/* Location */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Location
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    {tour.address}
                  </p>
                  <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                    <p className="text-muted-foreground">Map View</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
}
