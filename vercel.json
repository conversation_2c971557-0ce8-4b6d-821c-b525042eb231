{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install --ignore-scripts", "devCommand": "npm run dev", "git": {"deploymentEnabled": {"main": true, "dev": true}}, "github": {"autoAlias": false, "autoJobCancelation": true}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/tours/:slug", "destination": "/tour/:slug"}]}