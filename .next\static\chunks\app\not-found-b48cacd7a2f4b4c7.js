(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{285:(e,r,s)=>{"use strict";s.d(r,{$:()=>c});var t=s(5155),a=s(2115),n=s(9708),d=s(2085),i=s(9434);let l=(0,d.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,r)=>{let{className:s,variant:a,size:d,asChild:c=!1,...o}=e,u=c?n.DX:"button";return(0,t.jsx)(u,{className:(0,i.cn)(l({variant:a,size:d,className:s})),ref:r,...o})});c.displayName="Button"},6499:(e,r,s)=>{Promise.resolve().then(s.bind(s,7245))},6695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>c,Wu:()=>o,ZB:()=>l,Zp:()=>d,aR:()=>i});var t=s(5155),a=s(2115),n=s(9434);let d=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...a})});d.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";let l=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("font-semibold leading-none tracking-tight",s),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",s),...a})});o.displayName="CardContent",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},7245:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var t=s(5155),a=s(6874),n=s.n(a),d=s(285),i=s(6695),l=s(9946);let c=(0,l.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),o=(0,l.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),u=(0,l.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function h(){return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-muted/50 px-4",children:(0,t.jsxs)(i.Zp,{className:"w-full max-w-md text-center",children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100",children:(0,t.jsx)(c,{className:"h-8 w-8 text-red-600"})}),(0,t.jsx)(i.ZB,{className:"text-2xl",children:"Access Restricted"}),(0,t.jsx)(i.BT,{children:"You are not permitted to view this content. This page may be private, require special permissions, or may not exist."})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,t.jsx)("p",{children:"Possible reasons:"}),(0,t.jsxs)("ul",{className:"mt-2 text-left list-disc list-inside space-y-1",children:[(0,t.jsx)("li",{children:"The page requires authentication"}),(0,t.jsx)("li",{children:"You don't have the necessary permissions"}),(0,t.jsx)("li",{children:"The page has been moved or deleted"}),(0,t.jsx)("li",{children:"The URL was typed incorrectly"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,t.jsx)(d.$,{asChild:!0,className:"w-full",children:(0,t.jsxs)(n(),{href:"/",children:[(0,t.jsx)(o,{className:"mr-2 h-4 w-4"}),"Go to Homepage"]})}),(0,t.jsxs)(d.$,{variant:"outline",onClick:()=>window.history.back(),className:"w-full",children:[(0,t.jsx)(u,{className:"mr-2 h-4 w-4"}),"Go Back"]})]}),(0,t.jsx)("div",{className:"pt-4 border-t",children:(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Need help? ",(0,t.jsx)(n(),{href:"/contact",className:"text-primary hover:underline",children:"Contact Support"})]})})]})]})})}},9434:(e,r,s)=>{"use strict";s.d(r,{cn:()=>n});var t=s(2596),a=s(9688);function n(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,a.QP)((0,t.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,455,441,684,358],()=>r(6499)),_N_E=e.O()}]);