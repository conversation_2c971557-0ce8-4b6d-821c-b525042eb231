# VirtualRealTour Platform - Deployment Setup Script (PowerShell)
Write-Host "🚀 Setting up VirtualRealTour Platform for deployment..." -ForegroundColor Green

# Check if we're in a git repository
if (-not (Test-Path ".git")) {
    Write-Host "❌ Error: Not in a git repository" -ForegroundColor Red
    exit 1
}

# Check current branch
$currentBranch = git branch --show-current
Write-Host "📍 Current branch: $currentBranch" -ForegroundColor Cyan

# Create dev branch if it doesn't exist
$devBranchExists = git show-ref --verify --quiet refs/heads/dev
if ($LASTEXITCODE -ne 0) {
    Write-Host "🌿 Creating dev branch..." -ForegroundColor Yellow
    git checkout -b dev
    git push -u origin dev
} else {
    Write-Host "✅ Dev branch already exists" -ForegroundColor Green
}

# Switch to main branch and prepare for deployment
Write-Host "🔄 Switching to main branch..." -ForegroundColor Cyan
git checkout main

# Add all changes
Write-Host "📦 Adding all changes..." -ForegroundColor Yellow
git add .

# Commit changes
Write-Host "💾 Committing changes..." -ForegroundColor Yellow
$commitMessage = @"
VirtualRealTour platform ready for deployment

- ✅ Supabase SSR integration complete
- ✅ 3D tour viewer with fallback
- ✅ Bun for development, npm for build
- ✅ All components functional
- ✅ Build successful
- ✅ Ready for production deployment
"@

git commit -m $commitMessage

# Push to main branch
Write-Host "🚀 Pushing to main branch..." -ForegroundColor Green
git push origin main

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Successfully pushed to main branch" -ForegroundColor Green
    Write-Host ""
    Write-Host "🎯 Next steps:" -ForegroundColor Cyan
    Write-Host "1. Go to vercel.com with your deployment account"
    Write-Host "2. Import this GitHub repository"
    Write-Host "3. Set environment variables:"
    Write-Host "   - NEXT_PUBLIC_SUPABASE_URL=https://maudhokdhyhspfpasnfm.supabase.co"
    Write-Host "   - NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key"
    Write-Host "4. Deploy!"
    Write-Host ""
    Write-Host "🔄 Development workflow:" -ForegroundColor Cyan
    Write-Host "   git checkout dev          # Switch to development"
    Write-Host "   # ... make changes ..."
    Write-Host "   git add . && git commit -m 'Feature: description'"
    Write-Host "   git push origin dev       # Deploy to preview"
    Write-Host "   git checkout main && git merge dev && git push origin main  # Deploy to production"
} else {
    Write-Host "❌ Failed to push to main branch" -ForegroundColor Red
    Write-Host "🔧 Try fixing GitHub connectivity:" -ForegroundColor Yellow
    Write-Host "   git remote set-<NAME_EMAIL>:iwalk-jo/virtualrealtour_platform.git"
    Write-Host "   ssh -T **************"
}
}

# Switch back to dev for development
Write-Host "🔄 Switching back to dev branch for development..." -ForegroundColor Cyan
git checkout dev

Write-Host "✨ Setup complete! Ready for deployment and development." -ForegroundColor Green
