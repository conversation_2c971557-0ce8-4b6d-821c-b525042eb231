# Vercel ignore file - files to exclude from deployment

# Development files
.husky/
.git/
.gitignore
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output

# Build outputs (will be rebuilt on Vercel)
.next/
out/
dist/
build/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Documentation (not needed for deployment)
*.md
!README.md

# Scripts (not needed for deployment)
setup-deployment.sh
setup-deployment.ps1

# Temporary files
*.tmp
*.temp
