(()=>{var e={};e.id=807,e.ids=[807],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62273:(e,t,r)=>{"use strict";r.d(t,{t6:()=>l});var s=r(32190),i=r(45697);let o=i.z.object({NODE_ENV:i.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:i.z.string().url().default("http://localhost:3000"),NEXT_PUBLIC_API_URL:i.z.string().url().optional(),NEXT_PUBLIC_SUPABASE_URL:i.z.string().url().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:i.z.string().min(1).optional(),SUPABASE_SERVICE_ROLE_KEY:i.z.string().min(1).optional(),DATABASE_URL:i.z.string().url().optional(),NEXTAUTH_SECRET:i.z.string().min(32).optional(),NEXTAUTH_URL:i.z.string().url().optional(),NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET:i.z.string().default("media"),SUPABASE_STORAGE_URL:i.z.string().url().optional(),STRIPE_SECRET_KEY:i.z.string().optional(),NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:i.z.string().optional(),STRIPE_WEBHOOK_SECRET:i.z.string().optional(),PAYSTACK_SECRET_KEY:i.z.string().optional(),NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY:i.z.string().optional(),FLUTTERWAVE_SECRET_KEY:i.z.string().optional(),NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY:i.z.string().optional(),WHATSAPP_ACCESS_TOKEN:i.z.string().optional(),WHATSAPP_PHONE_NUMBER_ID:i.z.string().optional(),WHATSAPP_WEBHOOK_VERIFY_TOKEN:i.z.string().optional(),GOOGLE_MAPS_API_KEY:i.z.string().optional(),GOOGLE_ANALYTICS_ID:i.z.string().optional(),GOOGLE_SITE_VERIFICATION:i.z.string().optional(),RESEND_API_KEY:i.z.string().optional(),SENDGRID_API_KEY:i.z.string().optional(),SENTRY_DSN:i.z.string().url().optional(),NEXT_PUBLIC_SENTRY_DSN:i.z.string().url().optional(),NEXT_PUBLIC_ENABLE_VR:i.z.string().transform(e=>"true"===e).default("true"),NEXT_PUBLIC_ENABLE_ANALYTICS:i.z.string().transform(e=>"true"===e).default("true"),NEXT_PUBLIC_ENABLE_PAYMENTS:i.z.string().transform(e=>"true"===e).default("true"),NEXT_PUBLIC_ENABLE_WHATSAPP:i.z.string().transform(e=>"true"===e).default("true"),UPSTASH_REDIS_REST_URL:i.z.string().url().optional(),UPSTASH_REDIS_REST_TOKEN:i.z.string().optional(),MAX_FILE_SIZE:i.z.string().default("100MB"),ALLOWED_FILE_TYPES:i.z.string().default("image/jpeg,image/png,image/webp,video/mp4,video/webm"),FREE_PLAN_TOUR_LIMIT:i.z.string().transform(Number).default("3"),PRO_PLAN_TOUR_LIMIT:i.z.string().transform(Number).default("50"),ENTERPRISE_PLAN_TOUR_LIMIT:i.z.string().default("unlimited")}),n=function(){try{return o.parse(process.env)}catch(e){if(e instanceof i.z.ZodError){let t=e.errors.filter(e=>"invalid_type"===e.code&&"undefined"===e.received).map(e=>e.path.join(".")),r=e.errors.filter(e=>"invalid_type"!==e.code||"undefined"!==e.received).map(e=>`${e.path.join(".")}: ${e.message}`),s="Environment validation failed:\n";throw t.length>0&&(s+=`
Missing required variables:
${t.map(e=>`  - ${e}`).join("\n")}`),r.length>0&&(s+=`
Invalid variables:
${r.map(e=>`  - ${e}`).join("\n")}`),Error(s)}throw e}}();n.NODE_ENV;let a="production"===n.NODE_ENV;if(n.NODE_ENV,n.NEXT_PUBLIC_SUPABASE_URL&&n.NEXT_PUBLIC_SUPABASE_ANON_KEY,n.NEXT_PUBLIC_SUPABASE_URL,n.NEXT_PUBLIC_SUPABASE_ANON_KEY,n.SUPABASE_SERVICE_ROLE_KEY,n.NEXT_PUBLIC_ENABLE_VR,n.NEXT_PUBLIC_ENABLE_ANALYTICS,n.NEXT_PUBLIC_ENABLE_PAYMENTS,n.NEXT_PUBLIC_ENABLE_WHATSAPP,n.STRIPE_SECRET_KEY&&n.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,n.STRIPE_SECRET_KEY,n.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,n.STRIPE_WEBHOOK_SECRET,n.PAYSTACK_SECRET_KEY&&n.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,n.PAYSTACK_SECRET_KEY,n.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,n.FLUTTERWAVE_SECRET_KEY&&n.NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY,n.FLUTTERWAVE_SECRET_KEY,n.NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY,n.RESEND_API_KEY,n.RESEND_API_KEY,n.SENDGRID_API_KEY,n.SENDGRID_API_KEY,n.WHATSAPP_ACCESS_TOKEN&&n.WHATSAPP_PHONE_NUMBER_ID,n.WHATSAPP_ACCESS_TOKEN,n.WHATSAPP_PHONE_NUMBER_ID,n.WHATSAPP_WEBHOOK_VERIFY_TOKEN,n.GOOGLE_MAPS_API_KEY,n.GOOGLE_MAPS_API_KEY,n.GOOGLE_ANALYTICS_ID,n.GOOGLE_ANALYTICS_ID,n.SENTRY_DSN,n.SENTRY_DSN,n.NEXT_PUBLIC_SENTRY_DSN,n.UPSTASH_REDIS_REST_URL&&n.UPSTASH_REDIS_REST_TOKEN,n.UPSTASH_REDIS_REST_URL,n.UPSTASH_REDIS_REST_TOKEN,n.MAX_FILE_SIZE,n.ALLOWED_FILE_TYPES.split(","),n.FREE_PLAN_TOUR_LIMIT,n.PRO_PLAN_TOUR_LIMIT,"unlimited"===n.ENTERPRISE_PLAN_TOUR_LIMIT||n.ENTERPRISE_PLAN_TOUR_LIMIT,a){let e=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","SUPABASE_SERVICE_ROLE_KEY"].filter(e=>!n[e]);if(e.length>0)throw Error(`Critical environment variables missing in production: ${e.join(", ")}`)}class E extends Error{constructor(e,t,r,s){super(e),this.status=t,this.code=r,this.details=s,this.name="APIError"}}class _{constructor(e){this.defaultTimeout=3e4,this.baseURL=e||n.NEXT_PUBLIC_API_URL||"/api"}async request(e,t={}){let{timeout:r=this.defaultTimeout,...s}=t,i=`${this.baseURL}${e.startsWith("/")?"":"/"}${e}`,o=new AbortController,n=setTimeout(()=>o.abort(),r);try{let e=await fetch(i,{...s,signal:o.signal,headers:{"Content-Type":"application/json",...s.headers}});clearTimeout(n);let t=await e.json();if(!e.ok)throw new E(t.error?.message||"Request failed",e.status,t.error?.code,t.error?.details);return t}catch(e){if(clearTimeout(n),e instanceof E)throw e;if(e instanceof Error){if("AbortError"===e.name)throw new E("Request timeout",408);throw new E(e.message,0)}throw new E("Unknown error occurred",0)}}async get(e,t){return this.request(e,{...t,method:"GET"})}async post(e,t,r){return this.request(e,{...r,method:"POST",body:t?JSON.stringify(t):void 0})}async put(e,t,r){return this.request(e,{...r,method:"PUT",body:t?JSON.stringify(t):void 0})}async patch(e,t,r){return this.request(e,{...r,method:"PATCH",body:t?JSON.stringify(t):void 0})}async delete(e,t){return this.request(e,{...t,method:"DELETE"})}async upload(e,t,r){let{onProgress:s,...i}=r||{};return new Promise((r,o)=>{let n=new XMLHttpRequest;n.upload.addEventListener("progress",e=>{e.lengthComputable&&s&&s(Math.round(e.loaded/e.total*100))}),n.addEventListener("load",()=>{try{let e=JSON.parse(n.responseText);n.status>=200&&n.status<300?r(e):o(new E(e.error?.message||"Upload failed",n.status,e.error?.code,e.error?.details))}catch(e){o(new E("Invalid response format",n.status))}}),n.addEventListener("error",()=>{o(new E("Network error",0))}),n.addEventListener("timeout",()=>{o(new E("Upload timeout",408))});let a=`${this.baseURL}${e.startsWith("/")?"":"/"}${e}`;n.open("POST",a),n.timeout=i.timeout||this.defaultTimeout,i.headers&&Object.entries(i.headers).forEach(([e,t])=>{"content-type"!==e.toLowerCase()&&n.setRequestHeader(e,t)}),n.send(t)})}}function u(e,t=500,r,i){return s.NextResponse.json({success:!1,error:{code:r||function(e){switch(e){case 400:return"BAD_REQUEST";case 401:return"UNAUTHORIZED";case 403:return"FORBIDDEN";case 404:return"NOT_FOUND";case 409:return"CONFLICT";case 422:return"VALIDATION_ERROR";case 429:return"RATE_LIMIT_EXCEEDED";case 500:return"INTERNAL_ERROR";case 502:return"BAD_GATEWAY";case 503:return"SERVICE_UNAVAILABLE";case 504:return"GATEWAY_TIMEOUT";default:return"UNKNOWN_ERROR"}}(t),message:e,details:i}},{status:t})}function l(e){if(console.error("API Error:",e),e instanceof i.G)return u("Validation failed",400,"VALIDATION_ERROR",e.errors);if(e instanceof E)return u(e.message,e.status,e.code,e.details);if(e instanceof Error){if(e.message.includes("duplicate key"))return u("Resource already exists",409,"DUPLICATE_RESOURCE");if(e.message.includes("foreign key"))return u("Referenced resource not found",400,"INVALID_REFERENCE");if(e.message.includes("not found"))return u("Resource not found",404,"NOT_FOUND")}return u("Internal server error",500,"INTERNAL_ERROR")}new _},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64104:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>R,routeModule:()=>p,serverHooks:()=>N,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>I});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>A});var i=r(96559),o=r(48088),n=r(37719),a=r(32190),E=r(61246),_=r(44999),u=r(45697),l=r(62273);async function T(){let e=await (0,_.UL)();return(0,E.createServerClient)("https://maudhokdhyhspfpasnfm.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1hdWRob2tkaHloc3BmcGFzbmZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjgwMDQsImV4cCI6MjA2NDc0NDAwNH0.yaD0ot54tD5tj5Bz2SMZXgp6x6fzVXsAz2xjvB-bFFo",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:s})=>e.set(t,r,s))}catch{}}}})}let c=u.z.object({title:u.z.string().min(1).max(200),description:u.z.string().optional(),category:u.z.string().optional(),location:u.z.string().optional(),address:u.z.string().optional(),price:u.z.number().optional(),currency:u.z.string().default("NGN"),is_public:u.z.boolean().default(!0),password_protected:u.z.boolean().default(!1),password:u.z.string().optional()});async function d(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"20"),i=t.get("category"),o="true"===t.get("featured"),n=t.get("search"),E=(await T()).from("tours").select(`
        *,
        users:user_id (
          full_name,
          avatar_url,
          company
        ),
        scenes:scenes (
          id,
          title,
          thumbnail_url
        )
      `).eq("status","published").eq("is_public",!0);i&&(E=E.eq("category",i)),o&&(E=E.eq("is_featured",!0)),n&&(E=E.or(`title.ilike.%${n}%,description.ilike.%${n}%`));let _=(r-1)*s,{data:u,error:l,count:c}=await E.order("created_at",{ascending:!1}).range(_,_+s-1);if(l)throw l;return a.NextResponse.json({success:!0,data:{tours:u,pagination:{page:r,limit:s,total:c||0,totalPages:Math.ceil((c||0)/s)}}})}catch(e){return(0,l.t6)(e)}}async function A(e){try{let t=await T(),{data:{user:s},error:i}=await t.auth.getUser();if(i||!s)return a.NextResponse.json({success:!1,error:{message:"Authentication required"}},{status:401});let o=await e.json(),n=c.parse(o),E=n.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""),{data:_,error:u}=await t.rpc("generate_unique_slug",{base_slug:E});if(u)throw u;let l=null;if(n.password_protected&&n.password){let e=await r.e(663).then(r.bind(r,85663));l=await e.hash(n.password,10)}let{data:d,error:A}=await t.from("tours").insert({...n,user_id:s.id,slug:_,password_hash:l,status:"draft"}).select().single();if(A)throw A;return a.NextResponse.json({success:!0,data:d},{status:201})}catch(e){if(e instanceof u.z.ZodError)return a.NextResponse.json({success:!1,error:{message:"Validation failed",details:e.errors}},{status:400});return(0,l.t6)(e)}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/tours/route",pathname:"/api/tours",filename:"route",bundlePath:"app/api/tours/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\api\\tours\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:S,workUnitAsyncStorage:I,serverHooks:N}=p;function R(){return(0,n.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:I})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,198,580,82],()=>r(64104));module.exports=s})();