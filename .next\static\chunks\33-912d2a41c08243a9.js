"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[33],{285:(e,t,a)=>{a.d(t,{$:()=>l});var s=a(5155),r=a(2115),n=a(9708),o=a(2085),i=a(9434);let d=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=r.forwardRef((e,t)=>{let{className:a,variant:r,size:o,asChild:l=!1,...c}=e,u=l?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(d({variant:r,size:o,className:a})),ref:t,...c})});l.displayName="Button"},6033:(e,t,a)=>{a.d(t,{Header:()=>z});var s=a(5155),r=a(6874),n=a.n(r),o=a(5695),i=a(285),d=a(2115),l=a(1071),c=a(3052),u=a(5196),f=a(9428),m=a(9434);let h=l.bL,p=l.l9;l.YJ,l.ZL,l.Pb,l.z6,d.forwardRef((e,t)=>{let{className:a,inset:r,children:n,...o}=e;return(0,s.jsxs)(l.ZP,{ref:t,className:(0,m.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",a),...o,children:[n,(0,s.jsx)(c.A,{className:"ml-auto h-4 w-4"})]})}).displayName=l.ZP.displayName,d.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.G5,{ref:t,className:(0,m.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})}).displayName=l.G5.displayName;let x=d.forwardRef((e,t)=>{let{className:a,sideOffset:r=4,...n}=e;return(0,s.jsx)(l.ZL,{children:(0,s.jsx)(l.UC,{ref:t,sideOffset:r,className:(0,m.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})})});x.displayName=l.UC.displayName;let g=d.forwardRef((e,t)=>{let{className:a,inset:r,...n}=e;return(0,s.jsx)(l.q7,{ref:t,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",a),...n})});g.displayName=l.q7.displayName,d.forwardRef((e,t)=>{let{className:a,children:r,checked:n,...o}=e;return(0,s.jsxs)(l.H_,{ref:t,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:n,...o,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(u.A,{className:"h-4 w-4"})})}),r]})}).displayName=l.H_.displayName,d.forwardRef((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsxs)(l.hN,{ref:t,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(f.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=l.hN.displayName;let v=d.forwardRef((e,t)=>{let{className:a,inset:r,...n}=e;return(0,s.jsx)(l.JU,{ref:t,className:(0,m.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",a),...n})});v.displayName=l.JU.displayName;let b=d.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.wv,{ref:t,className:(0,m.cn)("-mx-1 my-1 h-px bg-muted",a),...r})});b.displayName=l.wv.displayName;var y=a(4011);let j=d.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(y.Root,{ref:t,className:(0,m.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...r})});j.displayName=y.Root.displayName;let N=d.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(y.Image,{ref:t,className:(0,m.cn)("aspect-square h-full w-full",a),...r})});N.displayName=y.Image.displayName;let w=d.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(y.Fallback,{ref:t,className:(0,m.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...r})});w.displayName=y.Fallback.displayName;var A=a(6126),S=a(9716),I=a(2657),E=a(4616),_=a(1007),R=a(381),k=a(4835),C=a(4783);let T=[{href:"/tours",label:"Tours"},{href:"/pricing",label:"Pricing"},{href:"/about",label:"About"},{href:"/contact",label:"Contact"}];function z(e){var t,a,r,d;let{variant:l="public"}=e,c=(0,o.usePathname)(),{user:u,loading:f,signOut:y}=(0,S.A)(),z=async()=>{try{await y()}catch(e){console.error("Error signing out:",e)}};return(0,s.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 h-16 flex items-center justify-between",children:[(0,s.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,s.jsx)(I.A,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("span",{className:"text-2xl font-bold",children:"VirtualRealTour"})]}),"public"===l&&(0,s.jsx)("nav",{className:"hidden md:flex items-center space-x-6",children:T.map(e=>(0,s.jsx)(n(),{href:e.href,className:(0,m.cn)("text-sm font-medium transition-colors hover:text-primary",c===e.href?"text-foreground":"text-muted-foreground"),children:e.label},e.href))}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:["dashboard"===l&&u&&(0,s.jsx)(i.$,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/dashboard/tours/new",children:[(0,s.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Create Tour"]})}),!f&&(0,s.jsx)(s.Fragment,{children:u?(0,s.jsxs)(h,{children:[(0,s.jsx)(p,{asChild:!0,children:(0,s.jsx)(i.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,s.jsxs)(j,{className:"h-8 w-8",children:[(0,s.jsx)(N,{src:null==(t=u.user_metadata)?void 0:t.avatar_url,alt:null==(a=u.user_metadata)?void 0:a.full_name}),(0,s.jsx)(w,{children:((null==(r=u.user_metadata)?void 0:r.full_name)||u.email||"U").split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2)})]})})}),(0,s.jsxs)(x,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsx)(v,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:(null==(d=u.user_metadata)?void 0:d.full_name)||"User"}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:u.email}),(0,s.jsx)("div",{className:"flex items-center space-x-2 pt-1",children:(0,s.jsx)(A.E,{variant:"secondary",className:"text-xs",children:"Free Plan"})})]})}),(0,s.jsx)(b,{}),(0,s.jsx)(g,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/dashboard",children:[(0,s.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),(0,s.jsx)(g,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/profile",children:[(0,s.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"Profile"]})}),(0,s.jsx)(g,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/settings",children:[(0,s.jsx)(R.A,{className:"mr-2 h-4 w-4"}),"Settings"]})}),(0,s.jsx)(b,{}),(0,s.jsxs)(g,{onClick:z,children:[(0,s.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"Sign out"]})]})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.$,{variant:"ghost",asChild:!0,children:(0,s.jsx)(n(),{href:"/auth/signin",children:"Sign In"})}),(0,s.jsx)(i.$,{asChild:!0,children:(0,s.jsx)(n(),{href:"/auth/signup",children:"Get Started"})})]})}),(0,s.jsx)(i.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,s.jsx)(C.A,{className:"h-5 w-5"})})]})]})})}},6126:(e,t,a)=>{a.d(t,{E:()=>i});var s=a(5155);a(2115);var r=a(2085),n=a(9434);let o=(0,r.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{className:(0,n.cn)(o({variant:a}),t),...r})}},9434:(e,t,a)=>{a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},9716:(e,t,a)=>{a.d(t,{Providers:()=>C,A:()=>_});var s=a(5155),r=a(2115),n=a(3057),o=a(2085),i=a(4416),d=a(9434);let l=n.Kq,c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.LM,{ref:t,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...r})});c.displayName=n.LM.displayName;let u=(0,o.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=r.forwardRef((e,t)=>{let{className:a,variant:r,...o}=e;return(0,s.jsx)(n.bL,{ref:t,className:(0,d.cn)(u({variant:r}),a),...o})});f.displayName=n.bL.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.rc,{ref:t,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",a),...r})}).displayName=n.rc.displayName;let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.bm,{ref:t,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",a),"toast-close":"",...r,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})});m.displayName=n.bm.displayName;let h=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.hE,{ref:t,className:(0,d.cn)("text-sm font-semibold",a),...r})});h.displayName=n.hE.displayName;let p=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.VY,{ref:t,className:(0,d.cn)("text-sm opacity-90",a),...r})});p.displayName=n.VY.displayName;let x=0,g=new Map,v=e=>{if(g.has(e))return;let t=setTimeout(()=>{g.delete(e),N({type:"REMOVE_TOAST",toastId:e})},1e6);g.set(e,t)},b=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?v(a):e.toasts.forEach(e=>{v(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},y=[],j={toasts:[]};function N(e){j=b(j,e),y.forEach(e=>{e(j)})}function w(e){let{...t}=e,a=(x=(x+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>N({type:"DISMISS_TOAST",toastId:a});return N({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>N({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function A(){let{toasts:e}=function(){let[e,t]=r.useState(j);return r.useEffect(()=>(y.push(t),()=>{let e=y.indexOf(t);e>-1&&y.splice(e,1)}),[e]),{...e,toast:w,dismiss:e=>N({type:"DISMISS_TOAST",toastId:e})}}();return(0,s.jsxs)(l,{children:[e.map(function(e){let{id:t,title:a,description:r,action:n,...o}=e;return(0,s.jsxs)(f,{...o,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[a&&(0,s.jsx)(h,{children:a}),r&&(0,s.jsx)(p,{children:r})]}),n,(0,s.jsx)(m,{})]},t)}),(0,s.jsx)(c,{})]})}let S=(0,r.createContext)(void 0);function I(e){let{children:t}=e,[a,n]=(0,r.useState)("system");return(0,r.useEffect)(()=>{let e=localStorage.getItem("theme");e&&n(e)},[]),(0,r.useEffect)(()=>{localStorage.setItem("theme",a);let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===a){let t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(t)}else e.classList.add(a)},[a]),(0,s.jsx)(S.Provider,{value:{theme:a,setTheme:n},children:t})}let E=(0,r.createContext)(void 0);function _(){let e=(0,r.useContext)(E);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}function R(e){let{children:t}=e,[n,o]=(0,r.useState)(null),[i,d]=(0,r.useState)(!0),l="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.yaD0ot54tD5tj5Bz2SMZXgp6x6fzVXsAz2xjvB-bFFo";(0,r.useEffect)(()=>{if(!l){console.warn("Supabase environment variables not configured. Running in demo mode."),d(!1);return}let e=(async()=>{try{let{createClient:e}=await Promise.all([a.e(365),a.e(643)]).then(a.bind(a,2643)),t=e();(async()=>{try{let{data:{user:e}}=await t.auth.getUser();o(e)}catch(e){console.error("Error getting user:",e)}finally{d(!1)}})();let{data:{subscription:s}}=t.auth.onAuthStateChange(async(e,t)=>{var a;o(null!=(a=null==t?void 0:t.user)?a:null),d(!1)});return()=>s.unsubscribe()}catch(e){console.error("Error initializing Supabase client:",e),d(!1)}})();return()=>{e.then(e=>null==e?void 0:e())}},[l]);let c=async()=>{if(!l)return void console.warn("Cannot sign out: Supabase not configured");try{let{createClient:e}=await Promise.all([a.e(365),a.e(643)]).then(a.bind(a,2643)),t=e();await t.auth.signOut()}catch(e){console.error("Error signing out:",e)}};return(0,s.jsx)(E.Provider,{value:{user:n,loading:i,signOut:c},children:t})}function k(e){let{children:t}=e;return(0,s.jsxs)(s.Fragment,{children:[t,(0,s.jsx)(A,{})]})}function C(e){let{children:t}=e;return(0,s.jsx)(I,{children:(0,s.jsx)(R,{children:(0,s.jsx)(k,{children:t})})})}}}]);