# API Specifications - VirtualRealTour Platform

## Overview
RESTful API design with Next.js App Router API routes, following OpenAPI 3.0 specifications for comprehensive documentation and type safety.

## Base Configuration

### Base URL
- **Development**: `http://localhost:3000/api`
- **Production**: `https://virtualrealtour.vercel.app/api`

### Authentication
- **Type**: <PERSON><PERSON> (JWT from Supabase Auth)
- **Header**: `Authorization: Bearer <token>`

### Response Format
```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}
```

## Authentication Endpoints

### POST /api/auth/signup
Register a new user account
```typescript
// Request
interface SignupRequest {
  email: string;
  password: string;
  fullName: string;
  phone?: string;
  company?: string;
}

// Response
interface SignupResponse {
  user: User;
  session: Session;
}
```

### POST /api/auth/signin
Authenticate user login
```typescript
// Request
interface SigninRequest {
  email: string;
  password: string;
}

// Response
interface SigninResponse {
  user: User;
  session: Session;
}
```

### POST /api/auth/signout
Sign out current user
```typescript
// Response
interface SignoutResponse {
  message: string;
}
```

### POST /api/auth/refresh
Refresh authentication token
```typescript
// Response
interface RefreshResponse {
  session: Session;
}
```

## User Management Endpoints

### GET /api/users/profile
Get current user profile
```typescript
// Response
interface UserProfile {
  id: string;
  email: string;
  fullName: string;
  avatarUrl?: string;
  phone?: string;
  company?: string;
  website?: string;
  bio?: string;
  location?: string;
  subscriptionTier: string;
  subscriptionStatus: string;
  totalTours: number;
  totalViews: number;
  isVerified: boolean;
  preferences: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}
```

### PUT /api/users/profile
Update user profile
```typescript
// Request
interface UpdateProfileRequest {
  fullName?: string;
  phone?: string;
  company?: string;
  website?: string;
  bio?: string;
  location?: string;
  preferences?: Record<string, any>;
}

// Response
interface UpdateProfileResponse {
  user: UserProfile;
}
```

### POST /api/users/avatar
Upload user avatar
```typescript
// Request: FormData with 'avatar' file field

// Response
interface AvatarUploadResponse {
  avatarUrl: string;
}
```

## Tour Management Endpoints

### GET /api/tours
Get tours with filtering and pagination
```typescript
// Query Parameters
interface ToursQuery {
  page?: number;
  limit?: number;
  category?: string;
  status?: 'draft' | 'published' | 'archived';
  featured?: boolean;
  search?: string;
  userId?: string;
  location?: string;
  sortBy?: 'created_at' | 'updated_at' | 'views' | 'likes';
  sortOrder?: 'asc' | 'desc';
}

// Response
interface ToursResponse {
  tours: Tour[];
  pagination: PaginationMeta;
}
```

### GET /api/tours/[id]
Get specific tour details
```typescript
// Response
interface TourResponse {
  tour: Tour & {
    scenes: Scene[];
    user: Pick<User, 'id' | 'fullName' | 'avatarUrl'>;
    analytics: TourAnalytics;
  };
}
```

### POST /api/tours
Create new tour
```typescript
// Request
interface CreateTourRequest {
  title: string;
  description?: string;
  category: string;
  tags?: string[];
  location?: string;
  latitude?: number;
  longitude?: number;
  address?: string;
  price?: number;
  currency?: string;
  visibility?: 'public' | 'private' | 'unlisted';
  settings?: Record<string, any>;
}

// Response
interface CreateTourResponse {
  tour: Tour;
}
```

### PUT /api/tours/[id]
Update existing tour
```typescript
// Request
interface UpdateTourRequest {
  title?: string;
  description?: string;
  category?: string;
  tags?: string[];
  location?: string;
  latitude?: number;
  longitude?: number;
  address?: string;
  price?: number;
  currency?: string;
  status?: 'draft' | 'published' | 'archived';
  visibility?: 'public' | 'private' | 'unlisted';
  settings?: Record<string, any>;
}

// Response
interface UpdateTourResponse {
  tour: Tour;
}
```

### DELETE /api/tours/[id]
Delete tour
```typescript
// Response
interface DeleteTourResponse {
  message: string;
}
```

### POST /api/tours/[id]/publish
Publish tour
```typescript
// Response
interface PublishTourResponse {
  tour: Tour;
}
```

### POST /api/tours/[id]/like
Like/unlike tour
```typescript
// Response
interface LikeTourResponse {
  liked: boolean;
  totalLikes: number;
}
```

### POST /api/tours/[id]/share
Track tour share
```typescript
// Request
interface ShareTourRequest {
  platform: 'whatsapp' | 'facebook' | 'twitter' | 'email' | 'link';
}

// Response
interface ShareTourResponse {
  shareUrl: string;
  totalShares: number;
}
```

## Scene Management Endpoints

### GET /api/tours/[tourId]/scenes
Get scenes for a tour
```typescript
// Response
interface ScenesResponse {
  scenes: Scene[];
}
```

### POST /api/tours/[tourId]/scenes
Create new scene
```typescript
// Request
interface CreateSceneRequest {
  title: string;
  description?: string;
  mediaId: string;
  orderIndex: number;
  isStartingScene?: boolean;
  position?: { x: number; y: number; z: number };
  rotation?: { x: number; y: number; z: number };
  settings?: Record<string, any>;
}

// Response
interface CreateSceneResponse {
  scene: Scene;
}
```

### PUT /api/scenes/[id]
Update scene
```typescript
// Request
interface UpdateSceneRequest {
  title?: string;
  description?: string;
  orderIndex?: number;
  isStartingScene?: boolean;
  position?: { x: number; y: number; z: number };
  rotation?: { x: number; y: number; z: number };
  settings?: Record<string, any>;
}

// Response
interface UpdateSceneResponse {
  scene: Scene;
}
```

### DELETE /api/scenes/[id]
Delete scene
```typescript
// Response
interface DeleteSceneResponse {
  message: string;
}
```

## Hotspot Management Endpoints

### GET /api/scenes/[sceneId]/hotspots
Get hotspots for a scene
```typescript
// Response
interface HotspotsResponse {
  hotspots: Hotspot[];
}
```

### POST /api/scenes/[sceneId]/hotspots
Create new hotspot
```typescript
// Request
interface CreateHotspotRequest {
  type: 'navigation' | 'info' | 'media' | 'link' | 'whatsapp' | 'product';
  title?: string;
  description?: string;
  position: { x: number; y: number; z: number };
  rotation?: { x: number; y: number; z: number };
  scale?: { x: number; y: number; z: number };
  targetSceneId?: string;
  targetUrl?: string;
  mediaId?: string;
  whatsappNumber?: string;
  whatsappMessage?: string;
  productData?: Record<string, any>;
  styleConfig?: Record<string, any>;
  animationConfig?: Record<string, any>;
}

// Response
interface CreateHotspotResponse {
  hotspot: Hotspot;
}
```

### PUT /api/hotspots/[id]
Update hotspot
```typescript
// Request: Same as CreateHotspotRequest but all fields optional

// Response
interface UpdateHotspotResponse {
  hotspot: Hotspot;
}
```

### DELETE /api/hotspots/[id]
Delete hotspot
```typescript
// Response
interface DeleteHotspotResponse {
  message: string;
}
```

## Media Management Endpoints

### POST /api/media/upload
Upload media file
```typescript
// Request: FormData with 'file' field and metadata

// Response
interface MediaUploadResponse {
  media: Media;
  uploadUrl?: string; // For direct upload to storage
}
```

### GET /api/media
Get user's media files
```typescript
// Query Parameters
interface MediaQuery {
  page?: number;
  limit?: number;
  type?: 'image_360' | 'video_360' | 'audio' | 'image' | 'video';
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  search?: string;
}

// Response
interface MediaResponse {
  media: Media[];
  pagination: PaginationMeta;
}
```

### GET /api/media/[id]
Get specific media file
```typescript
// Response
interface MediaDetailResponse {
  media: Media;
}
```

### PUT /api/media/[id]
Update media metadata
```typescript
// Request
interface UpdateMediaRequest {
  filename?: string;
  tags?: string[];
  altText?: string;
  metadata?: Record<string, any>;
}

// Response
interface UpdateMediaResponse {
  media: Media;
}
```

### DELETE /api/media/[id]
Delete media file
```typescript
// Response
interface DeleteMediaResponse {
  message: string;
}
```

### POST /api/media/[id]/process
Trigger media processing
```typescript
// Response
interface ProcessMediaResponse {
  processingId: string;
  status: string;
}
```

## Analytics Endpoints

### GET /api/analytics/tours/[id]
Get tour analytics
```typescript
// Query Parameters
interface AnalyticsQuery {
  startDate?: string;
  endDate?: string;
  granularity?: 'hour' | 'day' | 'week' | 'month';
}

// Response
interface TourAnalyticsResponse {
  views: AnalyticsData[];
  interactions: AnalyticsData[];
  demographics: DemographicsData;
  devices: DeviceData[];
  referrers: ReferrerData[];
}
```

### GET /api/analytics/dashboard
Get dashboard analytics
```typescript
// Response
interface DashboardAnalyticsResponse {
  totalTours: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  recentViews: AnalyticsData[];
  topTours: TourAnalytics[];
  userGrowth: AnalyticsData[];
}
```

### POST /api/analytics/track
Track user interaction
```typescript
// Request
interface TrackEventRequest {
  tourId?: string;
  sceneId?: string;
  hotspotId?: string;
  eventType: string;
  eventData?: Record<string, any>;
}

// Response
interface TrackEventResponse {
  tracked: boolean;
}
```

## Error Codes

### Authentication Errors
- `AUTH_001`: Invalid credentials
- `AUTH_002`: Token expired
- `AUTH_003`: Insufficient permissions
- `AUTH_004`: Account not verified

### Validation Errors
- `VAL_001`: Missing required fields
- `VAL_002`: Invalid field format
- `VAL_003`: Field value out of range
- `VAL_004`: Duplicate value

### Resource Errors
- `RES_001`: Resource not found
- `RES_002`: Resource already exists
- `RES_003`: Resource limit exceeded
- `RES_004`: Resource access denied

### System Errors
- `SYS_001`: Internal server error
- `SYS_002`: Database connection error
- `SYS_003`: External service unavailable
- `SYS_004`: Rate limit exceeded

## Rate Limiting

### Limits by Endpoint Type
- **Authentication**: 5 requests per minute
- **File Upload**: 10 requests per hour
- **Analytics**: 100 requests per hour
- **General API**: 1000 requests per hour

### Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

This API specification provides a comprehensive foundation for building the VirtualRealTour platform with proper type safety, error handling, and scalability considerations.
