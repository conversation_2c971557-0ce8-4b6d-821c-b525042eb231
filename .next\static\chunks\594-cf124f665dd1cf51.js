"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[594],{2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3057:(e,t,n)=>{n.d(t,{rc:()=>en,bm:()=>er,VY:()=>et,Kq:()=>Z,bL:()=>Q,hE:()=>ee,LM:()=>J});var r=n(2115),o=n(7650),i=n(5185),a=n(6101),s=n(7328),l=n(6081),u=n(9178),c=n(4378),d=n(8905),f=n(3655),p=n(9033),v=n(5845),m=n(2712),w=n(5155),y=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),E=r.forwardRef((e,t)=>(0,w.jsx)(f.sG.span,{...e,ref:t,style:{...y,...e.style}}));E.displayName="VisuallyHidden";var h="ToastProvider",[b,g,x]=(0,s.N)("Toast"),[T,C]=(0,l.A)("Toast",[x]),[N,R]=T(h),P=e=>{let{__scopeToast:t,label:n="Notification",duration:o=5e3,swipeDirection:i="right",swipeThreshold:a=50,children:s}=e,[l,u]=r.useState(null),[c,d]=r.useState(0),f=r.useRef(!1),p=r.useRef(!1);return n.trim()||console.error("Invalid prop `label` supplied to `".concat(h,"`. Expected non-empty `string`.")),(0,w.jsx)(b.Provider,{scope:t,children:(0,w.jsx)(N,{scope:t,label:n,duration:o,swipeDirection:i,swipeThreshold:a,toastCount:c,viewport:l,onViewportChange:u,onToastAdd:r.useCallback(()=>d(e=>e+1),[]),onToastRemove:r.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:s})})};P.displayName=h;var L="ToastViewport",D=["F8"],S="toast.viewportPause",M="toast.viewportResume",j=r.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:o=D,label:i="Notifications ({hotkey})",...s}=e,l=R(L,n),c=g(n),d=r.useRef(null),p=r.useRef(null),v=r.useRef(null),m=r.useRef(null),y=(0,a.s)(t,m,l.onViewportChange),E=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),h=l.toastCount>0;r.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null==(t=m.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),r.useEffect(()=>{let e=d.current,t=m.current;if(h&&e&&t){let n=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(S);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},r=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(M);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||r()},i=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",i),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[h,l.isClosePausedRef]);let x=r.useCallback(e=>{let{tabbingDirection:t}=e,n=c().map(e=>{let n=e.ref.current,r=[n,...function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}(n)];return"forwards"===t?r:r.reverse()});return("forwards"===t?n.reverse():n).flat()},[c]);return r.useEffect(()=>{let e=m.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){var r,o,i;let n=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null==(r=p.current)||r.focus();return}let s=x({tabbingDirection:a?"backwards":"forwards"}),l=s.findIndex(e=>e===n);H(s.slice(l+1))?t.preventDefault():a?null==(o=p.current)||o.focus():null==(i=v.current)||i.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,x]),(0,w.jsxs)(u.lg,{ref:d,role:"region","aria-label":i.replace("{hotkey}",E),tabIndex:-1,style:{pointerEvents:h?void 0:"none"},children:[h&&(0,w.jsx)(A,{ref:p,onFocusFromOutsideViewport:()=>{H(x({tabbingDirection:"forwards"}))}}),(0,w.jsx)(b.Slot,{scope:n,children:(0,w.jsx)(f.sG.ol,{tabIndex:-1,...s,ref:y})}),h&&(0,w.jsx)(A,{ref:v,onFocusFromOutsideViewport:()=>{H(x({tabbingDirection:"backwards"}))}})]})});j.displayName=L;var O="ToastFocusProxy",A=r.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=R(O,n);return(0,w.jsx)(E,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let n=e.relatedTarget;(null==(t=i.viewport)?void 0:t.contains(n))||r()}})});A.displayName=O;var k="Toast",I=r.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:o,onOpenChange:a,...s}=e,[l,u]=(0,v.i)({prop:r,defaultProp:null==o||o,onChange:a,caller:k});return(0,w.jsx)(d.C,{present:n||l,children:(0,w.jsx)(W,{open:l,...s,ref:t,onClose:()=>u(!1),onPause:(0,p.c)(e.onPause),onResume:(0,p.c)(e.onResume),onSwipeStart:(0,i.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,i.m)(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(n,"px"))}),onSwipeCancel:(0,i.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,i.m)(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(n,"px")),u(!1)})})})});I.displayName=k;var[F,_]=T(k,{onClose(){}}),W=r.forwardRef((e,t)=>{let{__scopeToast:n,type:s="foreground",duration:l,open:c,onClose:d,onEscapeKeyDown:v,onPause:m,onResume:y,onSwipeStart:E,onSwipeMove:h,onSwipeCancel:g,onSwipeEnd:x,...T}=e,C=R(k,n),[N,P]=r.useState(null),L=(0,a.s)(t,e=>P(e)),D=r.useRef(null),j=r.useRef(null),O=l||C.duration,A=r.useRef(0),I=r.useRef(O),_=r.useRef(0),{onToastAdd:W,onToastRemove:U}=C,G=(0,p.c)(()=>{var e;(null==N?void 0:N.contains(document.activeElement))&&(null==(e=C.viewport)||e.focus()),d()}),$=r.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(_.current),A.current=new Date().getTime(),_.current=window.setTimeout(G,e))},[G]);r.useEffect(()=>{let e=C.viewport;if(e){let t=()=>{$(I.current),null==y||y()},n=()=>{let e=new Date().getTime()-A.current;I.current=I.current-e,window.clearTimeout(_.current),null==m||m()};return e.addEventListener(S,n),e.addEventListener(M,t),()=>{e.removeEventListener(S,n),e.removeEventListener(M,t)}}},[C.viewport,O,m,y,$]),r.useEffect(()=>{c&&!C.isClosePausedRef.current&&$(O)},[c,O,C.isClosePausedRef,$]),r.useEffect(()=>(W(),()=>U()),[W,U]);let V=r.useMemo(()=>N?function e(t){let n=[];return Array.from(t.childNodes).forEach(t=>{var r;if(t.nodeType===t.TEXT_NODE&&t.textContent&&n.push(t.textContent),(r=t).nodeType===r.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!r)if(o){let e=t.dataset.radixToastAnnounceAlt;e&&n.push(e)}else n.push(...e(t))}}),n}(N):null,[N]);return C.viewport?(0,w.jsxs)(w.Fragment,{children:[V&&(0,w.jsx)(K,{__scopeToast:n,role:"status","aria-live":"foreground"===s?"assertive":"polite","aria-atomic":!0,children:V}),(0,w.jsx)(F,{scope:n,onClose:G,children:o.createPortal((0,w.jsx)(b.ItemSlot,{scope:n,children:(0,w.jsx)(u.bL,{asChild:!0,onEscapeKeyDown:(0,i.m)(v,()=>{C.isFocusedToastEscapeKeyDownRef.current||G(),C.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,w.jsx)(f.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":C.swipeDirection,...T,ref:L,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Escape"===e.key&&(null==v||v(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,G()))}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{0===e.button&&(D.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{if(!D.current)return;let t=e.clientX-D.current.x,n=e.clientY-D.current.y,r=!!j.current,o=["left","right"].includes(C.swipeDirection),i=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,a=o?i(0,t):0,s=o?0:i(0,n),l="touch"===e.pointerType?10:2,u={x:a,y:s},c={originalEvent:e,delta:u};r?(j.current=u,X("toast.swipeMove",h,c,{discrete:!1})):Y(u,C.swipeDirection,l)?(j.current=u,X("toast.swipeStart",E,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(n)>l)&&(D.current=null)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=j.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),j.current=null,D.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};Y(t,C.swipeDirection,C.swipeThreshold)?X("toast.swipeEnd",x,r,{discrete:!0}):X("toast.swipeCancel",g,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),C.viewport)})]}):null}),K=e=>{let{__scopeToast:t,children:n,...o}=e,i=R(k,t),[a,s]=r.useState(!1),[l,u]=r.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,p.c)(e);(0,m.N)(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>s(!0)),r.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,w.jsx)(c.Z,{asChild:!0,children:(0,w.jsx)(E,{...o,children:a&&(0,w.jsxs)(w.Fragment,{children:[i.label," ",n]})})})},U=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,w.jsx)(f.sG.div,{...r,ref:t})});U.displayName="ToastTitle";var G=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,w.jsx)(f.sG.div,{...r,ref:t})});G.displayName="ToastDescription";var $="ToastAction",V=r.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,w.jsx)(B,{altText:n,asChild:!0,children:(0,w.jsx)(z,{...r,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat($,"`. Expected non-empty `string`.")),null)});V.displayName=$;var q="ToastClose",z=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,o=_(q,n);return(0,w.jsx)(B,{asChild:!0,children:(0,w.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,i.m)(e.onClick,o.onClose)})})});z.displayName=q;var B=r.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...o}=e;return(0,w.jsx)(f.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function X(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,f.hO)(i,a):i.dispatchEvent(a)}var Y=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return"left"===t||"right"===t?i&&r>n:!i&&o>n};function H(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Z=P,J=j,Q=I,ee=U,et=G,en=V,er=z},3655:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>s});var r=n(2115),o=n(7650),i=n(9708),a=n(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},4378:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(2115),o=n(7650),i=n(3655),a=n(2712),s=n(5155),l=r.forwardRef((e,t)=>{var n,l;let{container:u,...c}=e,[d,f]=r.useState(!1);(0,a.N)(()=>f(!0),[]);let p=u||d&&(null==(l=globalThis)||null==(n=l.document)?void 0:n.body);return p?o.createPortal((0,s.jsx)(i.sG.div,{...c,ref:t}),p):null});l.displayName="Portal"},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>s});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function s({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,s,l]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),s=o.useRef(t);return a(()=>{s.current=t},[t]),o.useEffect(()=>{i.current!==n&&(s.current?.(n),i.current=n)},[n,i]),[n,r,s]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else s(t)},[u,e,s,l])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(2115),o=n(5155);function i(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,i){let a=r.createContext(i),s=n.length;n=[...n,i];let l=t=>{let{scope:n,children:i,...l}=t,u=n?.[e]?.[s]||a,c=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(u.Provider,{value:c,children:i})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[s]||a,u=r.useContext(l);if(u)return u;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},7328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function i(e,t,n){var o=r(e,t,"set");if(o.set)o.set.call(e,n);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=n}return n}n.d(t,{N:()=>f});var a,s=n(2115),l=n(6081),u=n(6101),c=n(9708),d=n(5155);function f(e){let t=e+"CollectionProvider",[n,r]=(0,l.A)(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=s.useRef(null),i=s.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let f=e+"CollectionSlot",p=(0,c.TL)(f),v=s.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(f,n),a=(0,u.s)(t,o.collectionRef);return(0,d.jsx)(p,{ref:a,children:r})});v.displayName=f;let m=e+"CollectionItemSlot",w="data-radix-collection-item",y=(0,c.TL)(m),E=s.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=s.useRef(null),l=(0,u.s)(t,a),c=i(m,n);return s.useEffect(()=>(c.itemMap.set(a,{ref:a,...o}),()=>void c.itemMap.delete(a))),(0,d.jsx)(y,{...{[w]:""},ref:l,children:r})});return E.displayName=m,[{Provider:a,Slot:v,ItemSlot:E},function(t){let n=i(e+"CollectionConsumer",t);return s.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(w,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var p=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=m(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function m(e){return e!=e||0===e?0:Math.trunc(e)}a=new WeakMap},8905:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(2115),o=n(6101),i=n(2712),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),l=r.useRef(null),u=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=l.current,n=u.current;if(n!==e){let r=c.current,o=s(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=s(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=s(l.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(t),l="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),u=(0,o.s)(a.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||a.isPresent?r.cloneElement(l,{ref:u}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{n.d(t,{lg:()=>y,qW:()=>f,bL:()=>w});var r,o=n(2115),i=n(5185),a=n(3655),s=n(6101),l=n(9033),u=n(5155),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:w,onPointerDownOutside:y,onFocusOutside:E,onInteractOutside:h,onDismiss:b,...g}=e,x=o.useContext(d),[T,C]=o.useState(null),N=null!=(f=null==T?void 0:T.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,R]=o.useState({}),P=(0,s.s)(t,e=>C(e)),L=Array.from(x.layers),[D]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),S=L.indexOf(D),M=T?L.indexOf(T):-1,j=x.layersWithOutsidePointerEventsDisabled.size>0,O=M>=S,A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));O&&!n&&(null==y||y(e),null==h||h(e),e.defaultPrevented||null==b||b())},N),k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...x.branches].some(e=>e.contains(t))&&(null==E||E(e),null==h||h(e),e.defaultPrevented||null==b||b())},N);return!function(e,t=globalThis?.document){let n=(0,l.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M===x.layers.size-1&&(null==w||w(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},N),o.useEffect(()=>{if(T)return p&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(r=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(T)),x.layers.add(T),v(),()=>{p&&1===x.layersWithOutsidePointerEventsDisabled.size&&(N.body.style.pointerEvents=r)}},[T,N,p,x]),o.useEffect(()=>()=>{T&&(x.layers.delete(T),x.layersWithOutsidePointerEventsDisabled.delete(T),v())},[T,x]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.sG.div,{...g,ref:P,style:{pointerEvents:j?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});f.displayName="DismissableLayer";var p=o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,s.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(a.sG.div,{...e,ref:i})});function v(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,a.hO)(i,s):i.dispatchEvent(s)}p.displayName="DismissableLayerBranch";var w=f,y=p}}]);