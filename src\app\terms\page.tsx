import { Metadata } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { PublicLayout } from "@/components/layout/public-layout";
import { FileText, Scale, AlertTriangle, Users } from "lucide-react";

export const metadata: Metadata = {
  title: "Terms of Service",
  description: "Read VirtualRealTour's terms of service and user agreement.",
};

export default function TermsPage() {
  return (
    <PublicLayout>
      <div className="py-20">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-4">
              <FileText className="h-12 w-12 text-primary" />
            </div>
            <h1 className="text-4xl font-bold mb-4">Terms of Service</h1>
            <p className="text-muted-foreground">
              Last updated: January 1, 2024
            </p>
          </div>

          {/* Important Notice */}
          <Card className="mb-8 border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="flex items-center text-orange-800">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Important Notice
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-orange-700">
                By using VirtualRealTour's services, you agree to be bound by these terms. 
                Please read them carefully before using our platform.
              </p>
            </CardContent>
          </Card>

          <div className="space-y-8">
            {/* Acceptance of Terms */}
            <Card>
              <CardHeader>
                <CardTitle>1. Acceptance of Terms</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  These Terms of Service ("Terms") govern your use of VirtualRealTour's 
                  platform and services. By accessing or using our services, you agree 
                  to be bound by these Terms and our Privacy Policy. If you do not agree 
                  to these Terms, you may not use our services.
                </p>
              </CardContent>
            </Card>

            {/* Description of Service */}
            <Card>
              <CardHeader>
                <CardTitle>2. Description of Service</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  VirtualRealTour provides a platform for creating, hosting, and sharing 
                  360° virtual tours. Our services include:
                </p>
                <ul className="list-disc list-inside text-muted-foreground space-y-1">
                  <li>Virtual tour creation and editing tools</li>
                  <li>Cloud hosting and content delivery</li>
                  <li>Analytics and performance tracking</li>
                  <li>Integration with third-party services</li>
                  <li>Customer support and documentation</li>
                </ul>
              </CardContent>
            </Card>

            {/* User Accounts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  3. User Accounts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Account Creation</h3>
                  <p className="text-muted-foreground">
                    You must create an account to use our services. You are responsible 
                    for maintaining the confidentiality of your account credentials and 
                    for all activities that occur under your account.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Eligibility</h3>
                  <p className="text-muted-foreground">
                    You must be at least 18 years old to create an account. By creating 
                    an account, you represent that you have the legal capacity to enter 
                    into these Terms.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Account Security</h3>
                  <p className="text-muted-foreground">
                    You must notify us immediately of any unauthorized use of your account 
                    or any other breach of security. We are not liable for any loss or 
                    damage arising from your failure to protect your account.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Acceptable Use */}
            <Card>
              <CardHeader>
                <CardTitle>4. Acceptable Use Policy</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  You agree not to use our services for any unlawful or prohibited activities, including:
                </p>
                
                <div>
                  <h3 className="font-semibold mb-2">Prohibited Content</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    <li>Illegal, harmful, or offensive content</li>
                    <li>Content that violates intellectual property rights</li>
                    <li>Spam, malware, or malicious code</li>
                    <li>Content that promotes violence or discrimination</li>
                    <li>Adult content or material harmful to minors</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Prohibited Activities</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    <li>Attempting to gain unauthorized access to our systems</li>
                    <li>Interfering with or disrupting our services</li>
                    <li>Using automated tools to access our platform</li>
                    <li>Impersonating others or providing false information</li>
                    <li>Violating any applicable laws or regulations</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Content and Intellectual Property */}
            <Card>
              <CardHeader>
                <CardTitle>5. Content and Intellectual Property</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Your Content</h3>
                  <p className="text-muted-foreground">
                    You retain ownership of the content you upload to our platform. 
                    By uploading content, you grant us a license to use, store, and 
                    display your content as necessary to provide our services.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Our Content</h3>
                  <p className="text-muted-foreground">
                    Our platform, including its design, features, and functionality, 
                    is owned by VirtualRealTour and protected by intellectual property laws. 
                    You may not copy, modify, or distribute our content without permission.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Copyright Infringement</h3>
                  <p className="text-muted-foreground">
                    We respect intellectual property rights and will respond to valid 
                    copyright infringement notices. If you believe your copyright has 
                    been infringed, please contact us with the required information.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Payment Terms */}
            <Card>
              <CardHeader>
                <CardTitle>6. Payment Terms</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Subscription Plans</h3>
                  <p className="text-muted-foreground">
                    We offer various subscription plans with different features and pricing. 
                    Subscription fees are billed in advance and are non-refundable except 
                    as required by law.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Payment Processing</h3>
                  <p className="text-muted-foreground">
                    Payments are processed by third-party payment providers. You agree 
                    to provide accurate payment information and authorize us to charge 
                    your payment method for applicable fees.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Cancellation and Refunds</h3>
                  <p className="text-muted-foreground">
                    You may cancel your subscription at any time. Cancellations take 
                    effect at the end of your current billing period. We offer a 
                    30-day money-back guarantee for new subscriptions.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Privacy and Data Protection */}
            <Card>
              <CardHeader>
                <CardTitle>7. Privacy and Data Protection</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Your privacy is important to us. Our Privacy Policy explains how we 
                  collect, use, and protect your information. By using our services, 
                  you consent to our data practices as described in our Privacy Policy.
                </p>
              </CardContent>
            </Card>

            {/* Disclaimers and Limitations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Scale className="h-5 w-5 mr-2" />
                  8. Disclaimers and Limitations of Liability
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Service Availability</h3>
                  <p className="text-muted-foreground">
                    We strive to provide reliable services but cannot guarantee 100% uptime. 
                    Our services are provided "as is" without warranties of any kind.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Limitation of Liability</h3>
                  <p className="text-muted-foreground">
                    To the maximum extent permitted by law, VirtualRealTour shall not be 
                    liable for any indirect, incidental, special, or consequential damages 
                    arising from your use of our services.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Termination */}
            <Card>
              <CardHeader>
                <CardTitle>9. Termination</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We may terminate or suspend your account at any time for violation of 
                  these Terms or for any other reason. Upon termination, your right to 
                  use our services will cease immediately, and we may delete your content.
                </p>
              </CardContent>
            </Card>

            {/* Governing Law */}
            <Card>
              <CardHeader>
                <CardTitle>10. Governing Law and Dispute Resolution</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  These Terms are governed by the laws of Nigeria. Any disputes arising 
                  from these Terms or your use of our services will be resolved through 
                  binding arbitration in Lagos, Nigeria, except where prohibited by law.
                </p>
              </CardContent>
            </Card>

            {/* Changes to Terms */}
            <Card>
              <CardHeader>
                <CardTitle>11. Changes to Terms</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We may update these Terms from time to time. We will notify you of 
                  material changes by posting the updated Terms on our website and 
                  sending you an email notification. Your continued use of our services 
                  constitutes acceptance of the updated Terms.
                </p>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>12. Contact Information</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  If you have any questions about these Terms, please contact us:
                </p>
                <div className="space-y-2 text-muted-foreground">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Address:</strong> 123 Victoria Island, Lagos State, Nigeria</p>
                  <p><strong>Phone:</strong> +234 (0) ************</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
}
