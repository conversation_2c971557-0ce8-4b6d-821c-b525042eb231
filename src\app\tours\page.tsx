import { Metada<PERSON> } from "next";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Eye, MapPin, Heart, Share2, Search } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Explore Tours",
  description: "Discover amazing virtual tours from across Nigeria",
};

export default function ToursPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Eye className="h-8 w-8 text-primary" />
            <span className="text-2xl font-bold">VirtualRealTour</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/tours" className="text-foreground font-medium">
              Tours
            </Link>
            <Link href="/pricing" className="text-muted-foreground hover:text-foreground">
              Pricing
            </Link>
            <Link href="/about" className="text-muted-foreground hover:text-foreground">
              About
            </Link>
            <Link href="/contact" className="text-muted-foreground hover:text-foreground">
              Contact
            </Link>
          </nav>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link href="/auth/signin">Sign In</Link>
            </Button>
            <Button asChild>
              <Link href="/auth/signup">Get Started</Link>
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Explore Virtual Tours</h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Discover stunning properties, educational institutions, hotels, and more 
            through immersive 360° virtual experiences across Nigeria.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-md mx-auto relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search tours by location, type, or name..."
              className="pl-10"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 mb-8">
          <Button variant="outline" size="sm">All Categories</Button>
          <Button variant="outline" size="sm">Real Estate</Button>
          <Button variant="outline" size="sm">Education</Button>
          <Button variant="outline" size="sm">Hospitality</Button>
          <Button variant="outline" size="sm">Retail</Button>
          <Button variant="outline" size="sm">Events</Button>
        </div>

        {/* Tours Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((tour) => (
            <Card key={tour} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="aspect-video bg-muted relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  <Eye className="h-12 w-12 text-muted-foreground" />
                </div>
                <div className="absolute top-4 left-4">
                  <span className="bg-primary text-primary-foreground px-2 py-1 rounded-full text-xs font-medium">
                    Real Estate
                  </span>
                </div>
              </div>
              
              <CardHeader>
                <CardTitle className="line-clamp-2">
                  Luxury 3-Bedroom Apartment in Victoria Island
                </CardTitle>
                <CardDescription className="flex items-center text-sm">
                  <MapPin className="h-4 w-4 mr-1" />
                  Victoria Island, Lagos
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span className="flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      1,234
                    </span>
                    <span className="flex items-center">
                      <Heart className="h-4 w-4 mr-1" />
                      89
                    </span>
                    <span className="flex items-center">
                      <Share2 className="h-4 w-4 mr-1" />
                      12
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    by <span className="font-medium">John Doe</span>
                  </div>
                  <Button size="sm" asChild>
                    <Link href={`/tours/${tour}`}>
                      View Tour
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Load More Tours
          </Button>
        </div>
      </div>
    </div>
  );
}
