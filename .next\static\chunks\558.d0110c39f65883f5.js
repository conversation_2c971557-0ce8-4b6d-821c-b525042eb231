"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{7558:(e,t,r)=>{r.r(t),r.d(t,{Canvas:()=>f,ReactThreeFiber:()=>n.t,_roots:()=>n._,act:()=>n.w,addAfterEffect:()=>n.k,addEffect:()=>n.j,addTail:()=>n.l,advance:()=>n.n,applyProps:()=>n.q,buildGraph:()=>n.x,context:()=>n.p,createEvents:()=>n.g,createPortal:()=>n.o,createRoot:()=>n.c,dispose:()=>n.v,events:()=>n.f,extend:()=>n.e,flushGlobalEffects:()=>n.h,getRootState:()=>n.s,invalidate:()=>n.m,reconciler:()=>n.r,unmountComponentAtNode:()=>n.d,useFrame:()=>n.C,useGraph:()=>n.D,useInstanceHandle:()=>n.y,useLoader:()=>n.F,useStore:()=>n.z,useThree:()=>n.A});var n=r(3816),o=r(2115),i=r(7431);function s(e,t){let r;return(...n)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...n),t)}}let l=["x","y","top","bottom","left","right","width","height"],c=(e,t)=>l.every(r=>e[r]===t[r]);var u=r(6354),a=r(5155);function d({ref:e,children:t,fallback:r,resize:l,style:u,gl:d,events:f=n.f,eventSource:h,eventPrefix:v,shadows:w,linear:m,flat:p,legacy:b,orthographic:E,frameloop:g,dpr:y,performance:z,raycaster:x,camera:C,scene:L,onPointerMissed:j,onCreated:H,...R}){o.useMemo(()=>(0,n.e)(i),[]);let S=(0,n.u)(),[O,k]=function({debounce:e,scroll:t,polyfill:r,offsetSize:n}={debounce:0,scroll:!1,offsetSize:!1}){var i,l,u;let a=r||("undefined"==typeof window?class{}:window.ResizeObserver);if(!a)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[d,f]=(0,o.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),h=(0,o.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:d,orientationHandler:null}),v=e?"number"==typeof e?e:e.scroll:null,w=e?"number"==typeof e?e:e.resize:null,m=(0,o.useRef)(!1);(0,o.useEffect)(()=>(m.current=!0,()=>void(m.current=!1)));let[p,b,E]=(0,o.useMemo)(()=>{let e=()=>{if(!h.current.element)return;let{left:e,top:t,width:r,height:o,bottom:i,right:s,x:l,y:u}=h.current.element.getBoundingClientRect(),a={left:e,top:t,width:r,height:o,bottom:i,right:s,x:l,y:u};h.current.element instanceof HTMLElement&&n&&(a.height=h.current.element.offsetHeight,a.width=h.current.element.offsetWidth),Object.freeze(a),m.current&&!c(h.current.lastBounds,a)&&f(h.current.lastBounds=a)};return[e,w?s(e,w):e,v?s(e,v):e]},[f,n,v,w]);function g(){h.current.scrollContainers&&(h.current.scrollContainers.forEach(e=>e.removeEventListener("scroll",E,!0)),h.current.scrollContainers=null),h.current.resizeObserver&&(h.current.resizeObserver.disconnect(),h.current.resizeObserver=null),h.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",h.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",h.current.orientationHandler))}function y(){h.current.element&&(h.current.resizeObserver=new a(E),h.current.resizeObserver.observe(h.current.element),t&&h.current.scrollContainers&&h.current.scrollContainers.forEach(e=>e.addEventListener("scroll",E,{capture:!0,passive:!0})),h.current.orientationHandler=()=>{E()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",h.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",h.current.orientationHandler))}return i=E,l=!!t,(0,o.useEffect)(()=>{if(l)return window.addEventListener("scroll",i,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",i,!0)},[i,l]),u=b,(0,o.useEffect)(()=>(window.addEventListener("resize",u),()=>void window.removeEventListener("resize",u)),[u]),(0,o.useEffect)(()=>{g(),y()},[t,E,b]),(0,o.useEffect)(()=>g,[]),[e=>{e&&e!==h.current.element&&(g(),h.current.element=e,h.current.scrollContainers=function e(t){let r=[];if(!t||t===document.body)return r;let{overflow:n,overflowX:o,overflowY:i}=window.getComputedStyle(t);return[n,o,i].some(e=>"auto"===e||"scroll"===e)&&r.push(t),[...r,...e(t.parentElement)]}(e),y())},d,p]}({scroll:!0,debounce:{scroll:50,resize:0},...l}),T=o.useRef(null),_=o.useRef(null);o.useImperativeHandle(e,()=>T.current);let B=(0,n.a)(j),[A,F]=o.useState(!1),[M,G]=o.useState(!1);if(A)throw A;if(M)throw M;let N=o.useRef(null);(0,n.b)(()=>{let e=T.current;k.width>0&&k.height>0&&e&&(N.current||(N.current=(0,n.c)(e)),async function(){await N.current.configure({gl:d,scene:L,events:f,shadows:w,linear:m,flat:p,legacy:b,orthographic:E,frameloop:g,dpr:y,performance:z,raycaster:x,camera:C,size:k,onPointerMissed:(...e)=>null==B.current?void 0:B.current(...e),onCreated:e=>{null==e.events.connect||e.events.connect(h?(0,n.i)(h)?h.current:h:_.current),v&&e.setEvents({compute:(e,t)=>{let r=e[v+"X"],n=e[v+"Y"];t.pointer.set(r/t.size.width*2-1,-(2*(n/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),null==H||H(e)}}),N.current.render((0,a.jsx)(S,{children:(0,a.jsx)(n.E,{set:G,children:(0,a.jsx)(o.Suspense,{fallback:(0,a.jsx)(n.B,{set:F}),children:null!=t?t:null})})}))}())}),o.useEffect(()=>{let e=T.current;if(e)return()=>(0,n.d)(e)},[]);let P=h?"none":"auto";return(0,a.jsx)("div",{ref:_,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:P,...u},...R,children:(0,a.jsx)("div",{ref:O,style:{width:"100%",height:"100%"},children:(0,a.jsx)("canvas",{ref:T,style:{display:"block"},children:r})})})}function f(e){return(0,a.jsx)(u.Af,{children:(0,a.jsx)(d,{...e})})}r(1933),r(5220),r(4342)}}]);