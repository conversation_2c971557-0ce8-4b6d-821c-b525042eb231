"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[579],{133:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},255:(e,t,n)=>{function r(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return r}}),n(5155),n(7650),n(5744),n(589)},620:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],d=[],f=1,p=null,m=3,h=!1,b=!1,v=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(d);null!==t;){if(null===t.callback)o(d);else if(t.startTime<=e)o(d),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(d)}}function P(e){if(v=!1,j(e),!b)if(null!==r(u))b=!0,A();else{var t=r(d);null!==t&&T(P,t.startTime-e)}}var _=!1,E=-1,x=5,O=-1;function M(){return!(t.unstable_now()-O<x)}function C(){if(_){var e=t.unstable_now();O=e;var n=!0;try{e:{b=!1,v&&(v=!1,y(E),E=-1),h=!0;var i=m;try{t:{for(j(e),p=r(u);null!==p&&!(p.expirationTime>e&&M());){var l=p.callback;if("function"==typeof l){p.callback=null,m=p.priorityLevel;var s=l(p.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){p.callback=s,j(e),n=!0;break t}p===r(u)&&o(u),j(e)}else o(u);p=r(u)}if(null!==p)n=!0;else{var c=r(d);null!==c&&T(P,c.startTime-e),n=!1}}break e}finally{p=null,m=i,h=!1}}}finally{n?a():_=!1}}}if("function"==typeof w)a=function(){w(C)};else if("undefined"!=typeof MessageChannel){var S=new MessageChannel,k=S.port2;S.port1.onmessage=C,a=function(){k.postMessage(null)}}else a=function(){g(C,0)};function A(){_||(_=!0,a())}function T(e,n){E=g(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){b||h||(b=!0,A())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):x=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return m},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:f++,callback:o,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>a?(e.sortIndex=i,n(d,e),null===r(u)&&e===r(d)&&(v?(y(E),E=-1):v=!0,T(P,i-a))):(e.sortIndex=l,n(u,e),b||h||(b=!0,A())),e},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}},901:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(8229)._(n(2115)).default.createContext(null)},1193:(e,t)=>{function n(e){var t;let{config:n,src:r,width:o,quality:i}=e,a=i||(null==(t=n.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return n.path+"?url="+encodeURIComponent(r)+"&w="+o+"&q="+a+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),n.__next_img_default=!0;let r=n},1469:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return s},getImageProps:function(){return l}});let r=n(8229),o=n(8883),i=n(3063),a=r._(n(1193));function l(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let s=i.Image},1933:(e,t,n)=>{e.exports=n(6500)},2146:(e,t,n)=>{function r(e){let{reason:t,children:n}=e;return n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}}),n(5262)},2152:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("volume-x",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]])},2178:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},2464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=n(8229)._(n(2115)).default.createContext({})},3063:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return w}});let r=n(8229),o=n(6966),i=n(5155),a=o._(n(2115)),l=r._(n(7650)),s=r._(n(5564)),c=n(8883),u=n(5840),d=n(6752);n(3230);let f=n(901),p=r._(n(1193)),m=n(6654),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function b(e,t,n,r,o,i,a){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==n?void 0:n.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,o=!1;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function v(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}let g=(0,a.forwardRef)((e,t)=>{let{src:n,srcSet:r,sizes:o,height:l,width:s,decoding:c,className:u,style:d,fetchPriority:f,placeholder:p,loading:h,unoptimized:g,fill:y,onLoadRef:w,onLoadingCompleteRef:j,setBlurComplete:P,setShowAltText:_,sizesInput:E,onLoad:x,onError:O,...M}=e,C=(0,a.useCallback)(e=>{e&&(O&&(e.src=e.src),e.complete&&b(e,p,w,j,P,g,E))},[n,p,w,j,P,O,g,E]),S=(0,m.useMergedRef)(t,C);return(0,i.jsx)("img",{...M,...v(f),loading:h,width:s,height:l,decoding:c,"data-nimg":y?"fill":"1",className:u,style:d,sizes:o,srcSet:r,src:n,ref:S,onLoad:e=>{b(e.currentTarget,p,w,j,P,g,E)},onError:e=>{_(!0),"empty"!==p&&P(!0),O&&O(e)}})});function y(e){let{isAppRouter:t,imgAttributes:n}=e,r={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...v(n.fetchPriority)};return t&&l.default.preload?(l.default.preload(n.src,r),null):(0,i.jsx)(s.default,{children:(0,i.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...r},"__nimg-"+n.src+n.srcSet+n.sizes)})}let w=(0,a.forwardRef)((e,t)=>{let n=(0,a.useContext)(f.RouterContext),r=(0,a.useContext)(d.ImageConfigContext),o=(0,a.useMemo)(()=>{var e;let t=h||r||u.imageConfigDefault,n=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:n,deviceSizes:o,qualities:i}},[r]),{onLoad:l,onLoadingComplete:s}=e,m=(0,a.useRef)(l);(0,a.useEffect)(()=>{m.current=l},[l]);let b=(0,a.useRef)(s);(0,a.useEffect)(()=>{b.current=s},[s]);let[v,w]=(0,a.useState)(!1),[j,P]=(0,a.useState)(!1),{props:_,meta:E}=(0,c.getImgProps)(e,{defaultLoader:p.default,imgConf:o,blurComplete:v,showAltText:j});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(g,{..._,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:m,onLoadingCompleteRef:b,setBlurComplete:w,setShowAltText:P,sizesInput:e.sizes,ref:t}),E.priority?(0,i.jsx)(y,{isAppRouter:!n,imgAttributes:_}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3816:(e,t,n)=>{let r,o,i,a,l;n.d(t,{A:()=>eb,B:()=>H,C:()=>ev,D:()=>eg,E:()=>Y,F:()=>eP,_:()=>eH,a:()=>F,b:()=>N,c:()=>eU,d:()=>eV,e:()=>eC,f:()=>te,g:()=>eu,h:()=>e2,i:()=>z,j:()=>eQ,k:()=>eJ,l:()=>e0,m:()=>e8,n:()=>e9,o:()=>eX,p:()=>ef,q:()=>eo,r:()=>eq,s:()=>B,t:()=>T,u:()=>q,v:()=>W,w:()=>R,x:()=>X,y:()=>em,z:()=>eh});var s=n(3264),c=n(7431),u=n(2115),d=n.t(u,2),f=n(1933),p=n(5643);let m=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},h=e=>e?m(e):m,{useSyncExternalStoreWithSelector:b}=p,v=e=>e,g=(e,t)=>{let n=h(e),r=(e,r=t)=>(function(e,t=v,n){let r=b(e.subscribe,e.getState,e.getInitialState,t,n);return u.useDebugValue(r),r})(n,e,r);return Object.assign(r,n),r},y=(e,t)=>e?g(e,t):g;var w=n(5220),j=n.n(w),P=n(4342);let _=e=>"object"==typeof e&&"function"==typeof e.then,E=[];function x(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let r=e.length;if(t.length!==r)return!1;for(let o=0;o<r;o++)if(!n(e[o],t[o]))return!1;return!0}function O(e,t=null,n=!1,r={}){for(let o of(null===t&&(t=[e]),E))if(x(t,o.keys,o.equal)){if(n)return;if(Object.prototype.hasOwnProperty.call(o,"error"))throw o.error;if(Object.prototype.hasOwnProperty.call(o,"response"))return r.lifespan&&r.lifespan>0&&(o.timeout&&clearTimeout(o.timeout),o.timeout=setTimeout(o.remove,r.lifespan)),o.response;if(!n)throw o.promise}let o={keys:t,equal:r.equal,remove:()=>{let e=E.indexOf(o);-1!==e&&E.splice(e,1)},promise:(_(e)?e:e(...t)).then(e=>{o.response=e,r.lifespan&&r.lifespan>0&&(o.timeout=setTimeout(o.remove,r.lifespan))}).catch(e=>o.error=e)};if(E.push(o),!n)throw o.promise}let M=(e,t,n)=>O(e,t,!1,n),C=(e,t,n)=>void O(e,t,!0,n),S=e=>{if(void 0===e||0===e.length)E.splice(0,E.length);else{let t=E.find(t=>x(e,t.keys,t.equal));t&&t.remove()}};var k=n(5155),A=n(6354);n(9538);var T=Object.freeze({__proto__:null});function I(e){let t=e.root;for(;t.getState().previousRoot;)t=t.getState().previousRoot;return t}let R=d.act,L=e=>e&&e.isOrthographicCamera,z=e=>e&&e.hasOwnProperty("current"),D=e=>null!=e&&("string"==typeof e||"number"==typeof e||e.isColor),N=((e,t)=>"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative"))()?u.useLayoutEffect:u.useEffect;function F(e){let t=u.useRef(e);return N(()=>void(t.current=e),[e]),t}function q(){let e=(0,A.u5)(),t=(0,A.y3)();return u.useMemo(()=>({children:n})=>{let r=(0,A.Nz)(e,!0,e=>e.type===u.StrictMode)?u.StrictMode:u.Fragment;return(0,k.jsx)(r,{children:(0,k.jsx)(t,{children:n})})},[e,t])}function H({set:e}){return N(()=>(e(new Promise(()=>null)),()=>e(!1)),[e]),null}let Y=(e=>((e=class extends u.Component{constructor(...e){super(...e),this.state={error:!1}}componentDidCatch(e){this.props.set(e)}render(){return this.state.error?null:this.props.children}}).getDerivedStateFromError=()=>({error:!0}),e))();function U(e){var t;let n="undefined"!=typeof window?null!=(t=window.devicePixelRatio)?t:2:1;return Array.isArray(e)?Math.min(Math.max(e[0],n),e[1]):e}function B(e){var t;return null==(t=e.__r3f)?void 0:t.root.getState()}let V={obj:e=>e===Object(e)&&!V.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,nul:e=>null===e,arr:e=>Array.isArray(e),equ(e,t,{arrays:n="shallow",objects:r="reference",strict:o=!0}={}){let i;if(typeof e!=typeof t||!!e!=!!t)return!1;if(V.str(e)||V.num(e)||V.boo(e))return e===t;let a=V.obj(e);if(a&&"reference"===r)return e===t;let l=V.arr(e);if(l&&"reference"===n)return e===t;if((l||a)&&e===t)return!0;for(i in e)if(!(i in t))return!1;if(a&&"shallow"===n&&"shallow"===r){for(i in o?t:e)if(!V.equ(e[i],t[i],{strict:o,objects:"reference"}))return!1}else for(i in o?t:e)if(e[i]!==t[i])return!1;if(V.und(i)){if(l&&0===e.length&&0===t.length||a&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}};function X(e){let t={nodes:{},materials:{},meshes:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material),e.isMesh&&!t.meshes[e.name]&&(t.meshes[e.name]=e)}),t}function W(e){for(let t in"Scene"!==e.type&&(null==e.dispose||e.dispose()),e){let n=e[t];(null==n?void 0:n.type)!=="Scene"&&(null==n||null==n.dispose||n.dispose())}}let Z=["children","key","ref"];function G(e,t,n,r){let o=null==e?void 0:e.__r3f;return!o&&(o={root:t,type:n,parent:null,children:[],props:function(e){let t={};for(let n in e)Z.includes(n)||(t[n]=e[n]);return t}(r),object:e,eventCount:0,handlers:{},isHidden:!1},e&&(e.__r3f=o)),o}function K(e,t){let n=e[t];if(!t.includes("-"))return{root:e,key:t,target:n};for(let o of(n=e,t.split("-"))){var r;t=o,e=n,n=null==(r=n)?void 0:r[t]}return{root:e,key:t,target:n}}let $=/-\d+$/;function Q(e,t){if(V.str(t.props.attach)){if($.test(t.props.attach)){let n=t.props.attach.replace($,""),{root:r,key:o}=K(e.object,n);Array.isArray(r[o])||(r[o]=[])}let{root:n,key:r}=K(e.object,t.props.attach);t.previousAttach=n[r],n[r]=t.object}else V.fun(t.props.attach)&&(t.previousAttach=t.props.attach(e.object,t.object))}function J(e,t){if(V.str(t.props.attach)){let{root:n,key:r}=K(e.object,t.props.attach),o=t.previousAttach;void 0===o?delete n[r]:n[r]=o}else null==t.previousAttach||t.previousAttach(e.object,t.object);delete t.previousAttach}let ee=[...Z,"args","dispose","attach","object","onUpdate","dispose"],et=new Map,en=["map","emissiveMap","sheenColorMap","specularColorMap","envMap"],er=/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;function eo(e,t){var n,r;let o=e.__r3f,i=o&&I(o).getState(),a=null==o?void 0:o.eventCount;for(let n in t){let a=t[n];if(ee.includes(n))continue;if(o&&er.test(n)){"function"==typeof a?o.handlers[n]=a:delete o.handlers[n],o.eventCount=Object.keys(o.handlers).length;continue}if(void 0===a)continue;let{root:l,key:c,target:u}=K(e,n);u instanceof s.zgK&&a instanceof s.zgK?u.mask=a.mask:u instanceof s.Q1f&&D(a)?u.set(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"function"==typeof u.copy&&null!=a&&a.constructor&&u.constructor===a.constructor?u.copy(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&Array.isArray(a)?"function"==typeof u.fromArray?u.fromArray(a):u.set(...a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"number"==typeof a?"function"==typeof u.setScalar?u.setScalar(a):u.set(a):(l[c]=a,i&&!i.linear&&en.includes(c)&&null!=(r=l[c])&&r.isTexture&&l[c].format===s.GWd&&l[c].type===s.OUM&&(l[c].colorSpace=s.er$))}if(null!=o&&o.parent&&null!=i&&i.internal&&null!=(n=o.object)&&n.isObject3D&&a!==o.eventCount){let e=o.object,t=i.internal.interaction.indexOf(e);t>-1&&i.internal.interaction.splice(t,1),o.eventCount&&null!==e.raycast&&i.internal.interaction.push(e)}return o&&void 0===o.props.attach&&(o.object.isBufferGeometry?o.props.attach="geometry":o.object.isMaterial&&(o.props.attach="material")),o&&ei(o),e}function ei(e){var t;if(!e.parent)return;null==e.props.onUpdate||e.props.onUpdate(e.object);let n=null==(t=e.root)||null==t.getState?void 0:t.getState();n&&0===n.internal.frames&&n.invalidate()}function ea(e,t){e.manual||(L(e)?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix())}let el=e=>null==e?void 0:e.isObject3D;function es(e){return(e.eventObject||e.object).uuid+"/"+e.index+e.instanceId}function ec(e,t,n,r){let o=n.get(t);o&&(n.delete(t),0===n.size&&(e.delete(r),o.target.releasePointerCapture(r)))}function eu(e){function t(e){return e.filter(e=>["Move","Over","Enter","Out","Leave"].some(t=>{var n;return null==(n=e.__r3f)?void 0:n.handlers["onPointer"+t]}))}function n(t){let{internal:n}=e.getState();for(let e of n.hovered.values())if(!t.length||!t.find(t=>t.object===e.object&&t.index===e.index&&t.instanceId===e.instanceId)){let r=e.eventObject.__r3f;if(n.hovered.delete(es(e)),null!=r&&r.eventCount){let n=r.handlers,o={...e,intersections:t};null==n.onPointerOut||n.onPointerOut(o),null==n.onPointerLeave||n.onPointerLeave(o)}}}function r(e,t){for(let n=0;n<t.length;n++){let r=t[n].__r3f;null==r||null==r.handlers.onPointerMissed||r.handlers.onPointerMissed(e)}}return{handlePointer:function(o){switch(o){case"onPointerLeave":case"onPointerCancel":return()=>n([]);case"onLostPointerCapture":return t=>{let{internal:r}=e.getState();"pointerId"in t&&r.capturedMap.has(t.pointerId)&&requestAnimationFrame(()=>{r.capturedMap.has(t.pointerId)&&(r.capturedMap.delete(t.pointerId),n([]))})}}return function(i){let{onPointerMissed:a,internal:l}=e.getState();l.lastEvent.current=i;let c="onPointerMove"===o,u="onClick"===o||"onContextMenu"===o||"onDoubleClick"===o,d=function(t,n){let r=e.getState(),o=new Set,i=[],a=n?n(r.internal.interaction):r.internal.interaction;for(let e=0;e<a.length;e++){let t=B(a[e]);t&&(t.raycaster.camera=void 0)}r.previousRoot||null==r.events.compute||r.events.compute(t,r);let l=a.flatMap(function(e){let n=B(e);if(!n||!n.events.enabled||null===n.raycaster.camera)return[];if(void 0===n.raycaster.camera){var r;null==n.events.compute||n.events.compute(t,n,null==(r=n.previousRoot)?void 0:r.getState()),void 0===n.raycaster.camera&&(n.raycaster.camera=null)}return n.raycaster.camera?n.raycaster.intersectObject(e,!0):[]}).sort((e,t)=>{let n=B(e.object),r=B(t.object);return n&&r&&r.events.priority-n.events.priority||e.distance-t.distance}).filter(e=>{let t=es(e);return!o.has(t)&&(o.add(t),!0)});for(let e of(r.events.filter&&(l=r.events.filter(l,r)),l)){let t=e.object;for(;t;){var s;null!=(s=t.__r3f)&&s.eventCount&&i.push({...e,eventObject:t}),t=t.parent}}if("pointerId"in t&&r.internal.capturedMap.has(t.pointerId))for(let e of r.internal.capturedMap.get(t.pointerId).values())o.has(es(e.intersection))||i.push(e.intersection);return i}(i,c?t:void 0),f=u?function(t){let{internal:n}=e.getState(),r=t.offsetX-n.initialClick[0],o=t.offsetY-n.initialClick[1];return Math.round(Math.sqrt(r*r+o*o))}(i):0;"onPointerDown"===o&&(l.initialClick=[i.offsetX,i.offsetY],l.initialHits=d.map(e=>e.eventObject)),u&&!d.length&&f<=2&&(r(i,l.interaction),a&&a(i)),c&&n(d),!function(e,t,r,o){if(e.length){let i={stopped:!1};for(let a of e){let l=B(a.object);if(l||a.object.traverseAncestors(e=>{let t=B(e);if(t)return l=t,!1}),l){let{raycaster:c,pointer:u,camera:d,internal:f}=l,p=new s.Pq0(u.x,u.y,0).unproject(d),m=e=>{var t,n;return null!=(t=null==(n=f.capturedMap.get(e))?void 0:n.has(a.eventObject))&&t},h=e=>{let n={intersection:a,target:t.target};f.capturedMap.has(e)?f.capturedMap.get(e).set(a.eventObject,n):f.capturedMap.set(e,new Map([[a.eventObject,n]])),t.target.setPointerCapture(e)},b=e=>{let t=f.capturedMap.get(e);t&&ec(f.capturedMap,a.eventObject,t,e)},v={};for(let e in t){let n=t[e];"function"!=typeof n&&(v[e]=n)}let g={...a,...v,pointer:u,intersections:e,stopped:i.stopped,delta:r,unprojectedPoint:p,ray:c.ray,camera:d,stopPropagation(){let r="pointerId"in t&&f.capturedMap.get(t.pointerId);(!r||r.has(a.eventObject))&&(g.stopped=i.stopped=!0,f.hovered.size&&Array.from(f.hovered.values()).find(e=>e.eventObject===a.eventObject)&&n([...e.slice(0,e.indexOf(a)),a]))},target:{hasPointerCapture:m,setPointerCapture:h,releasePointerCapture:b},currentTarget:{hasPointerCapture:m,setPointerCapture:h,releasePointerCapture:b},nativeEvent:t};if(o(g),!0===i.stopped)break}}}}(d,i,f,function(e){let t=e.eventObject,n=t.__r3f;if(!(null!=n&&n.eventCount))return;let a=n.handlers;if(c){if(a.onPointerOver||a.onPointerEnter||a.onPointerOut||a.onPointerLeave){let t=es(e),n=l.hovered.get(t);n?n.stopped&&e.stopPropagation():(l.hovered.set(t,e),null==a.onPointerOver||a.onPointerOver(e),null==a.onPointerEnter||a.onPointerEnter(e))}null==a.onPointerMove||a.onPointerMove(e)}else{let n=a[o];n?(!u||l.initialHits.includes(t))&&(r(i,l.interaction.filter(e=>!l.initialHits.includes(e))),n(e)):u&&l.initialHits.includes(t)&&r(i,l.interaction.filter(e=>!l.initialHits.includes(e)))}})}}}}let ed=e=>!!(null!=e&&e.render),ef=u.createContext(null),ep=(e,t)=>{let n=y((n,r)=>{let o,i=new s.Pq0,a=new s.Pq0,l=new s.Pq0;function c(e=r().camera,t=a,n=r().size){let{width:o,height:s,top:u,left:d}=n,f=o/s;t.isVector3?l.copy(t):l.set(...t);let p=e.getWorldPosition(i).distanceTo(l);if(L(e))return{width:o/e.zoom,height:s/e.zoom,top:u,left:d,factor:1,distance:p,aspect:f};{let t=2*Math.tan(e.fov*Math.PI/180/2)*p,n=o/s*t;return{width:n,height:t,top:u,left:d,factor:o/n,distance:p,aspect:f}}}let d=e=>n(t=>({performance:{...t.performance,current:e}})),f=new s.I9Y;return{set:n,get:r,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},scene:null,xr:null,invalidate:(t=1)=>e(r(),t),advance:(e,n)=>t(e,n,r()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new s.zD7,pointer:f,mouse:f,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let e=r();o&&clearTimeout(o),e.performance.current!==e.performance.min&&d(e.performance.min),o=setTimeout(()=>d(r().performance.max),e.performance.debounce)}},size:{width:0,height:0,top:0,left:0},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:c},setEvents:e=>n(t=>({...t,events:{...t.events,...e}})),setSize:(e,t,o=0,i=0)=>{let l=r().camera,s={width:e,height:t,top:o,left:i};n(e=>({size:s,viewport:{...e.viewport,...c(l,a,s)}}))},setDpr:e=>n(t=>{let n=U(e);return{viewport:{...t.viewport,dpr:n,initialDpr:t.viewport.initialDpr||n}}}),setFrameloop:(e="always")=>{let t=r().clock;t.stop(),t.elapsedTime=0,"never"!==e&&(t.start(),t.elapsedTime=0),n(()=>({frameloop:e}))},previousRoot:void 0,internal:{interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,lastEvent:u.createRef(),active:!1,frames:0,priority:0,subscribe:(e,t,n)=>{let o=r().internal;return o.priority=o.priority+ +(t>0),o.subscribers.push({ref:e,priority:t,store:n}),o.subscribers=o.subscribers.sort((e,t)=>e.priority-t.priority),()=>{let n=r().internal;null!=n&&n.subscribers&&(n.priority=n.priority-(t>0),n.subscribers=n.subscribers.filter(t=>t.ref!==e))}}}}}),r=n.getState(),o=r.size,i=r.viewport.dpr,a=r.camera;return n.subscribe(()=>{let{camera:e,size:t,viewport:r,gl:l,set:s}=n.getState();if(t.width!==o.width||t.height!==o.height||r.dpr!==i){o=t,i=r.dpr,ea(e,t),r.dpr>0&&l.setPixelRatio(r.dpr);let n="undefined"!=typeof HTMLCanvasElement&&l.domElement instanceof HTMLCanvasElement;l.setSize(t.width,t.height,n)}e!==a&&(a=e,s(t=>({viewport:{...t.viewport,...t.viewport.getCurrentViewport(e)}})))}),n.subscribe(t=>e(t)),n};function em(e){let t=u.useRef(null);return u.useImperativeHandle(t,()=>e.current.__r3f,[e]),t}function eh(){let e=u.useContext(ef);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function eb(e=e=>e,t){return eh()(e,t)}function ev(e,t=0){let n=eh(),r=n.getState().internal.subscribe,o=F(e);return N(()=>r(o,t,n),[t,r,n]),null}function eg(e){return u.useMemo(()=>X(e),[e])}let ey=new WeakMap,ew=e=>{var t;return"function"==typeof e&&(null==e||null==(t=e.prototype)?void 0:t.constructor)===e};function ej(e,t){return function(n,...r){let o;return ew(n)?(o=ey.get(n))||(o=new n,ey.set(n,o)):o=n,e&&e(o),Promise.all(r.map(e=>new Promise((n,r)=>o.load(e,e=>{el(null==e?void 0:e.scene)&&Object.assign(e,X(e.scene)),n(e)},t,t=>r(Error(`Could not load ${e}: ${null==t?void 0:t.message}`))))))}}function eP(e,t,n,r){let o=Array.isArray(t)?t:[t],i=M(ej(n,r),[e,...o],{equal:V.equ});return Array.isArray(t)?i:i[0]}eP.preload=function(e,t,n){let r=Array.isArray(t)?t:[t];return C(ej(n),[e,...r])},eP.clear=function(e,t){return S([e,...Array.isArray(t)?t:[t]])};let e_={},eE=/^three(?=[A-Z])/,ex=e=>`${e[0].toUpperCase()}${e.slice(1)}`,eO=0,eM=e=>"function"==typeof e;function eC(e){if(eM(e)){let t=`${eO++}`;return e_[t]=e,t}Object.assign(e_,e)}function eS(e,t){let n=ex(e),r=e_[n];if("primitive"!==e&&!r)throw Error(`R3F: ${n} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);if("primitive"===e&&!t.object)throw Error("R3F: Primitives without 'object' are invalid!");if(void 0!==t.args&&!Array.isArray(t.args))throw Error("R3F: The args prop must be an array!")}function ek(e){if(e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?Q(e.parent,e):el(e.object)&&!1!==e.props.visible&&(e.object.visible=!0),e.isHidden=!1,ei(e)}}function eA(e,t,n){let r=t.root.getState();if(e.parent||e.object===r.scene){if(!t.object){var o,i;let e=e_[ex(t.type)];t.object=null!=(o=t.props.object)?o:new e(...null!=(i=t.props.args)?i:[]),t.object.__r3f=t}if(eo(t.object,t.props),t.props.attach)Q(e,t);else if(el(t.object)&&el(e.object)){let r=e.object.children.indexOf(null==n?void 0:n.object);if(n&&-1!==r){let n=e.object.children.indexOf(t.object);-1!==n?(e.object.children.splice(n,1),e.object.children.splice(n<r?r-1:r,0,t.object)):(t.object.parent=e.object,e.object.children.splice(r,0,t.object),t.object.dispatchEvent({type:"added"}),e.object.dispatchEvent({type:"childadded",child:t.object}))}else e.object.add(t.object)}for(let e of t.children)eA(t,e);ei(t)}}function eT(e,t){t&&(t.parent=e,e.children.push(t),eA(e,t))}function eI(e,t,n){if(!t||!n)return;t.parent=e;let r=e.children.indexOf(n);-1!==r?e.children.splice(r,0,t):e.children.push(t),eA(e,t,n)}function eR(e){if("function"==typeof e.dispose){let t=()=>{try{e.dispose()}catch{}};"undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?t():(0,P.unstable_scheduleCallback)(P.unstable_IdlePriority,t)}}function eL(e,t,n){if(!t)return;t.parent=null;let r=e.children.indexOf(t);-1!==r&&e.children.splice(r,1),t.props.attach?J(e,t):el(t.object)&&el(e.object)&&(e.object.remove(t.object),function(e,t){let{internal:n}=e.getState();n.interaction=n.interaction.filter(e=>e!==t),n.initialHits=n.initialHits.filter(e=>e!==t),n.hovered.forEach((e,r)=>{(e.eventObject===t||e.object===t)&&n.hovered.delete(r)}),n.capturedMap.forEach((e,r)=>{ec(n.capturedMap,t,e,r)})}(I(t),t.object));let o=null!==t.props.dispose&&!1!==n;for(let e=t.children.length-1;e>=0;e--){let n=t.children[e];eL(t,n,o)}t.children.length=0,delete t.object.__r3f,o&&"primitive"!==t.type&&"Scene"!==t.object.type&&eR(t.object),void 0===n&&ei(t)}let ez=[],eD=()=>{},eN={},eF=0,eq=function(e){let t=j()(e);return t.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:u.version}),t}({isPrimaryRenderer:!1,warnsIfNotActing:!1,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,createInstance:function(e,t,n){var r;return eS(e=ex(e)in e_?e:e.replace(eE,""),t),"primitive"===e&&null!=(r=t.object)&&r.__r3f&&delete t.object.__r3f,G(t.object,n,e,t)},removeChild:eL,appendChild:eT,appendInitialChild:eT,insertBefore:eI,appendChildToContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&eT(n,t)},removeChildFromContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&eL(n,t)},insertInContainerBefore(e,t,n){let r=e.getState().scene.__r3f;t&&n&&r&&eI(r,t,n)},getRootHostContext:()=>eN,getChildHostContext:()=>eN,commitUpdate(e,t,n,r,o){var i,a,l;eS(t,r);let s=!1;if("primitive"===e.type&&n.object!==r.object||(null==(i=r.args)?void 0:i.length)!==(null==(a=n.args)?void 0:a.length)?s=!0:null!=(l=r.args)&&l.some((e,t)=>{var r;return e!==(null==(r=n.args)?void 0:r[t])})&&(s=!0),s)ez.push([e,{...r},o]);else{let t=function(e,t){let n={};for(let r in t)if(!ee.includes(r)&&!V.equ(t[r],e.props[r]))for(let e in n[r]=t[r],t)e.startsWith(`${r}-`)&&(n[e]=t[e]);for(let r in e.props){if(ee.includes(r)||t.hasOwnProperty(r))continue;let{root:o,key:i}=K(e.object,r);if(o.constructor&&0===o.constructor.length){let e=function(e){let t=et.get(e.constructor);try{t||(t=new e.constructor,et.set(e.constructor,t))}catch(e){}return t}(o);V.und(e)||(n[i]=e[i])}else n[i]=0}return n}(e,r);Object.keys(t).length&&(Object.assign(e.props,t),eo(e.object,t))}(null===o.sibling||(4&o.flags)==0)&&function(){for(let[e]of ez){let t=e.parent;if(t)for(let n of(e.props.attach?J(t,e):el(e.object)&&el(t.object)&&t.object.remove(e.object),e.children))n.props.attach?J(e,n):el(n.object)&&el(e.object)&&e.object.remove(n.object);e.isHidden&&ek(e),e.object.__r3f&&delete e.object.__r3f,"primitive"!==e.type&&eR(e.object)}for(let[r,o,i]of ez){r.props=o;let a=r.parent;if(a){let o=e_[ex(r.type)];r.object=null!=(e=r.props.object)?e:new o(...null!=(t=r.props.args)?t:[]),r.object.__r3f=r;var e,t,n=r.object;for(let e of[i,i.alternate])if(null!==e)if("function"==typeof e.ref){null==e.refCleanup||e.refCleanup();let t=e.ref(n);"function"==typeof t&&(e.refCleanup=t)}else e.ref&&(e.ref.current=n);for(let e of(eo(r.object,r.props),r.props.attach?Q(a,r):el(r.object)&&el(a.object)&&a.object.add(r.object),r.children))e.props.attach?Q(r,e):el(e.object)&&el(r.object)&&r.object.add(e.object);ei(r)}}ez.length=0}()},finalizeInitialChildren:()=>!1,commitMount(){},getPublicInstance:e=>null==e?void 0:e.object,prepareForCommit:()=>null,preparePortalMount:e=>G(e.getState().scene,e,"",{}),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance:function(e){if(!e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?J(e.parent,e):el(e.object)&&(e.object.visible=!1),e.isHidden=!0,ei(e)}},unhideInstance:ek,createTextInstance:eD,hideTextInstance:eD,unhideTextInstance:eD,scheduleTimeout:"function"==typeof setTimeout?setTimeout:void 0,cancelTimeout:"function"==typeof clearTimeout?clearTimeout:void 0,noTimeout:-1,getInstanceFromNode:()=>null,beforeActiveInstanceBlur(){},afterActiveInstanceBlur(){},detachDeletedInstance(){},prepareScopeUpdate(){},getInstanceFromScope:()=>null,shouldAttemptEagerTransition:()=>!1,trackSchedulerEvent:()=>{},resolveEventType:()=>null,resolveEventTimeStamp:()=>-1.1,requestPostPaintCallback(){},maySuspendCommit:()=>!1,preloadInstance:()=>!0,startSuspendingCommit(){},suspendInstance(){},waitForCommitToBeReady:()=>null,NotPendingTransition:null,HostTransitionContext:u.createContext(null),setCurrentUpdatePriority(e){eF=e},getCurrentUpdatePriority:()=>eF,resolveUpdatePriority(){var e;if(0!==eF)return eF;switch("undefined"!=typeof window&&(null==(e=window.event)?void 0:e.type)){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return f.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return f.ContinuousEventPriority;default:return f.DefaultEventPriority}},resetFormInstance(){}}),eH=new Map,eY={objects:"shallow",strict:!1};function eU(e){let t,n,r=eH.get(e),o=null==r?void 0:r.fiber,i=null==r?void 0:r.store;r&&console.warn("R3F.createRoot should only be called once!");let a="function"==typeof reportError?reportError:console.error,l=i||ep(e8,e9),u=o||eq.createContainer(l,f.ConcurrentRoot,null,!1,null,"",a,a,a,null);r||eH.set(e,{fiber:u,store:l});let d=!1,p=null;return{async configure(r={}){var o,i;let a;p=new Promise(e=>a=e);let{gl:u,size:f,scene:m,events:h,onCreated:b,shadows:v=!1,linear:g=!1,flat:y=!1,legacy:w=!1,orthographic:j=!1,frameloop:P="always",dpr:_=[1,2],performance:E,raycaster:x,camera:O,onPointerMissed:M}=r,C=l.getState(),S=C.gl;if(!C.gl){let t={canvas:e,powerPreference:"high-performance",antialias:!0,alpha:!0},n="function"==typeof u?await u(t):u;S=ed(n)?n:new c.WebGLRenderer({...t,...u}),C.set({gl:S})}let k=C.raycaster;k||C.set({raycaster:k=new s.tBo});let{params:A,...T}=x||{};if(V.equ(T,k,eY)||eo(k,{...T}),V.equ(A,k.params,eY)||eo(k,{params:{...k.params,...A}}),!C.camera||C.camera===n&&!V.equ(n,O,eY)){n=O;let e=null==O?void 0:O.isCamera,t=e?O:j?new s.qUd(0,0,0,0,.1,1e3):new s.ubm(75,0,.1,1e3);!e&&(t.position.z=5,O&&(eo(t,O),!t.manual&&("aspect"in O||"left"in O||"right"in O||"bottom"in O||"top"in O)&&(t.manual=!0,t.updateProjectionMatrix())),C.camera||null!=O&&O.rotation||t.lookAt(0,0,0)),C.set({camera:t}),k.camera=t}if(!C.scene){let e;null!=m&&m.isScene?G(e=m,l,"",{}):(G(e=new s.Z58,l,"",{}),m&&eo(e,m)),C.set({scene:e})}h&&!C.events.handlers&&C.set({events:h(l)});let I=function(e,t){if(!t&&"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&e.parentElement){let{width:t,height:n,top:r,left:o}=e.parentElement.getBoundingClientRect();return{width:t,height:n,top:r,left:o}}return!t&&"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas?{width:e.width,height:e.height,top:0,left:0}:{width:0,height:0,top:0,left:0,...t}}(e,f);if(V.equ(I,C.size,eY)||C.setSize(I.width,I.height,I.top,I.left),_&&C.viewport.dpr!==U(_)&&C.setDpr(_),C.frameloop!==P&&C.setFrameloop(P),C.onPointerMissed||C.set({onPointerMissed:M}),E&&!V.equ(E,C.performance,eY)&&C.set(e=>({performance:{...e.performance,...E}})),!C.xr){let e=(e,t)=>{let n=l.getState();"never"!==n.frameloop&&e9(e,!0,n,t)},t=()=>{let t=l.getState();t.gl.xr.enabled=t.gl.xr.isPresenting,t.gl.xr.setAnimationLoop(t.gl.xr.isPresenting?e:null),t.gl.xr.isPresenting||e8(t)},n={connect(){let e=l.getState().gl;e.xr.addEventListener("sessionstart",t),e.xr.addEventListener("sessionend",t)},disconnect(){let e=l.getState().gl;e.xr.removeEventListener("sessionstart",t),e.xr.removeEventListener("sessionend",t)}};"function"==typeof(null==(o=S.xr)?void 0:o.addEventListener)&&n.connect(),C.set({xr:n})}if(S.shadowMap){let e=S.shadowMap.enabled,t=S.shadowMap.type;if(S.shadowMap.enabled=!!v,V.boo(v))S.shadowMap.type=s.Wk7;else if(V.str(v)){let e={basic:s.bTm,percentage:s.QP0,soft:s.Wk7,variance:s.RyA};S.shadowMap.type=null!=(i=e[v])?i:s.Wk7}else V.obj(v)&&Object.assign(S.shadowMap,v);(e!==S.shadowMap.enabled||t!==S.shadowMap.type)&&(S.shadowMap.needsUpdate=!0)}return s.ppV.enabled=!w,d||(S.outputColorSpace=g?s.Zr2:s.er$,S.toneMapping=y?s.y_p:s.FV),C.legacy!==w&&C.set(()=>({legacy:w})),C.linear!==g&&C.set(()=>({linear:g})),C.flat!==y&&C.set(()=>({flat:y})),!u||V.fun(u)||ed(u)||V.equ(u,S,eY)||eo(S,u),t=b,d=!0,a(),this},render(n){return d||p||this.configure(),p.then(()=>{eq.updateContainer((0,k.jsx)(eB,{store:l,children:n,onCreated:t,rootElement:e}),u,null,()=>void 0)}),l},unmount(){eV(e)}}}function eB({store:e,children:t,onCreated:n,rootElement:r}){return N(()=>{let t=e.getState();t.set(e=>({internal:{...e.internal,active:!0}})),n&&n(t),e.getState().events.connected||null==t.events.connect||t.events.connect(r)},[]),(0,k.jsx)(ef.Provider,{value:e,children:t})}function eV(e,t){let n=eH.get(e),r=null==n?void 0:n.fiber;if(r){let o=null==n?void 0:n.store.getState();o&&(o.internal.active=!1),eq.updateContainer(null,r,null,()=>{o&&setTimeout(()=>{try{var n,r,i,a;null==o.events.disconnect||o.events.disconnect(),null==(n=o.gl)||null==(r=n.renderLists)||null==r.dispose||r.dispose(),null==(i=o.gl)||null==i.forceContextLoss||i.forceContextLoss(),null!=(a=o.gl)&&a.xr&&o.xr.disconnect(),W(o.scene),eH.delete(e),t&&t(e)}catch(e){}},500)})}}function eX(e,t,n){return(0,k.jsx)(eW,{children:e,container:t,state:n})}function eW({state:e={},children:t,container:n}){let{events:r,size:o,...i}=e,a=eh(),[l]=u.useState(()=>new s.tBo),[c]=u.useState(()=>new s.I9Y),d=F((e,t)=>{let i;if(t.camera&&o){let n=t.camera;i=e.viewport.getCurrentViewport(n,new s.Pq0,o),n!==e.camera&&ea(n,o)}return{...e,...t,scene:n,raycaster:l,pointer:c,mouse:c,previousRoot:a,events:{...e.events,...t.events,...r},size:{...e.size,...o},viewport:{...e.viewport,...i},setEvents:e=>t.set(t=>({...t,events:{...t.events,...e}}))}}),f=u.useMemo(()=>{let e=y((e,t)=>({...i,set:e,get:t})),t=t=>e.setState(e=>d.current(t,e));return t(a.getState()),a.subscribe(t),e},[a,n]);return(0,k.jsx)(k.Fragment,{children:eq.createPortal((0,k.jsx)(ef.Provider,{value:f,children:t}),f,null)})}function eZ(e,t){let n={callback:e};return t.add(n),()=>void t.delete(n)}let eG=new Set,eK=new Set,e$=new Set,eQ=e=>eZ(e,eG),eJ=e=>eZ(e,eK),e0=e=>eZ(e,e$);function e1(e,t){if(e.size)for(let{callback:n}of e.values())n(t)}function e2(e,t){switch(e){case"before":return e1(eG,t);case"after":return e1(eK,t);case"tail":return e1(e$,t)}}function e3(e,t,n){let i=t.clock.getDelta();"never"===t.frameloop&&"number"==typeof e&&(i=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),r=t.internal.subscribers;for(let e=0;e<r.length;e++)(o=r[e]).ref.current(o.store.getState(),i,n);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}let e5=!1,e4=!1;function e6(e){for(let n of(a=requestAnimationFrame(e6),e5=!0,i=0,e2("before",e),e4=!0,eH.values())){var t;(l=n.store.getState()).internal.active&&("always"===l.frameloop||l.internal.frames>0)&&!(null!=(t=l.gl.xr)&&t.isPresenting)&&(i+=e3(e,l))}if(e4=!1,e2("after",e),0===i)return e2("tail",e),e5=!1,cancelAnimationFrame(a)}function e8(e,t=1){var n;if(!e)return eH.forEach(e=>e8(e.store.getState(),t));(null==(n=e.gl.xr)||!n.isPresenting)&&e.internal.active&&"never"!==e.frameloop&&(t>1?e.internal.frames=Math.min(60,e.internal.frames+t):e4?e.internal.frames=2:e.internal.frames=1,e5||(e5=!0,requestAnimationFrame(e6)))}function e9(e,t=!0,n,r){if(t&&e2("before",e),n)e3(e,n,r);else for(let t of eH.values())e3(e,t.store.getState());t&&e2("after",e)}let e7={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function te(e){let{handlePointer:t}=eu(e);return{priority:1,enabled:!0,compute(e,t,n){t.pointer.set(e.offsetX/t.size.width*2-1,-(2*(e.offsetY/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)},connected:void 0,handlers:Object.keys(e7).reduce((e,n)=>({...e,[n]:t(n)}),{}),update:()=>{var t;let{events:n,internal:r}=e.getState();null!=(t=r.lastEvent)&&t.current&&n.handlers&&n.handlers.onPointerMove(r.lastEvent.current)},connect:t=>{let{set:n,events:r}=e.getState();if(null==r.disconnect||r.disconnect(),n(e=>({events:{...e.events,connected:t}})),r.handlers)for(let e in r.handlers){let n=r.handlers[e],[o,i]=e7[e];t.addEventListener(o,n,{passive:i})}},disconnect:()=>{let{set:t,events:n}=e.getState();if(n.connected){if(n.handlers)for(let e in n.handlers){let t=n.handlers[e],[r]=e7[e];n.connected.removeEventListener(r,t)}t(e=>({events:{...e.events,connected:void 0}}))}}}}},4054:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{bindSnapshot:function(){return a},createAsyncLocalStorage:function(){return i},createSnapshot:function(){return l}});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class r{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let o="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function i(){return o?new o:new r}function a(e){return o?o.bind(e):r.bind(e)}function l(){return o?o.snapshot():function(e,...t){return e(...t)}}},4342:(e,t,n)=>{e.exports=n(7319)},4932:(e,t,n)=>{function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}n.d(t,{N:()=>y});var o=n(3816),i=n(2115),a=n(3264),l=Object.defineProperty,s=(e,t,n)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,c=(e,t,n)=>(s(e,"symbol"!=typeof t?t+"":t,n),n);class u{constructor(){c(this,"_listeners")}addEventListener(e,t){void 0===this._listeners&&(this._listeners={});let n=this._listeners;void 0===n[e]&&(n[e]=[]),-1===n[e].indexOf(t)&&n[e].push(t)}hasEventListener(e,t){if(void 0===this._listeners)return!1;let n=this._listeners;return void 0!==n[e]&&-1!==n[e].indexOf(t)}removeEventListener(e,t){if(void 0===this._listeners)return;let n=this._listeners[e];if(void 0!==n){let e=n.indexOf(t);-1!==e&&n.splice(e,1)}}dispatchEvent(e){if(void 0===this._listeners)return;let t=this._listeners[e.type];if(void 0!==t){e.target=this;let n=t.slice(0);for(let t=0,r=n.length;t<r;t++)n[t].call(this,e);e.target=null}}}var d=Object.defineProperty,f=(e,t,n)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p=(e,t,n)=>(f(e,"symbol"!=typeof t?t+"":t,n),n);let m=new a.RlV,h=new a.Zcv,b=Math.cos(Math.PI/180*70),v=(e,t)=>(e%t+t)%t;class g extends u{constructor(e,t){super(),p(this,"object"),p(this,"domElement"),p(this,"enabled",!0),p(this,"target",new a.Pq0),p(this,"minDistance",0),p(this,"maxDistance",1/0),p(this,"minZoom",0),p(this,"maxZoom",1/0),p(this,"minPolarAngle",0),p(this,"maxPolarAngle",Math.PI),p(this,"minAzimuthAngle",-1/0),p(this,"maxAzimuthAngle",1/0),p(this,"enableDamping",!1),p(this,"dampingFactor",.05),p(this,"enableZoom",!0),p(this,"zoomSpeed",1),p(this,"enableRotate",!0),p(this,"rotateSpeed",1),p(this,"enablePan",!0),p(this,"panSpeed",1),p(this,"screenSpacePanning",!0),p(this,"keyPanSpeed",7),p(this,"zoomToCursor",!1),p(this,"autoRotate",!1),p(this,"autoRotateSpeed",2),p(this,"reverseOrbit",!1),p(this,"reverseHorizontalOrbit",!1),p(this,"reverseVerticalOrbit",!1),p(this,"keys",{LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"}),p(this,"mouseButtons",{LEFT:a.kBv.ROTATE,MIDDLE:a.kBv.DOLLY,RIGHT:a.kBv.PAN}),p(this,"touches",{ONE:a.wtR.ROTATE,TWO:a.wtR.DOLLY_PAN}),p(this,"target0"),p(this,"position0"),p(this,"zoom0"),p(this,"_domElementKeyEvents",null),p(this,"getPolarAngle"),p(this,"getAzimuthalAngle"),p(this,"setPolarAngle"),p(this,"setAzimuthalAngle"),p(this,"getDistance"),p(this,"getZoomScale"),p(this,"listenToKeyEvents"),p(this,"stopListenToKeyEvents"),p(this,"saveState"),p(this,"reset"),p(this,"update"),p(this,"connect"),p(this,"dispose"),p(this,"dollyIn"),p(this,"dollyOut"),p(this,"getScale"),p(this,"setScale"),this.object=e,this.domElement=t,this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this.getPolarAngle=()=>u.phi,this.getAzimuthalAngle=()=>u.theta,this.setPolarAngle=e=>{let t=v(e,2*Math.PI),r=u.phi;r<0&&(r+=2*Math.PI),t<0&&(t+=2*Math.PI);let o=Math.abs(t-r);2*Math.PI-o<o&&(t<r?t+=2*Math.PI:r+=2*Math.PI),d.phi=t-r,n.update()},this.setAzimuthalAngle=e=>{let t=v(e,2*Math.PI),r=u.theta;r<0&&(r+=2*Math.PI),t<0&&(t+=2*Math.PI);let o=Math.abs(t-r);2*Math.PI-o<o&&(t<r?t+=2*Math.PI:r+=2*Math.PI),d.theta=t-r,n.update()},this.getDistance=()=>n.object.position.distanceTo(n.target),this.listenToKeyEvents=e=>{e.addEventListener("keydown",ee),this._domElementKeyEvents=e},this.stopListenToKeyEvents=()=>{this._domElementKeyEvents.removeEventListener("keydown",ee),this._domElementKeyEvents=null},this.saveState=()=>{n.target0.copy(n.target),n.position0.copy(n.object.position),n.zoom0=n.object.zoom},this.reset=()=>{n.target.copy(n.target0),n.object.position.copy(n.position0),n.object.zoom=n.zoom0,n.object.updateProjectionMatrix(),n.dispatchEvent(r),n.update(),s=l.NONE},this.update=(()=>{let t=new a.Pq0,o=new a.Pq0(0,1,0),i=new a.PTz().setFromUnitVectors(e.up,o),p=i.clone().invert(),v=new a.Pq0,y=new a.PTz,w=2*Math.PI;return function(){let j=n.object.position;i.setFromUnitVectors(e.up,o),p.copy(i).invert(),t.copy(j).sub(n.target),t.applyQuaternion(i),u.setFromVector3(t),n.autoRotate&&s===l.NONE&&R(2*Math.PI/60/60*n.autoRotateSpeed),n.enableDamping?(u.theta+=d.theta*n.dampingFactor,u.phi+=d.phi*n.dampingFactor):(u.theta+=d.theta,u.phi+=d.phi);let P=n.minAzimuthAngle,_=n.maxAzimuthAngle;isFinite(P)&&isFinite(_)&&(P<-Math.PI?P+=w:P>Math.PI&&(P-=w),_<-Math.PI?_+=w:_>Math.PI&&(_-=w),P<=_?u.theta=Math.max(P,Math.min(_,u.theta)):u.theta=u.theta>(P+_)/2?Math.max(P,u.theta):Math.min(_,u.theta)),u.phi=Math.max(n.minPolarAngle,Math.min(n.maxPolarAngle,u.phi)),u.makeSafe(),!0===n.enableDamping?n.target.addScaledVector(g,n.dampingFactor):n.target.add(g),n.zoomToCursor&&k||n.object.isOrthographicCamera?u.radius=H(u.radius):u.radius=H(u.radius*f),t.setFromSpherical(u),t.applyQuaternion(p),j.copy(n.target).add(t),n.object.matrixAutoUpdate||n.object.updateMatrix(),n.object.lookAt(n.target),!0===n.enableDamping?(d.theta*=1-n.dampingFactor,d.phi*=1-n.dampingFactor,g.multiplyScalar(1-n.dampingFactor)):(d.set(0,0,0),g.set(0,0,0));let E=!1;if(n.zoomToCursor&&k){let r=null;if(n.object instanceof a.ubm&&n.object.isPerspectiveCamera){let e=t.length();r=H(e*f);let o=e-r;n.object.position.addScaledVector(C,o),n.object.updateMatrixWorld()}else if(n.object.isOrthographicCamera){let e=new a.Pq0(S.x,S.y,0);e.unproject(n.object),n.object.zoom=Math.max(n.minZoom,Math.min(n.maxZoom,n.object.zoom/f)),n.object.updateProjectionMatrix(),E=!0;let o=new a.Pq0(S.x,S.y,0);o.unproject(n.object),n.object.position.sub(o).add(e),n.object.updateMatrixWorld(),r=t.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),n.zoomToCursor=!1;null!==r&&(n.screenSpacePanning?n.target.set(0,0,-1).transformDirection(n.object.matrix).multiplyScalar(r).add(n.object.position):(m.origin.copy(n.object.position),m.direction.set(0,0,-1).transformDirection(n.object.matrix),Math.abs(n.object.up.dot(m.direction))<b?e.lookAt(n.target):(h.setFromNormalAndCoplanarPoint(n.object.up,n.target),m.intersectPlane(h,n.target))))}else n.object instanceof a.qUd&&n.object.isOrthographicCamera&&(E=1!==f)&&(n.object.zoom=Math.max(n.minZoom,Math.min(n.maxZoom,n.object.zoom/f)),n.object.updateProjectionMatrix());return f=1,k=!1,!!(E||v.distanceToSquared(n.object.position)>c||8*(1-y.dot(n.object.quaternion))>c)&&(n.dispatchEvent(r),v.copy(n.object.position),y.copy(n.object.quaternion),E=!1,!0)}})(),this.connect=e=>{n.domElement=e,n.domElement.style.touchAction="none",n.domElement.addEventListener("contextmenu",et),n.domElement.addEventListener("pointerdown",K),n.domElement.addEventListener("pointercancel",Q),n.domElement.addEventListener("wheel",J)},this.dispose=()=>{var e,t,r,o,i,a;n.domElement&&(n.domElement.style.touchAction="auto"),null==(e=n.domElement)||e.removeEventListener("contextmenu",et),null==(t=n.domElement)||t.removeEventListener("pointerdown",K),null==(r=n.domElement)||r.removeEventListener("pointercancel",Q),null==(o=n.domElement)||o.removeEventListener("wheel",J),null==(i=n.domElement)||i.ownerDocument.removeEventListener("pointermove",$),null==(a=n.domElement)||a.ownerDocument.removeEventListener("pointerup",Q),null!==n._domElementKeyEvents&&n._domElementKeyEvents.removeEventListener("keydown",ee)};let n=this,r={type:"change"},o={type:"start"},i={type:"end"},l={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6},s=l.NONE,c=1e-6,u=new a.YHV,d=new a.YHV,f=1,g=new a.Pq0,y=new a.I9Y,w=new a.I9Y,j=new a.I9Y,P=new a.I9Y,_=new a.I9Y,E=new a.I9Y,x=new a.I9Y,O=new a.I9Y,M=new a.I9Y,C=new a.Pq0,S=new a.I9Y,k=!1,A=[],T={};function I(){return Math.pow(.95,n.zoomSpeed)}function R(e){n.reverseOrbit||n.reverseHorizontalOrbit?d.theta+=e:d.theta-=e}function L(e){n.reverseOrbit||n.reverseVerticalOrbit?d.phi+=e:d.phi-=e}let z=(()=>{let e=new a.Pq0;return function(t,n){e.setFromMatrixColumn(n,0),e.multiplyScalar(-t),g.add(e)}})(),D=(()=>{let e=new a.Pq0;return function(t,r){!0===n.screenSpacePanning?e.setFromMatrixColumn(r,1):(e.setFromMatrixColumn(r,0),e.crossVectors(n.object.up,e)),e.multiplyScalar(t),g.add(e)}})(),N=(()=>{let e=new a.Pq0;return function(t,r){let o=n.domElement;if(o&&n.object instanceof a.ubm&&n.object.isPerspectiveCamera){let i=n.object.position;e.copy(i).sub(n.target);let a=e.length();z(2*t*(a*=Math.tan(n.object.fov/2*Math.PI/180))/o.clientHeight,n.object.matrix),D(2*r*a/o.clientHeight,n.object.matrix)}else o&&n.object instanceof a.qUd&&n.object.isOrthographicCamera?(z(t*(n.object.right-n.object.left)/n.object.zoom/o.clientWidth,n.object.matrix),D(r*(n.object.top-n.object.bottom)/n.object.zoom/o.clientHeight,n.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),n.enablePan=!1)}})();function F(e){n.object instanceof a.ubm&&n.object.isPerspectiveCamera||n.object instanceof a.qUd&&n.object.isOrthographicCamera?f=e:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),n.enableZoom=!1)}function q(e){if(!n.zoomToCursor||!n.domElement)return;k=!0;let t=n.domElement.getBoundingClientRect(),r=e.clientX-t.left,o=e.clientY-t.top,i=t.width,a=t.height;S.x=r/i*2-1,S.y=-(o/a*2)+1,C.set(S.x,S.y,1).unproject(n.object).sub(n.object.position).normalize()}function H(e){return Math.max(n.minDistance,Math.min(n.maxDistance,e))}function Y(e){y.set(e.clientX,e.clientY)}function U(e){P.set(e.clientX,e.clientY)}function B(){if(1==A.length)y.set(A[0].pageX,A[0].pageY);else{let e=.5*(A[0].pageX+A[1].pageX),t=.5*(A[0].pageY+A[1].pageY);y.set(e,t)}}function V(){if(1==A.length)P.set(A[0].pageX,A[0].pageY);else{let e=.5*(A[0].pageX+A[1].pageX),t=.5*(A[0].pageY+A[1].pageY);P.set(e,t)}}function X(){let e=A[0].pageX-A[1].pageX,t=A[0].pageY-A[1].pageY,n=Math.sqrt(e*e+t*t);x.set(0,n)}function W(e){if(1==A.length)w.set(e.pageX,e.pageY);else{let t=er(e),n=.5*(e.pageX+t.x),r=.5*(e.pageY+t.y);w.set(n,r)}j.subVectors(w,y).multiplyScalar(n.rotateSpeed);let t=n.domElement;t&&(R(2*Math.PI*j.x/t.clientHeight),L(2*Math.PI*j.y/t.clientHeight)),y.copy(w)}function Z(e){if(1==A.length)_.set(e.pageX,e.pageY);else{let t=er(e),n=.5*(e.pageX+t.x),r=.5*(e.pageY+t.y);_.set(n,r)}E.subVectors(_,P).multiplyScalar(n.panSpeed),N(E.x,E.y),P.copy(_)}function G(e){var t;let r=er(e),o=e.pageX-r.x,i=e.pageY-r.y,a=Math.sqrt(o*o+i*i);O.set(0,a),M.set(0,Math.pow(O.y/x.y,n.zoomSpeed)),t=M.y,F(f/t),x.copy(O)}function K(e){var t,r,i;!1!==n.enabled&&(0===A.length&&(null==(t=n.domElement)||t.ownerDocument.addEventListener("pointermove",$),null==(r=n.domElement)||r.ownerDocument.addEventListener("pointerup",Q)),i=e,A.push(i),"touch"===e.pointerType?function(e){switch(en(e),A.length){case 1:switch(n.touches.ONE){case a.wtR.ROTATE:if(!1===n.enableRotate)return;B(),s=l.TOUCH_ROTATE;break;case a.wtR.PAN:if(!1===n.enablePan)return;V(),s=l.TOUCH_PAN;break;default:s=l.NONE}break;case 2:switch(n.touches.TWO){case a.wtR.DOLLY_PAN:if(!1===n.enableZoom&&!1===n.enablePan)return;n.enableZoom&&X(),n.enablePan&&V(),s=l.TOUCH_DOLLY_PAN;break;case a.wtR.DOLLY_ROTATE:if(!1===n.enableZoom&&!1===n.enableRotate)return;n.enableZoom&&X(),n.enableRotate&&B(),s=l.TOUCH_DOLLY_ROTATE;break;default:s=l.NONE}break;default:s=l.NONE}s!==l.NONE&&n.dispatchEvent(o)}(e):function(e){let t;switch(e.button){case 0:t=n.mouseButtons.LEFT;break;case 1:t=n.mouseButtons.MIDDLE;break;case 2:t=n.mouseButtons.RIGHT;break;default:t=-1}switch(t){case a.kBv.DOLLY:if(!1===n.enableZoom)return;q(e),x.set(e.clientX,e.clientY),s=l.DOLLY;break;case a.kBv.ROTATE:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===n.enablePan)return;U(e),s=l.PAN}else{if(!1===n.enableRotate)return;Y(e),s=l.ROTATE}break;case a.kBv.PAN:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===n.enableRotate)return;Y(e),s=l.ROTATE}else{if(!1===n.enablePan)return;U(e),s=l.PAN}break;default:s=l.NONE}s!==l.NONE&&n.dispatchEvent(o)}(e))}function $(e){!1!==n.enabled&&("touch"===e.pointerType?function(e){switch(en(e),s){case l.TOUCH_ROTATE:if(!1===n.enableRotate)return;W(e),n.update();break;case l.TOUCH_PAN:if(!1===n.enablePan)return;Z(e),n.update();break;case l.TOUCH_DOLLY_PAN:if(!1===n.enableZoom&&!1===n.enablePan)return;n.enableZoom&&G(e),n.enablePan&&Z(e),n.update();break;case l.TOUCH_DOLLY_ROTATE:if(!1===n.enableZoom&&!1===n.enableRotate)return;n.enableZoom&&G(e),n.enableRotate&&W(e),n.update();break;default:s=l.NONE}}(e):function(e){if(!1!==n.enabled)switch(s){case l.ROTATE:if(!1===n.enableRotate)return;w.set(e.clientX,e.clientY),j.subVectors(w,y).multiplyScalar(n.rotateSpeed);let t=n.domElement;t&&(R(2*Math.PI*j.x/t.clientHeight),L(2*Math.PI*j.y/t.clientHeight)),y.copy(w),n.update();break;case l.DOLLY:var r,o;if(!1===n.enableZoom)return;(O.set(e.clientX,e.clientY),M.subVectors(O,x),M.y>0)?(r=I(),F(f/r)):M.y<0&&(o=I(),F(f*o)),x.copy(O),n.update();break;case l.PAN:if(!1===n.enablePan)return;_.set(e.clientX,e.clientY),E.subVectors(_,P).multiplyScalar(n.panSpeed),N(E.x,E.y),P.copy(_),n.update()}}(e))}function Q(e){var t,r,o;(function(e){delete T[e.pointerId];for(let t=0;t<A.length;t++)if(A[t].pointerId==e.pointerId)return void A.splice(t,1)})(e),0===A.length&&(null==(t=n.domElement)||t.releasePointerCapture(e.pointerId),null==(r=n.domElement)||r.ownerDocument.removeEventListener("pointermove",$),null==(o=n.domElement)||o.ownerDocument.removeEventListener("pointerup",Q)),n.dispatchEvent(i),s=l.NONE}function J(e){if(!1!==n.enabled&&!1!==n.enableZoom&&(s===l.NONE||s===l.ROTATE)){var t,r;e.preventDefault(),n.dispatchEvent(o),(q(e),e.deltaY<0)?(t=I(),F(f*t)):e.deltaY>0&&(r=I(),F(f/r)),n.update(),n.dispatchEvent(i)}}function ee(e){if(!1!==n.enabled&&!1!==n.enablePan){let t=!1;switch(e.code){case n.keys.UP:N(0,n.keyPanSpeed),t=!0;break;case n.keys.BOTTOM:N(0,-n.keyPanSpeed),t=!0;break;case n.keys.LEFT:N(n.keyPanSpeed,0),t=!0;break;case n.keys.RIGHT:N(-n.keyPanSpeed,0),t=!0}t&&(e.preventDefault(),n.update())}}function et(e){!1!==n.enabled&&e.preventDefault()}function en(e){let t=T[e.pointerId];void 0===t&&(t=new a.I9Y,T[e.pointerId]=t),t.set(e.pageX,e.pageY)}function er(e){return T[(e.pointerId===A[0].pointerId?A[1]:A[0]).pointerId]}this.dollyIn=(e=I())=>{F(f*e),n.update()},this.dollyOut=(e=I())=>{F(f/e),n.update()},this.getScale=()=>f,this.setScale=e=>{F(e),n.update()},this.getZoomScale=()=>I(),void 0!==t&&this.connect(t),this.update()}}let y=i.forwardRef(({makeDefault:e,camera:t,regress:n,domElement:a,enableDamping:l=!0,keyEvents:s=!1,onChange:c,onStart:u,onEnd:d,...f},p)=>{let m=(0,o.A)(e=>e.invalidate),h=(0,o.A)(e=>e.camera),b=(0,o.A)(e=>e.gl),v=(0,o.A)(e=>e.events),y=(0,o.A)(e=>e.setEvents),w=(0,o.A)(e=>e.set),j=(0,o.A)(e=>e.get),P=(0,o.A)(e=>e.performance),_=t||h,E=a||v.connected||b.domElement,x=i.useMemo(()=>new g(_),[_]);return(0,o.C)(()=>{x.enabled&&x.update()},-1),i.useEffect(()=>(s&&x.connect(!0===s?E:s),x.connect(E),()=>void x.dispose()),[s,E,n,x,m]),i.useEffect(()=>{let e=e=>{m(),n&&P.regress(),c&&c(e)},t=e=>{u&&u(e)},r=e=>{d&&d(e)};return x.addEventListener("change",e),x.addEventListener("start",t),x.addEventListener("end",r),()=>{x.removeEventListener("start",t),x.removeEventListener("end",r),x.removeEventListener("change",e)}},[c,u,d,x,m,y]),i.useEffect(()=>{if(e){let e=j().controls;return w({controls:x}),()=>w({controls:e})}},[e,x]),i.createElement("primitive",r({ref:p,object:x,enableDamping:l},f))})},5028:(e,t,n)=>{n.d(t,{default:()=>o.a});var r=n(6645),o=n.n(r)},5029:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=n(2115),o=r.useLayoutEffect,i=r.useEffect;function a(e){let{headManager:t,reduceComponentsToState:n}=e;function a(){if(t&&t.mountedInstances){let o=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(o,e))}}return o(()=>{var n;return null==t||null==(n=t.mountedInstances)||n.add(e.children),()=>{var n;null==t||null==(n=t.mountedInstances)||n.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=a),()=>{t&&(t._pendingUpdate=a)})),i(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{function n(e){let{widthInt:t,heightInt:n,blurWidth:r,blurHeight:o,blurDataURL:i,objectFit:a}=e,l=r?40*r:t,s=o?40*o:n,c=l&&s?"viewBox='0 0 "+l+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},5220:(e,t,n)=>{e.exports=n(1724)},5273:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},5564:(e,t,n)=>{var r=n(9538);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return b},defaultHead:function(){return f}});let o=n(8229),i=n(6966),a=n(5155),l=i._(n(2115)),s=o._(n(5029)),c=n(2464),u=n(2830),d=n(7544);function f(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===l.default.Fragment?e.concat(l.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(3230);let m=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:n}=t;return e.reduce(p,[]).reverse().concat(f(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,r={};return o=>{let i=!0,a=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){a=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?i=!1:t.add(o.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(o.props.hasOwnProperty(t))if("charSet"===t)n.has(t)?i=!1:n.add(t);else{let e=o.props[t],n=r[t]||new Set;("name"!==t||!a)&&n.has(e)?i=!1:(n.add(e),r[t]=n)}}}return i}}()).reverse().map((e,t)=>{let o=e.key||t;if(r.env.__NEXT_OPTIMIZE_FONTS&&!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,l.default.cloneElement(e,t)}return l.default.cloneElement(e,{key:o})})}let b=function(e){let{children:t}=e,n=(0,l.useContext)(c.AmpStateContext),r=(0,l.useContext)(u.HeadManagerContext);return(0,a.jsx)(s.default,{reduceComponentsToState:h,headManager:r,inAmpMode:(0,d.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5643:(e,t,n)=>{e.exports=n(6115)},5690:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},5744:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return r.workAsyncStorageInstance}});let r=n(7828)},5840:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return r}});let n=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6115:(e,t,n)=>{var r=n(2115),o=n(1414),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=l(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=a(e,(d=c(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,l=n)}var a,l,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,n,r,o]))[0],d[1]);return s(function(){f.hasValue=!0,f.value=p},[p]),u(p),p}},6354:(e,t,n)=>{n.d(t,{Af:()=>l,Nz:()=>o,u5:()=>s,y3:()=>d});var r=n(2115);function o(e,t,n){if(!e)return;if(!0===n(e))return e;let r=t?e.return:e.child;for(;r;){let e=o(r,t,n);if(e)return e;r=t?null:r.sibling}}function i(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}(()=>{var e,t;return"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative")})()?r.useLayoutEffect:r.useEffect;let a=i(r.createContext(null));class l extends r.Component{render(){return r.createElement(a.Provider,{value:this._reactInternals},this.props.children)}}function s(){let e=r.useContext(a);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=r.useId();return r.useMemo(()=>{for(let n of[e,null==e?void 0:e.alternate]){if(!n)continue;let e=o(n,!1,e=>{let n=e.memoizedState;for(;n;){if(n.memoizedState===t)return!0;n=n.next}});if(e)return e}},[e,t])}let c=Symbol.for("react.context"),u=e=>null!==e&&"object"==typeof e&&"$$typeof"in e&&e.$$typeof===c;function d(){let e=function(){let e=s(),[t]=r.useState(()=>new Map);t.clear();let n=e;for(;n;){let e=n.type;u(e)&&e!==a&&!t.has(e)&&t.set(e,r.use(i(e))),n=n.return}return t}();return r.useMemo(()=>Array.from(e.keys()).reduce((t,n)=>o=>r.createElement(t,null,r.createElement(n.Provider,{...o,value:e.get(n)})),e=>r.createElement(l,{...e})),[e])}},6500:(e,t)=>{t.ConcurrentRoot=1,t.ContinuousEventPriority=8,t.DefaultEventPriority=32,t.DiscreteEventPriority=2},6645:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let r=n(8229)._(n(7357));function o(e,t){var n;let o={};"function"==typeof e&&(o.loader=e);let i={...o,...t};return(0,r.default)({...i,modules:null==(n=i.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return i}});let r=n(8229)._(n(2115)),o=n(5840),i=r.default.createContext(o.imageConfigDefault)},6766:(e,t,n)=>{n.d(t,{default:()=>o.a});var r=n(1469),o=n.n(r)},7319:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],d=[],f=1,p=null,m=3,h=!1,b=!1,v=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(d);null!==t;){if(null===t.callback)o(d);else if(t.startTime<=e)o(d),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(d)}}function P(e){if(v=!1,j(e),!b)if(null!==r(u))b=!0,A();else{var t=r(d);null!==t&&T(P,t.startTime-e)}}var _=!1,E=-1,x=5,O=-1;function M(){return!(t.unstable_now()-O<x)}function C(){if(_){var e=t.unstable_now();O=e;var n=!0;try{e:{b=!1,v&&(v=!1,y(E),E=-1),h=!0;var i=m;try{t:{for(j(e),p=r(u);null!==p&&!(p.expirationTime>e&&M());){var l=p.callback;if("function"==typeof l){p.callback=null,m=p.priorityLevel;var s=l(p.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){p.callback=s,j(e),n=!0;break t}p===r(u)&&o(u),j(e)}else o(u);p=r(u)}if(null!==p)n=!0;else{var c=r(d);null!==c&&T(P,c.startTime-e),n=!1}}break e}finally{p=null,m=i,h=!1}}}finally{n?a():_=!1}}}if("function"==typeof w)a=function(){w(C)};else if("undefined"!=typeof MessageChannel){var S=new MessageChannel,k=S.port2;S.port1.onmessage=C,a=function(){k.postMessage(null)}}else a=function(){g(C,0)};function A(){_||(_=!0,a())}function T(e,n){E=g(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){b||h||(b=!0,A())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):x=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return m},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:f++,callback:o,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>a?(e.sortIndex=i,n(d,e),null===r(u)&&e===r(d)&&(v?(y(E),E=-1):v=!0,T(P,i-a))):(e.sortIndex=l,n(u,e),b||h||(b=!0,A())),e},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}},7357:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let r=n(5155),o=n(2115),i=n(2146);function a(e){return{default:e&&"default"in e?e.default:e}}n(255);let l={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},s=function(e){let t={...l,...e},n=(0,o.lazy)(()=>t.loader().then(a)),s=t.loading;function c(e){let a=s?(0,r.jsx)(s,{isLoading:!0,pastDelay:!0,error:null}):null,l=!t.ssr||!!t.loading,c=l?o.Suspense:o.Fragment,u=t.ssr?(0,r.jsxs)(r.Fragment,{children:[null,(0,r.jsx)(n,{...e})]}):(0,r.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(n,{...e})});return(0,r.jsx)(c,{...l?{fallback:a}:{},children:u})}return c.displayName="LoadableComponent",c}},7544:(e,t)=>{function n(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||n&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return n}})},7828:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,n(4054).createAsyncLocalStorage)()},7918:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]])},8247:(e,t,n)=>{e.exports=n(620)},8883:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),n(3230);let r=n(5100),o=n(5840),i=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var n,s;let c,u,d,{src:f,sizes:p,unoptimized:m=!1,priority:h=!1,loading:b,className:v,quality:g,width:y,height:w,fill:j=!1,style:P,overrideSrc:_,onLoad:E,onLoadingComplete:x,placeholder:O="empty",blurDataURL:M,fetchPriority:C,decoding:S="async",layout:k,objectFit:A,objectPosition:T,lazyBoundary:I,lazyRoot:R,...L}=e,{imgConf:z,showAltText:D,blurComplete:N,defaultLoader:F}=t,q=z||o.imageConfigDefault;if("allSizes"in q)c=q;else{let e=[...q.deviceSizes,...q.imageSizes].sort((e,t)=>e-t),t=q.deviceSizes.sort((e,t)=>e-t),r=null==(n=q.qualities)?void 0:n.sort((e,t)=>e-t);c={...q,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===F)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let H=L.loader||F;delete L.loader,delete L.srcSet;let Y="__next_img_default"in H;if(Y){if("custom"===c.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=H;H=t=>{let{config:n,...r}=t;return e(r)}}if(k){"fill"===k&&(j=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[k];e&&(P={...P,...e});let t={responsive:"100vw",fill:"100vw"}[k];t&&!p&&(p=t)}let U="",B=l(y),V=l(w);if((s=f)&&"object"==typeof s&&(a(s)||void 0!==s.src)){let e=a(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(u=e.blurWidth,d=e.blurHeight,M=M||e.blurDataURL,U=e.src,!j)if(B||V){if(B&&!V){let t=B/e.width;V=Math.round(e.height*t)}else if(!B&&V){let t=V/e.height;B=Math.round(e.width*t)}}else B=e.width,V=e.height}let X=!h&&("lazy"===b||void 0===b);(!(f="string"==typeof f?f:U)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,X=!1),c.unoptimized&&(m=!0),Y&&!c.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let W=l(g),Z=Object.assign(j?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:A,objectPosition:T}:{},D?{}:{color:"transparent"},P),G=N||"empty"===O?null:"blur"===O?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:B,heightInt:V,blurWidth:u,blurHeight:d,blurDataURL:M||"",objectFit:Z.objectFit})+'")':'url("'+O+'")',K=i.includes(Z.objectFit)?"fill"===Z.objectFit?"100% 100%":"cover":Z.objectFit,$=G?{backgroundSize:K,backgroundPosition:Z.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:G}:{},Q=function(e){let{config:t,src:n,unoptimized:r,width:o,quality:i,sizes:a,loader:l}=e;if(r)return{src:n,srcSet:void 0,sizes:void 0};let{widths:s,kind:c}=function(e,t,n){let{deviceSizes:r,allSizes:o}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(n);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,a),u=s.length-1;return{sizes:a||"w"!==c?a:"100vw",srcSet:s.map((e,r)=>l({config:t,src:n,quality:i,width:e})+" "+("w"===c?e:r+1)+c).join(", "),src:l({config:t,src:n,quality:i,width:s[u]})}}({config:c,src:f,unoptimized:m,width:B,quality:W,sizes:p,loader:H});return{props:{...L,loading:X?"lazy":b,fetchPriority:C,width:B,height:V,decoding:S,className:v,style:{...Z,...$},sizes:Q.sizes,srcSet:Q.srcSet,src:_||Q.src},meta:{unoptimized:m,priority:h,placeholder:O,fill:j}}}}}]);