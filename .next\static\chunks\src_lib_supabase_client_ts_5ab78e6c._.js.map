{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/lib/supabase/client.ts"], "sourcesContent": ["import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';\nimport { createClient as createBrowserClient } from '@supabase/supabase-js';\nimport { createClient as createSSRClient } from '@/utils/supabase/client';\nimport type { Database } from '@/types/supabase';\n\n// Client-side Supabase client for use in components (legacy)\nexport const supabase = createClientComponentClient<Database>();\n\n// New SSR-compatible client (recommended)\nexport const createClient = () => createSSRClient();\n\n// Alternative client creation for specific use cases\nexport function createSupabaseClient() {\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n  if (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n  }\n\n  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {\n    auth: {\n      persistSession: true,\n      autoRefreshToken: true,\n      detectSessionInUrl: true,\n    },\n  });\n}\n\n// Storage bucket names\nexport const STORAGE_BUCKETS = {\n  MEDIA: 'media',\n  AVATARS: 'avatars',\n  THUMBNAILS: 'thumbnails',\n  TEMP: 'temp',\n} as const;\n\n// Helper functions for storage operations\nexport const storage = {\n  /**\n   * Upload file to storage bucket\n   */\n  async uploadFile(\n    bucket: string,\n    path: string,\n    file: File,\n    options?: {\n      cacheControl?: string;\n      contentType?: string;\n      upsert?: boolean;\n    }\n  ) {\n    const { data, error } = await supabase.storage\n      .from(bucket)\n      .upload(path, file, {\n        cacheControl: options?.cacheControl || '3600',\n        contentType: options?.contentType || file.type,\n        upsert: options?.upsert || false,\n      });\n\n    if (error) {\n      throw new Error(`Upload failed: ${error.message}`);\n    }\n\n    return data;\n  },\n\n  /**\n   * Get public URL for a file\n   */\n  getPublicUrl(bucket: string, path: string) {\n    const { data } = supabase.storage\n      .from(bucket)\n      .getPublicUrl(path);\n\n    return data.publicUrl;\n  },\n\n  /**\n   * Create signed URL for private files\n   */\n  async createSignedUrl(\n    bucket: string,\n    path: string,\n    expiresIn: number = 3600\n  ) {\n    const { data, error } = await supabase.storage\n      .from(bucket)\n      .createSignedUrl(path, expiresIn);\n\n    if (error) {\n      throw new Error(`Failed to create signed URL: ${error.message}`);\n    }\n\n    return data.signedUrl;\n  },\n\n  /**\n   * Delete file from storage\n   */\n  async deleteFile(bucket: string, path: string) {\n    const { error } = await supabase.storage\n      .from(bucket)\n      .remove([path]);\n\n    if (error) {\n      throw new Error(`Delete failed: ${error.message}`);\n    }\n  },\n\n  /**\n   * List files in a bucket\n   */\n  async listFiles(bucket: string, path?: string) {\n    const { data, error } = await supabase.storage\n      .from(bucket)\n      .list(path);\n\n    if (error) {\n      throw new Error(`List failed: ${error.message}`);\n    }\n\n    return data;\n  },\n\n  /**\n   * Move file within storage\n   */\n  async moveFile(\n    bucket: string,\n    fromPath: string,\n    toPath: string\n  ) {\n    const { error } = await supabase.storage\n      .from(bucket)\n      .move(fromPath, toPath);\n\n    if (error) {\n      throw new Error(`Move failed: ${error.message}`);\n    }\n  },\n\n  /**\n   * Copy file within storage\n   */\n  async copyFile(\n    bucket: string,\n    fromPath: string,\n    toPath: string\n  ) {\n    const { error } = await supabase.storage\n      .from(bucket)\n      .copy(fromPath, toPath);\n\n    if (error) {\n      throw new Error(`Copy failed: ${error.message}`);\n    }\n  },\n};\n\n// Helper functions for database operations\nexport const db = {\n  /**\n   * Generic select with error handling\n   */\n  async select<T>(\n    table: string,\n    columns?: string,\n    filters?: Record<string, any>\n  ): Promise<T[]> {\n    let query = supabase.from(table).select(columns || '*');\n\n    if (filters) {\n      Object.entries(filters).forEach(([key, value]) => {\n        query = query.eq(key, value);\n      });\n    }\n\n    const { data, error } = await query;\n\n    if (error) {\n      throw new Error(`Select failed: ${error.message}`);\n    }\n\n    return data as T[];\n  },\n\n  /**\n   * Generic insert with error handling\n   */\n  async insert<T>(table: string, data: Partial<T>): Promise<T> {\n    const { data: result, error } = await supabase\n      .from(table)\n      .insert(data)\n      .select()\n      .single();\n\n    if (error) {\n      throw new Error(`Insert failed: ${error.message}`);\n    }\n\n    return result as T;\n  },\n\n  /**\n   * Generic update with error handling\n   */\n  async update<T>(\n    table: string,\n    id: string,\n    data: Partial<T>\n  ): Promise<T> {\n    const { data: result, error } = await supabase\n      .from(table)\n      .update(data)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) {\n      throw new Error(`Update failed: ${error.message}`);\n    }\n\n    return result as T;\n  },\n\n  /**\n   * Generic delete with error handling\n   */\n  async delete(table: string, id: string): Promise<void> {\n    const { error } = await supabase\n      .from(table)\n      .delete()\n      .eq('id', id);\n\n    if (error) {\n      throw new Error(`Delete failed: ${error.message}`);\n    }\n  },\n\n  /**\n   * Count records in a table\n   */\n  async count(\n    table: string,\n    filters?: Record<string, any>\n  ): Promise<number> {\n    let query = supabase\n      .from(table)\n      .select('*', { count: 'exact', head: true });\n\n    if (filters) {\n      Object.entries(filters).forEach(([key, value]) => {\n        query = query.eq(key, value);\n      });\n    }\n\n    const { count, error } = await query;\n\n    if (error) {\n      throw new Error(`Count failed: ${error.message}`);\n    }\n\n    return count || 0;\n  },\n};\n\n// Real-time subscription helpers\nexport const realtime = {\n  /**\n   * Subscribe to table changes\n   */\n  subscribeToTable(\n    table: string,\n    callback: (payload: any) => void,\n    filter?: string\n  ) {\n    const channel = supabase\n      .channel(`${table}_changes`)\n      .on(\n        'postgres_changes',\n        {\n          event: '*',\n          schema: 'public',\n          table,\n          filter,\n        },\n        callback\n      )\n      .subscribe();\n\n    return () => {\n      supabase.removeChannel(channel);\n    };\n  },\n\n  /**\n   * Subscribe to specific record changes\n   */\n  subscribeToRecord(\n    table: string,\n    id: string,\n    callback: (payload: any) => void\n  ) {\n    return this.subscribeToTable(table, callback, `id=eq.${id}`);\n  },\n\n  /**\n   * Subscribe to user's records\n   */\n  subscribeToUserRecords(\n    table: string,\n    userId: string,\n    callback: (payload: any) => void\n  ) {\n    return this.subscribeToTable(table, callback, `user_id=eq.${userId}`);\n  },\n};\n\n// Auth helpers\nexport const auth = {\n  /**\n   * Get current user\n   */\n  async getCurrentUser() {\n    const { data: { user }, error } = await supabase.auth.getUser();\n\n    if (error) {\n      throw new Error(`Failed to get user: ${error.message}`);\n    }\n\n    return user;\n  },\n\n  /**\n   * Get current session\n   */\n  async getCurrentSession() {\n    const { data: { session }, error } = await supabase.auth.getSession();\n\n    if (error) {\n      throw new Error(`Failed to get session: ${error.message}`);\n    }\n\n    return session;\n  },\n\n  /**\n   * Sign out user\n   */\n  async signOut() {\n    const { error } = await supabase.auth.signOut();\n\n    if (error) {\n      throw new Error(`Sign out failed: ${error.message}`);\n    }\n  },\n\n  /**\n   * Listen to auth state changes\n   */\n  onAuthStateChange(callback: (event: string, session: any) => void) {\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(callback);\n\n    return () => {\n      subscription.unsubscribe();\n    };\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAasB;AAbtB;AACA;;;;;;;;;AAKO,MAAM,WAAW,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD;AAG3C,MAAM,eAAe,IAAM;AAG3B,SAAS;IACd,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAEtC;IAEA,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAmB,AAAD,EAAY,aAAa,iBAAiB;QACjE,MAAM;YACJ,gBAAgB;YAChB,kBAAkB;YAClB,oBAAoB;QACtB;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,OAAO;IACP,SAAS;IACT,YAAY;IACZ,MAAM;AACR;AAGO,MAAM,UAAU;IACrB;;GAEC,GACD,MAAM,YACJ,MAAc,EACd,IAAY,EACZ,IAAU,EACV,OAIC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,QACL,MAAM,CAAC,MAAM,MAAM;YAClB,cAAc,SAAS,gBAAgB;YACvC,aAAa,SAAS,eAAe,KAAK,IAAI;YAC9C,QAAQ,SAAS,UAAU;QAC7B;QAEF,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;QACnD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,cAAa,MAAc,EAAE,IAAY;QACvC,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,OAAO,CAC9B,IAAI,CAAC,QACL,YAAY,CAAC;QAEhB,OAAO,KAAK,SAAS;IACvB;IAEA;;GAEC,GACD,MAAM,iBACJ,MAAc,EACd,IAAY,EACZ,YAAoB,IAAI;QAExB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,QACL,eAAe,CAAC,MAAM;QAEzB,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,MAAM,OAAO,EAAE;QACjE;QAEA,OAAO,KAAK,SAAS;IACvB;IAEA;;GAEC,GACD,MAAM,YAAW,MAAc,EAAE,IAAY;QAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CACrC,IAAI,CAAC,QACL,MAAM,CAAC;YAAC;SAAK;QAEhB,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;QACnD;IACF;IAEA;;GAEC,GACD,MAAM,WAAU,MAAc,EAAE,IAAa;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,QACL,IAAI,CAAC;QAER,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE;QACjD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,UACJ,MAAc,EACd,QAAgB,EAChB,MAAc;QAEd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CACrC,IAAI,CAAC,QACL,IAAI,CAAC,UAAU;QAElB,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE;QACjD;IACF;IAEA;;GAEC,GACD,MAAM,UACJ,MAAc,EACd,QAAgB,EAChB,MAAc;QAEd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CACrC,IAAI,CAAC,QACL,IAAI,CAAC,UAAU;QAElB,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE;QACjD;IACF;AACF;AAGO,MAAM,KAAK;IAChB;;GAEC,GACD,MAAM,QACJ,KAAa,EACb,OAAgB,EAChB,OAA6B;QAE7B,IAAI,QAAQ,SAAS,IAAI,CAAC,OAAO,MAAM,CAAC,WAAW;QAEnD,IAAI,SAAS;YACX,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC3C,QAAQ,MAAM,EAAE,CAAC,KAAK;YACxB;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;QACnD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,QAAU,KAAa,EAAE,IAAgB;QAC7C,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,OACL,MAAM,CAAC,MACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;QACnD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,QACJ,KAAa,EACb,EAAU,EACV,IAAgB;QAEhB,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,OACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;QACnD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,QAAO,KAAa,EAAE,EAAU;QACpC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,OACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;QACnD;IACF;IAEA;;GAEC,GACD,MAAM,OACJ,KAAa,EACb,OAA6B;QAE7B,IAAI,QAAQ,SACT,IAAI,CAAC,OACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,IAAI,SAAS;YACX,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC3C,QAAQ,MAAM,EAAE,CAAC,KAAK;YACxB;QACF;QAEA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAE/B,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,MAAM,OAAO,EAAE;QAClD;QAEA,OAAO,SAAS;IAClB;AACF;AAGO,MAAM,WAAW;IACtB;;GAEC,GACD,kBACE,KAAa,EACb,QAAgC,EAChC,MAAe;QAEf,MAAM,UAAU,SACb,OAAO,CAAC,GAAG,MAAM,QAAQ,CAAC,EAC1B,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR;YACA;QACF,GACA,UAED,SAAS;QAEZ,OAAO;YACL,SAAS,aAAa,CAAC;QACzB;IACF;IAEA;;GAEC,GACD,mBACE,KAAa,EACb,EAAU,EACV,QAAgC;QAEhC,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,UAAU,CAAC,MAAM,EAAE,IAAI;IAC7D;IAEA;;GAEC,GACD,wBACE,KAAa,EACb,MAAc,EACd,QAAgC;QAEhC,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,UAAU,CAAC,WAAW,EAAE,QAAQ;IACtE;AACF;AAGO,MAAM,OAAO;IAClB;;GAEC,GACD,MAAM;QACJ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE7D,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,MAAM,OAAO,EAAE;QACxD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM;QACJ,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAEnE,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;QAC3D;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM;QACJ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE7C,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,MAAM,OAAO,EAAE;QACrD;IACF;IAEA;;GAEC,GACD,mBAAkB,QAA+C;QAC/D,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAAC;QAEnE,OAAO;YACL,aAAa,WAAW;QAC1B;IACF;AACF", "debugId": null}}]}