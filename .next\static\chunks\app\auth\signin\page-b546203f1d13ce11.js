(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{968:(e,t,r)=>{"use strict";r.d(t,{Root:()=>a});var n=r(2115),l=r(3655),i=r(5155),o=n.forwardRef((e,t)=>(0,i.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var a=o},3655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>u,sG:()=>a});var n=r(2115),l=r(7650),i=r(9708),o=r(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:l,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?r:t,{...i,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function u(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},4985:(e,t,r)=>{Promise.resolve().then(r.bind(r,968)),Promise.resolve().then(r.t.bind(r,6874,23))},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>o,t:()=>i});var n=r(2115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function o(...e){return n.useCallback(i(...e),e)}},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>a,TL:()=>o});var n=r(2115),l=r(6101),i=r(5155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var o;let e,a,u=(o=r,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let r={...t};for(let n in t){let l=e[n],i=t[n];/^on[A-Z]/.test(n)?l&&i?r[n]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...i}:"className"===n&&(r[n]=[l,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,l.t)(t,u):u),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,a=n.Children.toArray(l),u=a.find(s);if(u){let e=u.props.children,l=a.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var a=o("Slot"),u=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,441,684,358],()=>t(4985)),_N_E=e.O()}]);