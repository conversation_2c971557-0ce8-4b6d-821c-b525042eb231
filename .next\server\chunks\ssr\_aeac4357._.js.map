{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/app/tours/page.tsx"], "sourcesContent": ["import { Metada<PERSON> } from \"next\";\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Eye, MapPin, Heart, Share2, Search } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport const metadata: Metadata = {\n  title: \"Explore Tours\",\n  description: \"Discover amazing virtual tours from across Nigeria\",\n};\n\nexport default function ToursPage() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b\">\n        <div className=\"container mx-auto px-4 py-4 flex items-center justify-between\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Eye className=\"h-8 w-8 text-primary\" />\n            <span className=\"text-2xl font-bold\">VirtualRealTour</span>\n          </Link>\n          <nav className=\"hidden md:flex items-center space-x-6\">\n            <Link href=\"/tours\" className=\"text-foreground font-medium\">\n              Tours\n            </Link>\n            <Link href=\"/pricing\" className=\"text-muted-foreground hover:text-foreground\">\n              Pricing\n            </Link>\n            <Link href=\"/about\" className=\"text-muted-foreground hover:text-foreground\">\n              About\n            </Link>\n            <Link href=\"/contact\" className=\"text-muted-foreground hover:text-foreground\">\n              Contact\n            </Link>\n          </nav>\n          <div className=\"flex items-center space-x-4\">\n            <Button variant=\"ghost\" asChild>\n              <Link href=\"/auth/signin\">Sign In</Link>\n            </Button>\n            <Button asChild>\n              <Link href=\"/auth/signup\">Get Started</Link>\n            </Button>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold mb-4\">Explore Virtual Tours</h1>\n          <p className=\"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\n            Discover stunning properties, educational institutions, hotels, and more \n            through immersive 360° virtual experiences across Nigeria.\n          </p>\n          \n          {/* Search Bar */}\n          <div className=\"max-w-md mx-auto relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\n            <Input\n              placeholder=\"Search tours by location, type, or name...\"\n              className=\"pl-10\"\n            />\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"flex flex-wrap gap-4 mb-8\">\n          <Button variant=\"outline\" size=\"sm\">All Categories</Button>\n          <Button variant=\"outline\" size=\"sm\">Real Estate</Button>\n          <Button variant=\"outline\" size=\"sm\">Education</Button>\n          <Button variant=\"outline\" size=\"sm\">Hospitality</Button>\n          <Button variant=\"outline\" size=\"sm\">Retail</Button>\n          <Button variant=\"outline\" size=\"sm\">Events</Button>\n        </div>\n\n        {/* Tours Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {[1, 2, 3, 4, 5, 6].map((tour) => (\n            <Card key={tour} className=\"overflow-hidden hover:shadow-lg transition-shadow\">\n              <div className=\"aspect-video bg-muted relative\">\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <Eye className=\"h-12 w-12 text-muted-foreground\" />\n                </div>\n                <div className=\"absolute top-4 left-4\">\n                  <span className=\"bg-primary text-primary-foreground px-2 py-1 rounded-full text-xs font-medium\">\n                    Real Estate\n                  </span>\n                </div>\n              </div>\n              \n              <CardHeader>\n                <CardTitle className=\"line-clamp-2\">\n                  Luxury 3-Bedroom Apartment in Victoria Island\n                </CardTitle>\n                <CardDescription className=\"flex items-center text-sm\">\n                  <MapPin className=\"h-4 w-4 mr-1\" />\n                  Victoria Island, Lagos\n                </CardDescription>\n              </CardHeader>\n              \n              <CardContent>\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-4 text-sm text-muted-foreground\">\n                    <span className=\"flex items-center\">\n                      <Eye className=\"h-4 w-4 mr-1\" />\n                      1,234\n                    </span>\n                    <span className=\"flex items-center\">\n                      <Heart className=\"h-4 w-4 mr-1\" />\n                      89\n                    </span>\n                    <span className=\"flex items-center\">\n                      <Share2 className=\"h-4 w-4 mr-1\" />\n                      12\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-sm text-muted-foreground\">\n                    by <span className=\"font-medium\">John Doe</span>\n                  </div>\n                  <Button size=\"sm\" asChild>\n                    <Link href={`/tours/${tour}`}>\n                      View Tour\n                    </Link>\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Load More */}\n        <div className=\"text-center mt-12\">\n          <Button variant=\"outline\" size=\"lg\">\n            Load More Tours\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA8B;;;;;;8CAG5D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA8C;;;;;;8CAG9E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA8C;;;;;;8CAG5E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA8C;;;;;;;;;;;;sCAIhF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,OAAO;8CAC7B,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAe;;;;;;;;;;;8CAE5B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAuD;;;;;;0CAMpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;0CACpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;0CACpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;0CACpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;0CACpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;0CACpC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;;;;;;;kCAItC,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC,gIAAA,CAAA,OAAI;gCAAY,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgF;;;;;;;;;;;;;;;;;kDAMpG,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAe;;;;;;0DAGpC,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;;kEACzB,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKvC,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGlC,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;0DAMzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DAAgC;0EAC1C,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEnC,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,OAAO;kEACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,OAAO,EAAE,MAAM;sEAAE;;;;;;;;;;;;;;;;;;;;;;;;+BA7C3B;;;;;;;;;;kCAwDf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAO9C", "debugId": null}}]}