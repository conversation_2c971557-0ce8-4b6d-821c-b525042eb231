(()=>{var e={};e.id=413,e.ids=[413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62273:(e,t,r)=>{"use strict";r.d(t,{t6:()=>d});var i=r(32190),s=r(45697);let a=s.z.object({NODE_ENV:s.z.enum(["development","production","test"]).default("development"),NEXT_PUBLIC_APP_URL:s.z.string().url().default("http://localhost:3000"),NEXT_PUBLIC_API_URL:s.z.string().url().optional(),NEXT_PUBLIC_SUPABASE_URL:s.z.string().url().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:s.z.string().min(1).optional(),SUPABASE_SERVICE_ROLE_KEY:s.z.string().min(1).optional(),DATABASE_URL:s.z.string().url().optional(),NEXTAUTH_SECRET:s.z.string().min(32).optional(),NEXTAUTH_URL:s.z.string().url().optional(),NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET:s.z.string().default("media"),SUPABASE_STORAGE_URL:s.z.string().url().optional(),STRIPE_SECRET_KEY:s.z.string().optional(),NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:s.z.string().optional(),STRIPE_WEBHOOK_SECRET:s.z.string().optional(),PAYSTACK_SECRET_KEY:s.z.string().optional(),NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY:s.z.string().optional(),FLUTTERWAVE_SECRET_KEY:s.z.string().optional(),NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY:s.z.string().optional(),WHATSAPP_ACCESS_TOKEN:s.z.string().optional(),WHATSAPP_PHONE_NUMBER_ID:s.z.string().optional(),WHATSAPP_WEBHOOK_VERIFY_TOKEN:s.z.string().optional(),GOOGLE_MAPS_API_KEY:s.z.string().optional(),GOOGLE_ANALYTICS_ID:s.z.string().optional(),GOOGLE_SITE_VERIFICATION:s.z.string().optional(),RESEND_API_KEY:s.z.string().optional(),SENDGRID_API_KEY:s.z.string().optional(),SENTRY_DSN:s.z.string().url().optional(),NEXT_PUBLIC_SENTRY_DSN:s.z.string().url().optional(),NEXT_PUBLIC_ENABLE_VR:s.z.string().transform(e=>"true"===e).default("true"),NEXT_PUBLIC_ENABLE_ANALYTICS:s.z.string().transform(e=>"true"===e).default("true"),NEXT_PUBLIC_ENABLE_PAYMENTS:s.z.string().transform(e=>"true"===e).default("true"),NEXT_PUBLIC_ENABLE_WHATSAPP:s.z.string().transform(e=>"true"===e).default("true"),UPSTASH_REDIS_REST_URL:s.z.string().url().optional(),UPSTASH_REDIS_REST_TOKEN:s.z.string().optional(),MAX_FILE_SIZE:s.z.string().default("100MB"),ALLOWED_FILE_TYPES:s.z.string().default("image/jpeg,image/png,image/webp,video/mp4,video/webm"),FREE_PLAN_TOUR_LIMIT:s.z.string().transform(Number).default("3"),PRO_PLAN_TOUR_LIMIT:s.z.string().transform(Number).default("50"),ENTERPRISE_PLAN_TOUR_LIMIT:s.z.string().default("unlimited")}),o=function(){try{return a.parse(process.env)}catch(e){if(e instanceof s.z.ZodError){let t=e.errors.filter(e=>"invalid_type"===e.code&&"undefined"===e.received).map(e=>e.path.join(".")),r=e.errors.filter(e=>"invalid_type"!==e.code||"undefined"!==e.received).map(e=>`${e.path.join(".")}: ${e.message}`),i="Environment validation failed:\n";throw t.length>0&&(i+=`
Missing required variables:
${t.map(e=>`  - ${e}`).join("\n")}`),r.length>0&&(i+=`
Invalid variables:
${r.map(e=>`  - ${e}`).join("\n")}`),Error(i)}throw e}}();o.NODE_ENV;let n="production"===o.NODE_ENV;if(o.NODE_ENV,o.NEXT_PUBLIC_SUPABASE_URL&&o.NEXT_PUBLIC_SUPABASE_ANON_KEY,o.NEXT_PUBLIC_SUPABASE_URL,o.NEXT_PUBLIC_SUPABASE_ANON_KEY,o.SUPABASE_SERVICE_ROLE_KEY,o.NEXT_PUBLIC_ENABLE_VR,o.NEXT_PUBLIC_ENABLE_ANALYTICS,o.NEXT_PUBLIC_ENABLE_PAYMENTS,o.NEXT_PUBLIC_ENABLE_WHATSAPP,o.STRIPE_SECRET_KEY&&o.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,o.STRIPE_SECRET_KEY,o.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,o.STRIPE_WEBHOOK_SECRET,o.PAYSTACK_SECRET_KEY&&o.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,o.PAYSTACK_SECRET_KEY,o.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,o.FLUTTERWAVE_SECRET_KEY&&o.NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY,o.FLUTTERWAVE_SECRET_KEY,o.NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY,o.RESEND_API_KEY,o.RESEND_API_KEY,o.SENDGRID_API_KEY,o.SENDGRID_API_KEY,o.WHATSAPP_ACCESS_TOKEN&&o.WHATSAPP_PHONE_NUMBER_ID,o.WHATSAPP_ACCESS_TOKEN,o.WHATSAPP_PHONE_NUMBER_ID,o.WHATSAPP_WEBHOOK_VERIFY_TOKEN,o.GOOGLE_MAPS_API_KEY,o.GOOGLE_MAPS_API_KEY,o.GOOGLE_ANALYTICS_ID,o.GOOGLE_ANALYTICS_ID,o.SENTRY_DSN,o.SENTRY_DSN,o.NEXT_PUBLIC_SENTRY_DSN,o.UPSTASH_REDIS_REST_URL&&o.UPSTASH_REDIS_REST_TOKEN,o.UPSTASH_REDIS_REST_URL,o.UPSTASH_REDIS_REST_TOKEN,o.MAX_FILE_SIZE,o.ALLOWED_FILE_TYPES.split(","),o.FREE_PLAN_TOUR_LIMIT,o.PRO_PLAN_TOUR_LIMIT,"unlimited"===o.ENTERPRISE_PLAN_TOUR_LIMIT||o.ENTERPRISE_PLAN_TOUR_LIMIT,n){let e=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","SUPABASE_SERVICE_ROLE_KEY"].filter(e=>!o[e]);if(e.length>0)throw Error(`Critical environment variables missing in production: ${e.join(", ")}`)}class E extends Error{constructor(e,t,r,i){super(e),this.status=t,this.code=r,this.details=i,this.name="APIError"}}class u{constructor(e){this.defaultTimeout=3e4,this.baseURL=e||o.NEXT_PUBLIC_API_URL||"/api"}async request(e,t={}){let{timeout:r=this.defaultTimeout,...i}=t,s=`${this.baseURL}${e.startsWith("/")?"":"/"}${e}`,a=new AbortController,o=setTimeout(()=>a.abort(),r);try{let e=await fetch(s,{...i,signal:a.signal,headers:{"Content-Type":"application/json",...i.headers}});clearTimeout(o);let t=await e.json();if(!e.ok)throw new E(t.error?.message||"Request failed",e.status,t.error?.code,t.error?.details);return t}catch(e){if(clearTimeout(o),e instanceof E)throw e;if(e instanceof Error){if("AbortError"===e.name)throw new E("Request timeout",408);throw new E(e.message,0)}throw new E("Unknown error occurred",0)}}async get(e,t){return this.request(e,{...t,method:"GET"})}async post(e,t,r){return this.request(e,{...r,method:"POST",body:t?JSON.stringify(t):void 0})}async put(e,t,r){return this.request(e,{...r,method:"PUT",body:t?JSON.stringify(t):void 0})}async patch(e,t,r){return this.request(e,{...r,method:"PATCH",body:t?JSON.stringify(t):void 0})}async delete(e,t){return this.request(e,{...t,method:"DELETE"})}async upload(e,t,r){let{onProgress:i,...s}=r||{};return new Promise((r,a)=>{let o=new XMLHttpRequest;o.upload.addEventListener("progress",e=>{e.lengthComputable&&i&&i(Math.round(e.loaded/e.total*100))}),o.addEventListener("load",()=>{try{let e=JSON.parse(o.responseText);o.status>=200&&o.status<300?r(e):a(new E(e.error?.message||"Upload failed",o.status,e.error?.code,e.error?.details))}catch(e){a(new E("Invalid response format",o.status))}}),o.addEventListener("error",()=>{a(new E("Network error",0))}),o.addEventListener("timeout",()=>{a(new E("Upload timeout",408))});let n=`${this.baseURL}${e.startsWith("/")?"":"/"}${e}`;o.open("POST",n),o.timeout=s.timeout||this.defaultTimeout,s.headers&&Object.entries(s.headers).forEach(([e,t])=>{"content-type"!==e.toLowerCase()&&o.setRequestHeader(e,t)}),o.send(t)})}}function _(e,t=500,r,s){return i.NextResponse.json({success:!1,error:{code:r||function(e){switch(e){case 400:return"BAD_REQUEST";case 401:return"UNAUTHORIZED";case 403:return"FORBIDDEN";case 404:return"NOT_FOUND";case 409:return"CONFLICT";case 422:return"VALIDATION_ERROR";case 429:return"RATE_LIMIT_EXCEEDED";case 500:return"INTERNAL_ERROR";case 502:return"BAD_GATEWAY";case 503:return"SERVICE_UNAVAILABLE";case 504:return"GATEWAY_TIMEOUT";default:return"UNKNOWN_ERROR"}}(t),message:e,details:s}},{status:t})}function d(e){if(console.error("API Error:",e),e instanceof s.G)return _("Validation failed",400,"VALIDATION_ERROR",e.errors);if(e instanceof E)return _(e.message,e.status,e.code,e.details);if(e instanceof Error){if(e.message.includes("duplicate key"))return _("Resource already exists",409,"DUPLICATE_RESOURCE");if(e.message.includes("foreign key"))return _("Referenced resource not found",400,"INVALID_REFERENCE");if(e.message.includes("not found"))return _("Resource not found",404,"NOT_FOUND")}return _("Internal server error",500,"INTERNAL_ERROR")}new u},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68207:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>T,serverHooks:()=>A,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var i={};r.r(i),r.d(i,{DELETE:()=>c,GET:()=>p,POST:()=>l});var s=r(96559),a=r(48088),o=r(37719),n=r(32190),E=r(61246),u=r(44999),_=r(62273);async function d(){let e=await (0,u.UL)();return(0,E.createServerClient)("https://maudhokdhyhspfpasnfm.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1hdWRob2tkaHloc3BmcGFzbmZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjgwMDQsImV4cCI6MjA2NDc0NDAwNH0.yaD0ot54tD5tj5Bz2SMZXgp6x6fzVXsAz2xjvB-bFFo",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:i})=>e.set(t,r,i))}catch{}}}})}async function l(e){try{var t;let r=await d(),{data:{user:i},error:s}=await r.auth.getUser();if(s||!i)return n.NextResponse.json({success:!1,error:{message:"Authentication required"}},{status:401});let a=await e.formData(),o=a.get("file"),E=a.get("tourId"),u=a.get("bucket")||"media";if(!o)return n.NextResponse.json({success:!1,error:{message:"No file provided"}},{status:400});let _=await (t={maxSize:100,allowedTypes:["image/jpeg","image/png","image/webp","video/mp4","video/webm"]},new Promise(async e=>{let r=[],i=[],s={size:o.size,type:o.type,width:void 0,height:void 0,duration:void 0};if(t.maxSize){let e=1024*t.maxSize*1024;o.size>e&&r.push(`File size must be less than ${t.maxSize}MB`)}if(t.allowedTypes&&!t.allowedTypes.includes(o.type)&&r.push(`File type ${o.type} is not allowed`),o.type.startsWith("image/"))try{let e=await new Promise((e,t)=>{let r=new Image;r.onload=()=>{e({width:r.naturalWidth,height:r.naturalHeight})},r.onerror=()=>t(Error("Failed to load image")),r.src=URL.createObjectURL(o)});s.width=e.width,s.height=e.height,t.minWidth&&e.width<t.minWidth&&r.push(`Image width must be at least ${t.minWidth}px`),t.minHeight&&e.height<t.minHeight&&r.push(`Image height must be at least ${t.minHeight}px`),t.maxWidth&&e.width>t.maxWidth&&r.push(`Image width must be less than ${t.maxWidth}px`),t.maxHeight&&e.height>t.maxHeight&&r.push(`Image height must be less than ${t.maxHeight}px`),(e.width>4096||e.height>4096)&&i.push("Large images may take longer to process")}catch(e){r.push("Failed to read image dimensions")}if(o.type.startsWith("video/"))try{let e=await new Promise((e,t)=>{let r=document.createElement("video");r.onloadedmetadata=()=>{e({duration:r.duration,width:r.videoWidth,height:r.videoHeight})},r.onerror=()=>t(Error("Failed to load video")),r.src=URL.createObjectURL(o)});s.width=e.width,s.height=e.height,s.duration=e.duration,e.duration>300&&i.push("Long videos may take significant time to process")}catch(e){i.push("Failed to read video metadata")}e({isValid:0===r.length,errors:r,warnings:i,metadata:s})}));if(!_.isValid)return n.NextResponse.json({success:!1,error:{message:"File validation failed",details:_.errors}},{status:400});let l=o.name.split(".").pop(),c=Date.now(),p=Math.random().toString(36).substring(2),T=`${c}-${p}.${l}`,m=E?`${i.id}/${E}/${T}`:`${i.id}/${T}`,{data:h,error:A}=await r.storage.from(u).upload(m,o,{cacheControl:"3600",upsert:!1});if(A)throw A;let{data:{publicUrl:g}}=r.storage.from(u).getPublicUrl(m),{data:S,error:I}=await r.from("media_files").insert({user_id:i.id,filename:T,original_filename:o.name,file_path:m,file_size:o.size,mime_type:o.type,width:_.metadata?.width,height:_.metadata?.height,duration:_.metadata?.duration,is_360:!!(_.metadata?.width&&_.metadata?.height)&&_.metadata.width/_.metadata.height>=1.8,processing_status:"completed",metadata:{tour_id:E,bucket:u,upload_date:new Date().toISOString(),validation_warnings:_.warnings}}).select().single();if(I)throw await r.storage.from(u).remove([m]),I;return n.NextResponse.json({success:!0,data:{...S,public_url:g,validation_warnings:_.warnings}},{status:201})}catch(e){return(0,_.t6)(e)}}async function c(e){try{let t=await d(),{data:{user:r},error:i}=await t.auth.getUser();if(i||!r)return n.NextResponse.json({success:!1,error:{message:"Authentication required"}},{status:401});let{searchParams:s}=new URL(e.url),a=s.get("id");if(!a)return n.NextResponse.json({success:!1,error:{message:"Media ID required"}},{status:400});let{data:o,error:E}=await t.from("media_files").select("*").eq("id",a).eq("user_id",r.id).single();if(E){if("PGRST116"===E.code)return n.NextResponse.json({success:!1,error:{message:"Media file not found"}},{status:404});throw E}let u=o.metadata?.bucket||"media",{error:_}=await t.storage.from(u).remove([o.file_path]);_&&console.error("Storage deletion failed:",_);let{error:l}=await t.from("media_files").delete().eq("id",a).eq("user_id",r.id);if(l)throw l;return n.NextResponse.json({success:!0,message:"Media file deleted successfully"})}catch(e){return(0,_.t6)(e)}}async function p(e){try{let t=await d(),{data:{user:r},error:i}=await t.auth.getUser();if(i||!r)return n.NextResponse.json({success:!1,error:{message:"Authentication required"}},{status:401});let{searchParams:s}=new URL(e.url),a=s.get("tourId"),o=parseInt(s.get("page")||"1"),E=parseInt(s.get("limit")||"20"),u=t.from("media_files").select("*",{count:"exact"}).eq("user_id",r.id);a&&(u=u.eq("metadata->>tour_id",a));let _=(o-1)*E,{data:l,error:c,count:p}=await u.order("created_at",{ascending:!1}).range(_,_+E-1);if(c)throw c;let T=l?.map(e=>{let r=e.metadata?.bucket||"media",{data:{publicUrl:i}}=t.storage.from(r).getPublicUrl(e.file_path);return{...e,public_url:i}});return n.NextResponse.json({success:!0,data:{media:T,pagination:{page:o,limit:E,total:p||0,totalPages:Math.ceil((p||0)/E)}}})}catch(e){return(0,_.t6)(e)}}let T=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:A}=T;function g(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,198,580,82],()=>r(68207));module.exports=i})();