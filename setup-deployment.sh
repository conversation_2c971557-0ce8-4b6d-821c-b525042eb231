#!/bin/bash

# VirtualRealTour Platform - Deployment Setup Script
echo "🚀 Setting up VirtualRealTour Platform for deployment..."

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "❌ Error: Not in a git repository"
    exit 1
fi

# Check current branch
CURRENT_BRANCH=$(git branch --show-current)
echo "📍 Current branch: $CURRENT_BRANCH"

# Create dev branch if it doesn't exist
if ! git show-ref --verify --quiet refs/heads/dev; then
    echo "🌿 Creating dev branch..."
    git checkout -b dev
    git push -u origin dev
else
    echo "✅ Dev branch already exists"
fi

# Switch to main branch and prepare for deployment
echo "🔄 Switching to main branch..."
git checkout main

# Add all changes
echo "📦 Adding all changes..."
git add .

# Commit changes
echo "💾 Committing changes..."
git commit -m "VirtualRealTour platform ready for deployment

- ✅ Supabase SSR integration complete
- ✅ 3D tour viewer with fallback
- ✅ Bun for development, npm for build
- ✅ All components functional
- ✅ Build successful
- ✅ Ready for production deployment"

# Push to main branch
echo "🚀 Pushing to main branch..."
if git push origin main; then
    echo "✅ Successfully pushed to main branch"
    echo ""
    echo "🎯 Next steps:"
    echo "1. Go to vercel.com with your deployment account"
    echo "2. Import this GitHub repository"
    echo "3. Set environment variables:"
    echo "   - NEXT_PUBLIC_SUPABASE_URL=https://maudhokdhyhspfpasnfm.supabase.co"
    echo "   - NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key"
    echo "4. Deploy!"
    echo ""
    echo "🔄 Development workflow:"
    echo "   git checkout dev          # Switch to development"
    echo "   # ... make changes ..."
    echo "   git add . && git commit -m 'Feature: description'"
    echo "   git push origin dev       # Deploy to preview"
    echo "   git checkout main && git merge dev && git push origin main  # Deploy to production"
else
    echo "❌ Failed to push to main branch"
    echo "🔧 Try fixing GitHub connectivity:"
    echo "   git remote set-<NAME_EMAIL>:iwalk-jo/virtualrealtour_platform.git"
    echo "   ssh -T **************"
fi

# Switch back to dev for development
echo "🔄 Switching back to dev branch for development..."
git checkout dev

echo "✨ Setup complete! Ready for deployment and development."
