# VirtualRealTour Platform

A comprehensive virtual tour platform built specifically for the Nigerian market, featuring 360° immersive experiences, VR support, and local payment integrations.

## 🌟 Features

### Core Features
- 🏠 **360° Virtual Tours**: Create and share immersive virtual experiences
- 🎯 **Interactive Hotspots**: Add clickable hotspots with rich multimedia content
- 🥽 **VR/WebXR Support**: Full virtual reality experience in supported browsers
- 📱 **Mobile-First Design**: Optimized for all devices with touch controls
- 🎨 **Customizable Branding**: White-label solutions for enterprises

### Nigerian Market Focus
- 💳 **Local Payments**: Integrated with Paystack and Flutterwave
- 📱 **WhatsApp Integration**: Direct customer communication via WhatsApp Business
- 🌍 **Mobile-Optimized**: Built for Nigeria's mobile-first internet usage
- 💰 **Naira Pricing**: All pricing in Nigerian Naira (₦)

### Technical Features
- 🔐 **Secure Authentication**: User management with Supabase Auth
- 📊 **Advanced Analytics**: Comprehensive tour performance insights
- ☁️ **Cloud Storage**: Scalable media storage and CDN delivery
- 🚀 **High Performance**: Optimized loading and rendering
- 🛡️ **Enterprise Security**: Role-based access control and data protection

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/UI
- **3D Engine**: React Three Fiber + Three.js
- **State Management**: React Context + Custom Hooks

### Backend & Services
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Payments**: Paystack, Flutterwave
- **Communication**: WhatsApp Business API
- **Analytics**: Custom analytics engine

### Development & Deployment
- **Package Manager**: npm
- **Linting**: ESLint + Prettier
- **Type Checking**: TypeScript
- **Deployment**: Vercel (recommended)

## 🚀 Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn package manager
- Supabase account (optional for development)

### Quick Start

1. **Clone the repository**
```bash
git clone https://github.com/your-username/virtualrealtour-platform.git
cd virtualrealtour-platform
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.local.example .env.local
```

4. **Start development server**
```bash
npm run dev
```

5. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

### Development Mode

The application runs in development mode without requiring Supabase configuration. You can:
- Browse all pages and UI components
- Test responsive design
- View sample tours and content
- Test navigation and routing

For full functionality (authentication, database, file uploads), configure Supabase credentials.

## 📱 Current Implementation Status

### ✅ Completed Features
- **Core Architecture**: Next.js 14 with App Router, TypeScript, Tailwind CSS
- **UI Components**: Complete Shadcn/UI component library integration
- **Authentication System**: Supabase Auth with optional configuration
- **Responsive Design**: Mobile-first design with device detection
- **3D Viewer Foundation**: React Three Fiber setup for 360° tours
- **Page Structure**: All main pages (Home, About, Pricing, Contact, Tours, etc.)
- **Error Handling**: Custom 404 page and error boundaries
- **Loading States**: Application-wide loading with battery charging animation
- **File Upload System**: Complete file validation and upload infrastructure
- **API Client**: Robust API client with error handling
- **Environment Configuration**: Comprehensive environment variable management

### 🔄 Ready for Next Phase
- **Tour Builder**: Advanced tour creation interface
- **Hotspot Editor**: Interactive hotspot management
- **Payment Integration**: Paystack/Flutterwave implementation
- **Analytics Dashboard**: Tour performance tracking
- **WhatsApp Integration**: Business communication features

## 📁 Project Structure

```
virtualrealtour-platform/
├── src/
│   ├── app/                    # Next.js 14 App Router
│   │   ├── (auth)/            # Authentication pages
│   │   ├── (dashboard)/       # Protected dashboard pages
│   │   ├── tours/             # Tour-related pages
│   │   ├── about/             # About page
│   │   ├── pricing/           # Pricing page
│   │   ├── contact/           # Contact page
│   │   ├── privacy/           # Privacy policy
│   │   ├── terms/             # Terms of service
│   │   ├── not-found.tsx      # Custom 404 page
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Homepage with featured tours
│   ├── components/            # Reusable UI components
│   │   ├── 3d/               # 3D viewer components
│   │   │   └── tour-viewer.tsx # Main 360° tour viewer
│   │   ├── layout/           # Layout components
│   │   │   ├── public-layout.tsx
│   │   │   └── dashboard-layout.tsx
│   │   ├── ui/               # Base UI components (Shadcn)
│   │   │   ├── app-loading.tsx # Custom loading component
│   │   │   └── loading.tsx    # Various loading states
│   │   ├── error-boundary.tsx # Error handling
│   │   └── providers.tsx     # Context providers
│   ├── hooks/                # Custom React hooks
│   │   ├── use-device-detection.ts # Device and browser detection
│   │   ├── use-file-upload.ts     # File upload with progress
│   │   ├── use-media-query.ts     # Responsive design hooks
│   │   ├── use-click-outside.ts   # Click outside detection
│   │   ├── use-debounce.ts        # Debounced values
│   │   ├── use-local-storage.ts   # Local storage management
│   │   └── index.ts               # Hook exports
│   ├── lib/                  # Utility functions
│   │   ├── api/              # API client and error handling
│   │   │   ├── client.ts     # HTTP client with upload support
│   │   │   └── error-handler.ts # Comprehensive error handling
│   │   ├── utils/            # Helper functions
│   │   │   └── file.ts       # File validation and processing
│   │   └── env.ts            # Environment configuration
│   ├── types/                # TypeScript definitions
│   │   ├── supabase.ts       # Database types
│   │   └── index.ts          # Global types
│   └── middleware.ts         # Next.js middleware with auth
├── public/                   # Static assets
├── .env.local               # Environment variables
├── next.config.ts           # Next.js configuration
├── tailwind.config.ts       # Tailwind CSS configuration
└── tsconfig.json           # TypeScript configuration
```

## 🔧 Configuration

### Environment Variables

The application includes a comprehensive environment configuration system that gracefully handles missing variables in development mode.

#### Required for Production
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

#### Optional Services
```env
# Payment Providers
PAYSTACK_SECRET_KEY=your_paystack_secret_key
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=your_paystack_public_key

FLUTTERWAVE_SECRET_KEY=your_flutterwave_secret_key
NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY=your_flutterwave_public_key

# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id

# Feature Flags
NEXT_PUBLIC_ENABLE_VR=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_PAYMENTS=true
NEXT_PUBLIC_ENABLE_WHATSAPP=true
```

## 🎯 Development Highlights

### Enterprise-Grade Architecture
- **Type Safety**: Comprehensive TypeScript coverage
- **Error Handling**: Robust error boundaries and user-friendly messages
- **Performance**: Optimized loading and rendering
- **Accessibility**: WCAG compliant design patterns
- **Security**: Secure authentication and data handling

### Nigerian Market Optimization
- **Mobile-First**: Optimized for mobile devices and slow connections
- **Local Integration**: WhatsApp Business API integration
- **Payment Methods**: Support for local payment providers
- **Currency**: Naira pricing and formatting
- **Cultural Adaptation**: UI/UX designed for Nigerian users

### Developer Experience
- **Hot Reload**: Instant development feedback
- **Type Safety**: Full TypeScript integration
- **Component Library**: Consistent UI with Shadcn/UI
- **Custom Hooks**: Reusable logic for common patterns
- **Error Boundaries**: Graceful error handling in development

## 🚀 Next Development Phase

The foundation is now complete and ready for the next development phase:

1. **Advanced Tour Builder**: Drag-and-drop interface for creating tours
2. **Hotspot Management**: Rich media hotspots with various content types
3. **Payment Integration**: Complete Paystack/Flutterwave implementation
4. **Analytics Dashboard**: Real-time tour performance metrics
5. **WhatsApp Integration**: Automated customer communication
6. **VR Enhancements**: Advanced VR features and optimizations

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 📱 WhatsApp: +234 (0) 123 456 7890
- 🌐 Website: [virtualrealtour.ng](https://virtualrealtour.ng)

---

**VirtualRealTour** - Bringing Nigerian spaces to life through immersive virtual experiences.
