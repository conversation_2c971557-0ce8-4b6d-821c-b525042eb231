(()=>{var e={};e.id=630,e.ids=[630],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},499:(e,t,r)=>{"use strict";e.exports=r(36978)},1577:(e,t,r)=>{Promise.resolve().then(r.bind(r,11096)),Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,78648)),Promise.resolve().then(r.bind(r,22230))},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:i,quality:s}=e,a=s||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+i+"&q="+a+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7026:(e,t,r)=>{"use strict";e.exports=function(e){function t(e,t,r,n){return new nA(e,t,r,n)}function n(){}function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=is&&e[is]||e["@@iterator"])?e:null}function a(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===ia?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case nK:return"Fragment";case n$:return"Portal";case n1:return"Profiler";case n0:return"StrictMode";case n6:return"Suspense";case n8:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case n4:return(e.displayName||"Context")+".Provider";case n3:return(e._context.displayName||"Context")+".Consumer";case n5:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case n7:return null!==(t=e.displayName||null)?t:a(e.type)||"Memo";case n9:t=e._payload,e=e._init;try{return a(e(t))}catch(e){}}return null}function o(e){if(void 0===it)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);it=t&&t[1]||"",ir=-1<e.stack.indexOf("\n    at")?" (<anonymous>)":-1<e.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+it+e+ir}function l(e,t){if(!e||il)return"";il=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(t){var r=function(){throw Error()};if(Object.defineProperty(r.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(r,[])}catch(e){var n=e}Reflect.construct(e,[],r)}else{try{r.call()}catch(e){n=e}e.call(r.prototype)}}else{try{throw Error()}catch(e){n=e}(r=e())&&"function"==typeof r.catch&&r.catch(function(){})}}catch(e){if(e&&n&&"string"==typeof e.stack)return[e.stack,n.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=n.DetermineComponentFrameRoot(),a=s[0],l=s[1];if(a&&l){var u=a.split("\n"),h=l.split("\n");for(i=n=0;n<u.length&&!u[n].includes("DetermineComponentFrameRoot");)n++;for(;i<h.length&&!h[i].includes("DetermineComponentFrameRoot");)i++;if(n===u.length||i===h.length)for(n=u.length-1,i=h.length-1;1<=n&&0<=i&&u[n]!==h[i];)i--;for(;1<=n&&0<=i;n--,i--)if(u[n]!==h[i]){if(1!==n||1!==i)do if(n--,i--,0>i||u[n]!==h[i]){var c="\n"+u[n].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=n&&0<=i);break}}}finally{il=!1,Error.prepareStackTrace=r}return(r=e?e.displayName||e.name:"")?o(r):""}function u(e){try{var t="";do t+=function(e){switch(e.tag){case 26:case 27:case 5:return o(e.type);case 16:return o("Lazy");case 13:return o("Suspense");case 19:return o("SuspenseList");case 0:case 15:return e=l(e.type,!1);case 11:return e=l(e.type.render,!1);case 1:return e=l(e.type,!0);default:return""}}(e),e=e.return;while(e);return t}catch(e){return"\nError generating stack: "+e.message+"\n"+e.stack}}function h(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do 0!=(4098&(t=e).flags)&&(r=t.return),e=t.return;while(e)}return 3===t.tag?r:null}function c(e){if(h(e)!==e)throw Error(i(188))}function d(e){var t=e.alternate;if(!t){if(null===(t=h(e)))throw Error(i(188));return t!==e?null:e}for(var r=e,n=t;;){var s=r.return;if(null===s)break;var a=s.alternate;if(null===a){if(null!==(n=s.return)){r=n;continue}break}if(s.child===a.child){for(a=s.child;a;){if(a===r)return c(s),e;if(a===n)return c(s),t;a=a.sibling}throw Error(i(188))}if(r.return!==n.return)r=s,n=a;else{for(var o=!1,l=s.child;l;){if(l===r){o=!0,r=s,n=a;break}if(l===n){o=!0,n=s,r=a;break}l=l.sibling}if(!o){for(l=a.child;l;){if(l===r){o=!0,r=a,n=s;break}if(l===n){o=!0,n=a,r=s;break}l=l.sibling}if(!o)throw Error(i(189))}}if(r.alternate!==n)throw Error(i(190))}if(3!==r.tag)throw Error(i(188));return r.stateNode.current===r?e:t}function f(e){return{current:e}}function p(e){0>s5||(e.current=s4[s5],s4[s5]=null,s5--)}function m(e,t){s4[++s5]=e.current,e.current=t}function y(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194176&e;case 4194304:case 8388608:case 0x1000000:case 0x2000000:return 0x3c00000&e;case 0x4000000:return 0x4000000;case 0x8000000:return 0x8000000;case 0x10000000:return 0x10000000;case 0x20000000:return 0x20000000;case 0x40000000:return 0;default:return e}}function g(e,t){var r=e.pendingLanes;if(0===r)return 0;var n=0,i=e.suspendedLanes,s=e.pingedLanes,a=e.warmLanes;e=0!==e.finishedLanes;var o=0x7ffffff&r;return 0!==o?0!=(r=o&~i)?n=y(r):0!=(s&=o)?n=y(s):e||0!=(a=o&~a)&&(n=y(a)):0!=(o=r&~i)?n=y(o):0!==s?n=y(s):e||0!=(a=r&~a)&&(n=y(a)),0===n?0:0!==t&&t!==n&&0==(t&i)&&((i=n&-n)>=(a=t&-t)||32===i&&0!=(4194176&a))?t:n}function x(e,t){return 0==(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function b(){var e=ae;return 0==(4194176&(ae<<=1))&&(ae=128),e}function v(){var e=at;return 0==(0x3c00000&(at<<=1))&&(at=4194304),e}function w(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function S(e,t){e.pendingLanes|=t,0x10000000!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function M(e,t,r){e.pendingLanes|=t,e.suspendedLanes&=~t;var n=31-s8(t);e.entangledLanes|=t,e.entanglements[n]=0x40000000|e.entanglements[n]|4194218&r}function _(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-s8(r),i=1<<n;i&t|e[n]&t&&(e[n]|=t),r&=~i}}function z(e){return 2<(e&=-e)?8<e?0!=(0x7ffffff&e)?32:0x10000000:8:2}function k(e){if("function"==typeof ac&&ad(e),ap&&"function"==typeof ap.setStrictMode)try{ap.setStrictMode(af,e)}catch(e){}}function C(e,t){if("object"==typeof e&&null!==e){var r=ay.get(e);return void 0!==r?r:(t={value:e,source:t,stack:u(t)},ay.set(e,t),t)}return{value:e,source:t,stack:u(t)}}function P(e,t){ag[ax++]=av,ag[ax++]=ab,ab=e,av=t}function E(e,t,r){aw[aS++]=a_,aw[aS++]=az,aw[aS++]=aM,aM=e;var n=a_;e=az;var i=32-s8(n)-1;n&=~(1<<i),r+=1;var s=32-s8(t)+i;if(30<s){var a=i-i%5;s=(n&(1<<a)-1).toString(32),n>>=a,i-=a,a_=1<<32-s8(t)+i|r<<i|n,az=s+e}else a_=1<<s|r<<i|n,az=e}function A(e){null!==e.return&&(P(e,1),E(e,1,0))}function N(e){for(;e===ab;)ab=ag[--ax],ag[ax]=null,av=ag[--ax],ag[ax]=null;for(;e===aM;)aM=aw[--aS],aw[aS]=null,az=aw[--aS],aw[aS]=null,a_=aw[--aS],aw[aS]=null}function T(e,t){m(aP,t),m(aC,e),m(ak,null),e=im(t),p(ak),m(ak,e)}function j(){p(ak),p(aC),p(aP)}function O(e){null!==e.memoizedState&&m(aE,e);var t=ak.current,r=iy(t,e.type);t!==r&&(m(aC,e),m(ak,r))}function R(e){aC.current===e&&(p(ak),p(aC)),aE.current===e&&(p(aE),iC?iH._currentValue=iW:iH._currentValue2=iW)}function I(e){throw D(C(Error(i(418,"")),e)),aR}function L(e,t){if(!iA)throw Error(i(175));sC(e.stateNode,e.type,e.memoizedProps,t,e)||I(e)}function F(e){for(aA=e.return;aA;)switch(aA.tag){case 3:case 27:aO=!0;return;case 5:case 13:aO=!1;return;default:aA=aA.return}}function V(e){if(!iA||e!==aA)return!1;if(!aT)return F(e),aT=!0,!1;var t=!1;if(s$?3!==e.tag&&27!==e.tag&&(5!==e.tag||sR(e.type)&&!iS(e.type,e.memoizedProps))&&(t=!0):3!==e.tag&&(5!==e.tag||sR(e.type)&&!iS(e.type,e.memoizedProps))&&(t=!0),t&&aN&&I(e),F(e),13===e.tag){if(!iA)throw Error(i(316));if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));aN=sA(e)}else aN=aA?sv(e.stateNode):null;return!0}function B(){iA&&(aN=aA=null,aT=!1)}function D(e){null===aj?aj=[e]:aj.push(e)}function U(){for(var e=aL,t=aF=aL=0;t<e;){var r=aI[t];aI[t++]=null;var n=aI[t];aI[t++]=null;var i=aI[t];aI[t++]=null;var s=aI[t];if(aI[t++]=null,null!==n&&null!==i){var a=n.pending;null===a?i.next=i:(i.next=a.next,a.next=i),n.pending=i}0!==s&&J(r,i,s)}}function W(e,t,r,n){aI[aL++]=e,aI[aL++]=t,aI[aL++]=r,aI[aL++]=n,aF|=n,e.lanes|=n,null!==(e=e.alternate)&&(e.lanes|=n)}function H(e,t,r,n){return W(e,t,r,n),Z(e)}function q(e,t){return W(e,null,null,t),Z(e)}function J(e,t,r){e.lanes|=r;var n=e.alternate;null!==n&&(n.lanes|=r);for(var i=!1,s=e.return;null!==s;)s.childLanes|=r,null!==(n=s.alternate)&&(n.childLanes|=r),22===s.tag&&(null===(e=s.stateNode)||1&e._visibility||(i=!0)),e=s,s=s.return;i&&null!==t&&3===e.tag&&(s=e.stateNode,i=31-s8(r),null===(e=(s=s.hiddenUpdates)[i])?s[i]=[t]:e.push(t),t.lane=0x20000000|r)}function Z(e){if(50<lf)throw lf=0,lp=null,Error(i(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}function Y(e){var t;e!==aB&&null===e.next&&(null===aB?aV=aB=e:aB=aB.next=e),aU=!0,aD||(aD=!0,t=G,iJ?iZ(function(){0!=(6&oX)?ar(ao,t):t()}):ar(ao,t))}function X(e,t){if(!aW&&aU){aW=!0;do for(var r=!1,n=aV;null!==n;){if(!t)if(0!==e){var i=n.pendingLanes;if(0===i)var s=0;else{var a=n.suspendedLanes,o=n.pingedLanes;s=0xc000055&(s=(1<<31-s8(42|e)+1)-1&(i&~(a&~o)))?0xc000055&s|1:s?2|s:0}0!==s&&(r=!0,K(n,s))}else s=o$,0==(3&(s=g(n,n===oG?s:0)))||x(n,s)||(r=!0,K(n,s));n=n.next}while(r);aW=!1}}function G(){aU=aD=!1;var e=0;0!==aH&&(iI()&&(e=aH),aH=0);for(var t=aa(),r=null,n=aV;null!==n;){var i=n.next,s=Q(n,t);0===s?(n.next=null,null===r?aV=i:r.next=i,null===i&&(aB=r)):(r=n,(0!==e||0!=(3&s))&&(aU=!0)),n=i}X(e,!1)}function Q(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,i=e.expirationTimes,s=-0x3c00001&e.pendingLanes;0<s;){var a=31-s8(s),o=1<<a,l=i[a];-1===l?(0==(o&r)||0!=(o&n))&&(i[a]=function(e,t){switch(e){case 1:case 2:case 4:case 8:return t+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return -1}}(o,t)):l<=t&&(e.expiredLanes|=o),s&=~o}if(t=oG,r=o$,r=g(e,e===t?r:0),n=e.callbackNode,0===r||e===t&&2===oK||null!==e.cancelPendingCommit)return null!==n&&null!==n&&an(n),e.callbackNode=null,e.callbackPriority=0;if(0==(3&r)||x(e,r)){if((t=r&-r)===e.callbackPriority)return t;switch(null!==n&&an(n),z(r)){case 2:case 8:r=al;break;case 32:default:r=au;break;case 0x10000000:r=ah}return r=ar(r,n=$.bind(null,e)),e.callbackPriority=t,e.callbackNode=r,t}return null!==n&&null!==n&&an(n),e.callbackPriority=2,e.callbackNode=null,2}function $(e,t){var r=e.callbackNode;if(nS()&&e.callbackNode!==r)return null;var n=o$;return 0===(n=g(e,e===oG?n:0))?null:(nr(e,n,t),Q(e,aa()),null!=e.callbackNode&&e.callbackNode===r?$.bind(null,e):null)}function K(e,t){if(nS())return null;nr(e,t,!0)}function ee(){return 0===aH&&(aH=b()),aH}function et(){if(0==--aJ&&null!==aq){null!==aY&&(aY.status="fulfilled");var e=aq;aq=null,aZ=0,aY=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function er(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function en(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ei(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function es(e,t,r){var n=e.updateQueue;if(null===n)return null;if(n=n.shared,0!=(2&oX)){var i=n.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),n.pending=t,t=Z(e),J(e,null,r),t}return W(e,n,t,r),Z(e)}function ea(e,t,r){if(null!==(t=t.updateQueue)&&(t=t.shared,0!=(4194176&r))){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,_(e,r)}}function eo(e,t){var r=e.updateQueue,n=e.alternate;if(null!==n&&r===(n=n.updateQueue)){var i=null,s=null;if(null!==(r=r.firstBaseUpdate)){do{var a={lane:r.lane,tag:r.tag,payload:r.payload,callback:null,next:null};null===s?i=s=a:s=s.next=a,r=r.next}while(null!==r);null===s?i=s=t:s=s.next=t}else i=s=t;r={baseState:n.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:n.shared,callbacks:n.callbacks},e.updateQueue=r;return}null===(e=r.lastBaseUpdate)?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function el(){if(aG){var e=aY;if(null!==e)throw e}}function eu(e,t,r,n){aG=!1;var i=e.updateQueue;aX=!1;var s=i.firstBaseUpdate,a=i.lastBaseUpdate,o=i.shared.pending;if(null!==o){i.shared.pending=null;var l=o,u=l.next;l.next=null,null===a?s=u:a.next=u,a=l;var h=e.alternate;null!==h&&(o=(h=h.updateQueue).lastBaseUpdate)!==a&&(null===o?h.firstBaseUpdate=u:o.next=u,h.lastBaseUpdate=l)}if(null!==s){var c=i.baseState;for(a=0,h=u=l=null,o=s;;){var d=-0x20000001&o.lane,f=d!==o.lane;if(f?(o$&d)===d:(n&d)===d){0!==d&&d===aZ&&(aG=!0),null!==h&&(h=h.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var p=e,m=o;switch(d=t,m.tag){case 1:if("function"==typeof(p=m.payload)){c=p.call(r,c,d);break e}c=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null==(d="function"==typeof(p=m.payload)?p.call(r,c,d):p))break e;c=nX({},c,d);break e;case 2:aX=!0}}null!==(d=o.callback)&&(e.flags|=64,f&&(e.flags|=8192),null===(f=i.callbacks)?i.callbacks=[d]:f.push(d))}else f={lane:d,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===h?(u=h=f,l=c):h=h.next=f,a|=d;if(null===(o=o.next))if(null===(o=i.shared.pending))break;else o=(f=o).next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}null===h&&(l=c),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=h,null===s&&(i.shared.lanes=0),o6|=a,e.lanes=a,e.memoizedState=c}}function eh(e,t){if("function"!=typeof e)throw Error(i(191,e));e.call(t)}function ec(e,t){var r=e.callbacks;if(null!==r)for(e.callbacks=null,e=0;e<r.length;e++)eh(r[e],t)}function ed(e,t){if(am(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var i=r[n];if(!aQ.call(t,i)||!am(e[i],t[i]))return!1}return!0}function ef(e){return"fulfilled"===(e=e.status)||"rejected"===e}function ep(){}function em(e,t,r){switch(void 0===(r=e[r])?e.push(t):r!==t&&(t.then(ep,ep),t=r),t.status){case"fulfilled":return t.value;case"rejected":if((e=t.reason)===a$)throw Error(i(483));throw e;default:if("string"==typeof t.status)t.then(ep,ep);else{if(null!==(e=oG)&&100<e.shellSuspendCounter)throw Error(i(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var r=t;r.status="fulfilled",r.value=e}},function(e){if("pending"===t.status){var r=t;r.status="rejected",r.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":if((e=t.reason)===a$)throw Error(i(483));throw e}throw a1=t,a$}}function ey(){if(null===a1)throw Error(i(459));var e=a1;return a1=null,e}function eg(e){var t=a3;return a3+=1,null===a2&&(a2=[]),em(a2,e,t)}function ex(e,t){e.ref=void 0!==(t=t.props.ref)?t:null}function eb(e,t){if(t.$$typeof===nG)throw Error(i(525));throw Error(i(31,"[object Object]"===(e=Object.prototype.toString.call(t))?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ev(e){return(0,e._init)(e._payload)}function ew(e){function r(t,r){if(e){var n=t.deletions;null===n?(t.deletions=[r],t.flags|=16):n.push(r)}}function n(t,n){if(!e)return null;for(;null!==n;)r(t,n),n=n.sibling;return null}function a(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function o(e,t){return(e=nT(e,t)).index=0,e.sibling=null,e}function l(t,r,n){return(t.index=n,e)?null!==(n=t.alternate)?(n=n.index)<r?(t.flags|=0x2000002,r):n:(t.flags|=0x2000002,r):(t.flags|=1048576,r)}function u(t){return e&&null===t.alternate&&(t.flags|=0x2000002),t}function h(e,t,r,n){return null===t||6!==t.tag?(t=nL(r,e.mode,n)).return=e:(t=o(t,r)).return=e,t}function c(e,t,r,n){var i=r.type;return i===nK?f(e,t,r.props.children,n,r.key):(null!==t&&(t.elementType===i||"object"==typeof i&&null!==i&&i.$$typeof===n9&&ev(i)===t.type)?ex(t=o(t,r.props),r):ex(t=nO(r.type,r.key,r.props,null,e.mode,n),r),t.return=e,t)}function d(e,t,r,n){return null===t||4!==t.tag||t.stateNode.containerInfo!==r.containerInfo||t.stateNode.implementation!==r.implementation?(t=nF(r,e.mode,n)).return=e:(t=o(t,r.children||[])).return=e,t}function f(e,t,r,n,i){return null===t||7!==t.tag?(t=nR(r,e.mode,n,i)).return=e:(t=o(t,r)).return=e,t}function p(e,t,r){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=nL(""+t,e.mode,r)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case nQ:return ex(r=nO(t.type,t.key,t.props,null,e.mode,r),t),r.return=e,r;case n$:return(t=nF(t,e.mode,r)).return=e,t;case n9:return p(e,t=(0,t._init)(t._payload),r)}if(iu(t)||s(t))return(t=nR(t,e.mode,r,null)).return=e,t;if("function"==typeof t.then)return p(e,eg(t),r);if(t.$$typeof===n4)return p(e,rl(e,t),r);eb(e,t)}return null}function m(e,t,r,n){var i=null!==t?t.key:null;if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return null!==i?null:h(e,t,""+r,n);if("object"==typeof r&&null!==r){switch(r.$$typeof){case nQ:return r.key===i?c(e,t,r,n):null;case n$:return r.key===i?d(e,t,r,n):null;case n9:return m(e,t,r=(i=r._init)(r._payload),n)}if(iu(r)||s(r))return null!==i?null:f(e,t,r,n,null);if("function"==typeof r.then)return m(e,t,eg(r),n);if(r.$$typeof===n4)return m(e,t,rl(e,r),n);eb(e,r)}return null}function y(e,t,r,n,i){if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return h(t,e=e.get(r)||null,""+n,i);if("object"==typeof n&&null!==n){switch(n.$$typeof){case nQ:return c(t,e=e.get(null===n.key?r:n.key)||null,n,i);case n$:return d(t,e=e.get(null===n.key?r:n.key)||null,n,i);case n9:return y(e,t,r,n=(0,n._init)(n._payload),i)}if(iu(n)||s(n))return f(t,e=e.get(r)||null,n,i,null);if("function"==typeof n.then)return y(e,t,r,eg(n),i);if(n.$$typeof===n4)return y(e,t,r,rl(t,n),i);eb(t,n)}return null}return function(h,c,d,f){try{a3=0;var g=function t(h,c,d,f){if("object"==typeof d&&null!==d&&d.type===nK&&null===d.key&&(d=d.props.children),"object"==typeof d&&null!==d){switch(d.$$typeof){case nQ:e:{for(var g=d.key;null!==c;){if(c.key===g){if((g=d.type)===nK){if(7===c.tag){n(h,c.sibling),(f=o(c,d.props.children)).return=h,h=f;break e}}else if(c.elementType===g||"object"==typeof g&&null!==g&&g.$$typeof===n9&&ev(g)===c.type){n(h,c.sibling),ex(f=o(c,d.props),d),f.return=h,h=f;break e}n(h,c);break}r(h,c),c=c.sibling}d.type===nK?(f=nR(d.props.children,h.mode,f,d.key)).return=h:(ex(f=nO(d.type,d.key,d.props,null,h.mode,f),d),f.return=h),h=f}return u(h);case n$:e:{for(g=d.key;null!==c;){if(c.key===g)if(4===c.tag&&c.stateNode.containerInfo===d.containerInfo&&c.stateNode.implementation===d.implementation){n(h,c.sibling),(f=o(c,d.children||[])).return=h,h=f;break e}else{n(h,c);break}r(h,c),c=c.sibling}(f=nF(d,h.mode,f)).return=h,h=f}return u(h);case n9:return t(h,c,d=(g=d._init)(d._payload),f)}if(iu(d))return function(t,i,s,o){for(var u=null,h=null,c=i,d=i=0,f=null;null!==c&&d<s.length;d++){c.index>d?(f=c,c=null):f=c.sibling;var g=m(t,c,s[d],o);if(null===g){null===c&&(c=f);break}e&&c&&null===g.alternate&&r(t,c),i=l(g,i,d),null===h?u=g:h.sibling=g,h=g,c=f}if(d===s.length)return n(t,c),aT&&P(t,d),u;if(null===c){for(;d<s.length;d++)null!==(c=p(t,s[d],o))&&(i=l(c,i,d),null===h?u=c:h.sibling=c,h=c);return aT&&P(t,d),u}for(c=a(c);d<s.length;d++)null!==(f=y(c,t,d,s[d],o))&&(e&&null!==f.alternate&&c.delete(null===f.key?d:f.key),i=l(f,i,d),null===h?u=f:h.sibling=f,h=f);return e&&c.forEach(function(e){return r(t,e)}),aT&&P(t,d),u}(h,c,d,f);if(s(d)){if("function"!=typeof(g=s(d)))throw Error(i(150));return function(t,s,o,u){if(null==o)throw Error(i(151));for(var h=null,c=null,d=s,f=s=0,g=null,x=o.next();null!==d&&!x.done;f++,x=o.next()){d.index>f?(g=d,d=null):g=d.sibling;var b=m(t,d,x.value,u);if(null===b){null===d&&(d=g);break}e&&d&&null===b.alternate&&r(t,d),s=l(b,s,f),null===c?h=b:c.sibling=b,c=b,d=g}if(x.done)return n(t,d),aT&&P(t,f),h;if(null===d){for(;!x.done;f++,x=o.next())null!==(x=p(t,x.value,u))&&(s=l(x,s,f),null===c?h=x:c.sibling=x,c=x);return aT&&P(t,f),h}for(d=a(d);!x.done;f++,x=o.next())null!==(x=y(d,t,f,x.value,u))&&(e&&null!==x.alternate&&d.delete(null===x.key?f:x.key),s=l(x,s,f),null===c?h=x:c.sibling=x,c=x);return e&&d.forEach(function(e){return r(t,e)}),aT&&P(t,f),h}(h,c,d=g.call(d),f)}if("function"==typeof d.then)return t(h,c,eg(d),f);if(d.$$typeof===n4)return t(h,c,rl(h,d),f);eb(h,d)}return"string"==typeof d&&""!==d||"number"==typeof d||"bigint"==typeof d?(d=""+d,null!==c&&6===c.tag?(n(h,c.sibling),(f=o(c,d)).return=h):(n(h,c),(f=nL(d,h.mode,f)).return=h),u(h=f)):n(h,c)}(h,c,d,f);return a2=null,g}catch(e){if(e===a$)throw e;var x=t(29,e,null,h.mode);return x.lanes=f,x.return=h,x}finally{}}}function eS(e,t){m(a8,e=o4),m(a6,t),o4=e|t.baseLanes}function eM(){m(a8,o4),m(a6,a6.current)}function e_(){o4=a8.current,p(a6),p(a8)}function ez(e){var t=e.alternate;m(oe,1&oe.current),m(a7,e),null===a9&&(null===t||null!==a6.current?a9=e:null!==t.memoizedState&&(a9=e))}function ek(e){if(22===e.tag){if(m(oe,oe.current),m(a7,e),null===a9){var t=e.alternate;null!==t&&null!==t.memoizedState&&(a9=e)}}else eC(e)}function eC(){m(oe,oe.current),m(a7,a7.current)}function eP(e){p(a7),a9===e&&(a9=null),p(oe)}function eE(e){for(var t=e;null!==t;){if(13===t.tag){var r=t.memoizedState;if(null!==r&&(null===(r=r.dehydrated)||sp(r)||sm(r)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function eA(){throw Error(i(321))}function eN(e,t){if(null===t)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!am(e[r],t[r]))return!1;return!0}function eT(e,t,r,n,i,s){return ot=s,or=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,io.H=null===e||null===e.memoizedState?op:om,oo=!1,s=r(n,i),oo=!1,oa&&(s=eO(t,r,n,i)),ej(e),s}function ej(e){io.H=of;var t=null!==on&&null!==on.next;if(ot=0,oi=on=or=null,os=!1,ou=0,oh=null,t)throw Error(i(300));null===e||ov||null!==(e=e.dependencies)&&rs(e)&&(ov=!0)}function eO(e,t,r,n){or=e;var s=0;do{if(oa&&(oh=null),ou=0,oa=!1,25<=s)throw Error(i(301));if(s+=1,oi=on=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}io.H=oy,a=t(r,n)}while(oa);return a}function eR(){var e=io.H,t=e.useState()[0];return t="function"==typeof t.then?eD(t):t,e=e.useState()[0],(null!==on?on.memoizedState:null)!==e&&(or.flags|=1024),t}function eI(){var e=0!==ol;return ol=0,e}function eL(e,t,r){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r}function eF(e){if(os){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}os=!1}ot=0,oi=on=or=null,oa=!1,ou=ol=0,oh=null}function eV(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===oi?or.memoizedState=oi=e:oi=oi.next=e,oi}function eB(){if(null===on){var e=or.alternate;e=null!==e?e.memoizedState:null}else e=on.next;var t=null===oi?or.memoizedState:oi.next;if(null!==t)oi=t,on=e;else{if(null===e){if(null===or.alternate)throw Error(i(467));throw Error(i(310))}e={memoizedState:(on=e).memoizedState,baseState:on.baseState,baseQueue:on.baseQueue,queue:on.queue,next:null},null===oi?or.memoizedState=oi=e:oi=oi.next=e}return oi}function eD(e){var t=ou;return ou+=1,null===oh&&(oh=[]),e=em(oh,e,t),t=or,null===(null===oi?t.memoizedState:oi.next)&&(io.H=null===(t=t.alternate)||null===t.memoizedState?op:om),e}function eU(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return eD(e);if(e.$$typeof===n4)return ro(e)}throw Error(i(438,String(e)))}function eW(e){var t=null,r=or.updateQueue;if(null!==r&&(t=r.memoCache),null==t){var n=or.alternate;null!==n&&null!==(n=n.updateQueue)&&null!=(n=n.memoCache)&&(t={data:n.data.map(function(e){return e.slice()}),index:0})}if(null==t&&(t={data:[],index:0}),null===r&&(r=od(),or.updateQueue=r),r.memoCache=t,void 0===(r=t.data[t.index]))for(r=t.data[t.index]=Array(e),n=0;n<e;n++)r[n]=ii;return t.index++,r}function eH(e,t){return"function"==typeof t?t(e):t}function eq(e){return eJ(eB(),on,e)}function eJ(e,t,r){var n=e.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=r;var s=e.baseQueue,a=n.pending;if(null!==a){if(null!==s){var o=s.next;s.next=a.next,a.next=o}t.baseQueue=s=a,n.pending=null}if(a=e.baseState,null===s)e.memoizedState=a;else{t=s.next;var l=o=null,u=null,h=t,c=!1;do{var d=-0x20000001&h.lane;if(d!==h.lane?(o$&d)===d:(ot&d)===d){var f=h.revertLane;if(0===f)null!==u&&(u=u.next={lane:0,revertLane:0,action:h.action,hasEagerState:h.hasEagerState,eagerState:h.eagerState,next:null}),d===aZ&&(c=!0);else if((ot&f)===f){h=h.next,f===aZ&&(c=!0);continue}else d={lane:0,revertLane:h.revertLane,action:h.action,hasEagerState:h.hasEagerState,eagerState:h.eagerState,next:null},null===u?(l=u=d,o=a):u=u.next=d,or.lanes|=f,o6|=f;d=h.action,oo&&r(a,d),a=h.hasEagerState?h.eagerState:r(a,d)}else f={lane:d,revertLane:h.revertLane,action:h.action,hasEagerState:h.hasEagerState,eagerState:h.eagerState,next:null},null===u?(l=u=f,o=a):u=u.next=f,or.lanes|=d,o6|=d;h=h.next}while(null!==h&&h!==t);if(null===u?o=a:u.next=l,!am(a,e.memoizedState)&&(ov=!0,c&&null!==(r=aY)))throw r;e.memoizedState=a,e.baseState=o,e.baseQueue=u,n.lastRenderedState=a}return null===s&&(n.lanes=0),[e.memoizedState,n.dispatch]}function eZ(e){var t=eB(),r=t.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=e;var n=r.dispatch,s=r.pending,a=t.memoizedState;if(null!==s){r.pending=null;var o=s=s.next;do a=e(a,o.action),o=o.next;while(o!==s);am(a,t.memoizedState)||(ov=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),r.lastRenderedState=a}return[a,n]}function eY(e,t,r){var n=or,s=eB(),a=aT;if(a){if(void 0===r)throw Error(i(407));r=r()}else r=t();var o=!am((on||s).memoizedState,r);if(o&&(s.memoizedState=r,ov=!0),s=s.queue,tu(eQ.bind(null,n,s,e),[e]),s.getSnapshot!==t||o||null!==oi&&1&oi.memoizedState.tag){if(n.flags|=2048,ti(9,eG.bind(null,n,s,r,t),{destroy:void 0},null),null===oG)throw Error(i(349));a||0!=(60&ot)||eX(n,t,r)}return r}function eX(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},null===(t=or.updateQueue)?(t=od(),or.updateQueue=t,t.stores=[e]):null===(r=t.stores)?t.stores=[e]:r.push(e)}function eG(e,t,r,n){t.value=r,t.getSnapshot=n,e$(t)&&eK(e)}function eQ(e,t,r){return r(function(){e$(t)&&eK(e)})}function e$(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!am(e,r)}catch(e){return!0}}function eK(e){var t=q(e,2);null!==t&&nt(t,e,2)}function e0(e){var t=eV();if("function"==typeof e){var r=e;if(e=r(),oo){k(!0);try{r()}finally{k(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:eH,lastRenderedState:e},t}function e1(e,t,r,n){return e.baseState=r,eJ(e,on,"function"==typeof n?n:eH)}function e2(e,t,r,n,s){if(tE(e))throw Error(i(485));if(null!==(e=t.action)){var a={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==io.T?r(!0):a.isTransition=!1,n(a),null===(r=t.pending)?(a.next=t.pending=a,e3(t,a)):(a.next=r.next,t.pending=r.next=a)}}function e3(e,t){var r=t.action,n=t.payload,i=e.state;if(t.isTransition){var s=io.T,a={};io.T=a;try{var o=r(i,n),l=io.S;null!==l&&l(a,o),e4(e,t,o)}catch(r){e6(e,t,r)}finally{io.T=s}}else try{s=r(i,n),e4(e,t,s)}catch(r){e6(e,t,r)}}function e4(e,t,r){null!==r&&"object"==typeof r&&"function"==typeof r.then?r.then(function(r){e5(e,t,r)},function(r){return e6(e,t,r)}):e5(e,t,r)}function e5(e,t,r){t.status="fulfilled",t.value=r,e8(t),e.state=r,null!==(t=e.pending)&&((r=t.next)===t?e.pending=null:(r=r.next,t.next=r,e3(e,r)))}function e6(e,t,r){var n=e.pending;if(e.pending=null,null!==n){n=n.next;do t.status="rejected",t.reason=r,e8(t),t=t.next;while(t!==n)}e.action=null}function e8(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function e7(e,t){return t}function e9(e,t){if(aT){var r=oG.formState;if(null!==r){e:{var n=or;if(aT){if(aN){var i=sx(aN,aO);if(i){aN=sv(i),n=sb(i);break e}}I(n)}n=!1}n&&(t=r[0])}}(r=eV()).memoizedState=r.baseState=t,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e7,lastRenderedState:t},r.queue=n,r=tk.bind(null,or,n),n.dispatch=r,n=e0(!1);var s=tP.bind(null,or,!1,n.queue);return n=eV(),i={state:t,dispatch:null,action:e,pending:null},n.queue=i,r=e2.bind(null,or,i,s,r),i.dispatch=r,n.memoizedState=e,[t,r,!1]}function te(e){return tt(eB(),on,e)}function tt(e,t,r){t=eJ(e,t,e7)[0],e=eq(eH)[0],t="object"==typeof t&&null!==t&&"function"==typeof t.then?eD(t):t;var n=eB(),i=n.queue,s=i.dispatch;return r!==n.memoizedState&&(or.flags|=2048,ti(9,tr.bind(null,i,r),{destroy:void 0},null)),[t,s,e]}function tr(e,t){e.action=t}function tn(e){var t=eB(),r=on;if(null!==r)return tt(t,r,e);eB(),t=t.memoizedState;var n=(r=eB()).queue.dispatch;return r.memoizedState=e,[t,n,!1]}function ti(e,t,r,n){return e={tag:e,create:t,inst:r,deps:n,next:null},null===(t=or.updateQueue)&&(t=od(),or.updateQueue=t),null===(r=t.lastEffect)?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e),e}function ts(){return eB().memoizedState}function ta(e,t,r,n){var i=eV();or.flags|=e,i.memoizedState=ti(1|t,r,{destroy:void 0},void 0===n?null:n)}function to(e,t,r,n){var i=eB();n=void 0===n?null:n;var s=i.memoizedState.inst;null!==on&&null!==n&&eN(n,on.memoizedState.deps)?i.memoizedState=ti(t,r,s,n):(or.flags|=e,i.memoizedState=ti(1|t,r,s,n))}function tl(e,t){ta(8390656,8,e,t)}function tu(e,t){to(2048,8,e,t)}function th(e,t){return to(4,2,e,t)}function tc(e,t){return to(4,4,e,t)}function td(e,t){if("function"==typeof t){var r=t(e=e());return function(){"function"==typeof r?r():t(null)}}if(null!=t)return t.current=e=e(),function(){t.current=null}}function tf(e,t,r){r=null!=r?r.concat([e]):null,to(4,4,td.bind(null,t,e),r)}function tp(){}function tm(e,t){var r=eB();t=void 0===t?null:t;var n=r.memoizedState;return null!==t&&eN(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function ty(e,t){var r=eB();t=void 0===t?null:t;var n=r.memoizedState;if(null!==t&&eN(t,n[1]))return n[0];if(n=e(),oo){k(!0);try{e()}finally{k(!1)}}return r.memoizedState=[n,t],n}function tg(e,t,r){return void 0===r||0!=(0x40000000&ot)?e.memoizedState=t:(e.memoizedState=r,e=ne(),or.lanes|=e,o6|=e,r)}function tx(e,t,r,n){return am(r,t)?r:null!==a6.current?(am(e=tg(e,r,n),t)||(ov=!0),e):0==(42&ot)?(ov=!0,e.memoizedState=r):(e=ne(),or.lanes|=e,o6|=e,t)}function tb(e,t,r,n,i){var s=iO();ij(0!==s&&8>s?s:8);var a=io.T,o={};io.T=o,tP(e,!1,t,r);try{var l=i(),u=io.S;if(null!==u&&u(o,l),null!==l&&"object"==typeof l&&"function"==typeof l.then){var h,c,d=(h=[],c={status:"pending",value:null,reason:null,then:function(e){h.push(e)}},l.then(function(){c.status="fulfilled",c.value=n;for(var e=0;e<h.length;e++)(0,h[e])(n)},function(e){for(c.status="rejected",c.reason=e,e=0;e<h.length;e++)(0,h[e])(void 0)}),c);tC(e,t,d,r9(e))}else tC(e,t,n,r9(e))}catch(r){tC(e,t,{then:function(){},status:"rejected",reason:r},r9())}finally{ij(s),io.T=a}}function tv(e){var t=e.memoizedState;if(null!==t)return t;var r={};return(t={memoizedState:iW,baseState:iW,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:eH,lastRenderedState:iW},next:null}).next={memoizedState:r,baseState:r,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:eH,lastRenderedState:r},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function tw(){return ro(iH)}function tS(){return eB().memoizedState}function tM(){return eB().memoizedState}function t_(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var r=r9(),n=es(t,e=ei(r),r);null!==n&&(nt(n,t,r),ea(n,t,r)),t={cache:rh()},e.payload=t;return}t=t.return}}function tz(e,t,r){var n=r9();r={lane:n,revertLane:0,action:r,hasEagerState:!1,eagerState:null,next:null},tE(e)?tA(t,r):null!==(r=H(e,t,r,n))&&(nt(r,e,n),tN(r,t,n))}function tk(e,t,r){tC(e,t,r,r9())}function tC(e,t,r,n){var i={lane:n,revertLane:0,action:r,hasEagerState:!1,eagerState:null,next:null};if(tE(e))tA(t,i);else{var s=e.alternate;if(0===e.lanes&&(null===s||0===s.lanes)&&null!==(s=t.lastRenderedReducer))try{var a=t.lastRenderedState,o=s(a,r);if(i.hasEagerState=!0,i.eagerState=o,am(o,a))return W(e,t,i,0),null===oG&&U(),!1}catch(e){}finally{}if(null!==(r=H(e,t,i,n)))return nt(r,e,n),tN(r,t,n),!0}return!1}function tP(e,t,r,n){if(n={lane:2,revertLane:ee(),action:n,hasEagerState:!1,eagerState:null,next:null},tE(e)){if(t)throw Error(i(479))}else null!==(t=H(e,r,n,2))&&nt(t,e,2)}function tE(e){var t=e.alternate;return e===or||null!==t&&t===or}function tA(e,t){oa=os=!0;var r=e.pending;null===r?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function tN(e,t,r){if(0!=(4194176&r)){var n=t.lanes;n&=e.pendingLanes,t.lanes=r|=n,_(e,r)}}function tT(e,t,r,n){r=null==(r=r(n,t=e.memoizedState))?t:nX({},t,r),e.memoizedState=r,0===e.lanes&&(e.updateQueue.baseState=r)}function tj(e,t,r,n,i,s,a){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(n,s,a):!t.prototype||!t.prototype.isPureReactComponent||!ed(r,n)||!ed(i,s)}function tO(e,t,r,n){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(r,n),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&og.enqueueReplaceState(t,t.state,null)}function tR(e,t){var r=t;if("ref"in t)for(var n in r={},t)"ref"!==n&&(r[n]=t[n]);if(e=e.defaultProps)for(var i in r===t&&(r=nX({},r)),e)void 0===r[i]&&(r[i]=e[i]);return r}function tI(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(e){setTimeout(function(){throw e})}}function tL(e,t,r){try{(0,e.onCaughtError)(r.value,{componentStack:r.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(e){setTimeout(function(){throw e})}}function tF(e,t,r){return(r=ei(r)).tag=3,r.payload={element:null},r.callback=function(){tI(e,t)},r}function tV(e){return(e=ei(e)).tag=3,e}function tB(e,t,r,n){var i=r.type.getDerivedStateFromError;if("function"==typeof i){var s=n.value;e.payload=function(){return i(s)},e.callback=function(){tL(t,r,n)}}var a=r.stateNode;null!==a&&"function"==typeof a.componentDidCatch&&(e.callback=function(){tL(t,r,n),"function"!=typeof i&&(null===lo?lo=new Set([this]):lo.add(this));var e=n.stack;this.componentDidCatch(n.value,{componentStack:null!==e?e:""})})}function tD(e,t,r,n){t.child=null===e?a5(t,null,r,n):a4(t,e.child,r,n)}function tU(e,t,r,n,i){r=r.render;var s=t.ref;if("ref"in n){var a={};for(var o in n)"ref"!==o&&(a[o]=n[o])}else a=n;return(ra(t),n=eT(e,t,r,a,s,i),o=eI(),null===e||ov)?(aT&&o&&A(t),t.flags|=1,tD(e,t,n,i),t.child):(eL(e,t,i),t8(e,t,i))}function tW(e,t,r,n,i){if(null===e){var s=r.type;return"function"!=typeof s||nN(s)||void 0!==s.defaultProps||null!==r.compare?((e=nO(r.type,null,n,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=s,tH(e,t,s,n,i))}if(s=e.child,!t7(e,i)){var a=s.memoizedProps;if((r=null!==(r=r.compare)?r:ed)(a,n)&&e.ref===t.ref)return t8(e,t,i)}return t.flags|=1,(e=nT(s,n)).ref=t.ref,e.return=t,t.child=e}function tH(e,t,r,n,i){if(null!==e){var s=e.memoizedProps;if(ed(s,n)&&e.ref===t.ref)if(ov=!1,t.pendingProps=n=s,!t7(e,i))return t.lanes=e.lanes,t8(e,t,i);else 0!=(131072&e.flags)&&(ov=!0)}return tY(e,t,r,n,i)}function tq(e,t,r){var n=t.pendingProps,i=n.children,s=0!=(2&t.stateNode._pendingVisibility),a=null!==e?e.memoizedState:null;if(tZ(e,t),"hidden"===n.mode||s){if(0!=(128&t.flags)){if(n=null!==a?a.baseLanes|r:r,null!==e){for(s=0,i=t.child=e.child;null!==i;)s=s|i.lanes|i.childLanes,i=i.sibling;t.childLanes=s&~n}else t.childLanes=0,t.child=null;return tJ(e,t,n,r)}if(0==(0x20000000&r))return t.lanes=t.childLanes=0x20000000,tJ(e,t,null!==a?a.baseLanes|r:r,r);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&rf(t,null!==a?a.cachePool:null),null!==a?eS(t,a):eM(),ek(t)}else null!==a?(rf(t,a.cachePool),eS(t,a),eC(t),t.memoizedState=null):(null!==e&&rf(t,null),eM(),eC(t));return tD(e,t,i,r),t.child}function tJ(e,t,r,n){var i=rd();return t.memoizedState={baseLanes:r,cachePool:i=null===i?null:{parent:iC?oP._currentValue:oP._currentValue2,pool:i}},null!==e&&rf(t,null),eM(),ek(t),null!==e&&ri(e,t,n,!0),null}function tZ(e,t){var r=t.ref;if(null===r)null!==e&&null!==e.ref&&(t.flags|=2097664);else{if("function"!=typeof r&&"object"!=typeof r)throw Error(i(284));(null===e||e.ref!==r)&&(t.flags|=2097664)}}function tY(e,t,r,n,i){return(ra(t),r=eT(e,t,r,n,void 0,i),n=eI(),null===e||ov)?(aT&&n&&A(t),t.flags|=1,tD(e,t,r,i),t.child):(eL(e,t,i),t8(e,t,i))}function tX(e,t,r,n,i,s){return(ra(t),t.updateQueue=null,r=eO(t,n,r,i),ej(e),n=eI(),null===e||ov)?(aT&&n&&A(t),t.flags|=1,tD(e,t,r,s),t.child):(eL(e,t,s),t8(e,t,s))}function tG(e,t,r,n,i){if(ra(t),null===t.stateNode){var s=s6,a=r.contextType;"object"==typeof a&&null!==a&&(s=ro(a)),t.memoizedState=null!==(s=new r(n,s)).state&&void 0!==s.state?s.state:null,s.updater=og,t.stateNode=s,s._reactInternals=t,(s=t.stateNode).props=n,s.state=t.memoizedState,s.refs={},er(t),a=r.contextType,s.context="object"==typeof a&&null!==a?ro(a):s6,s.state=t.memoizedState,"function"==typeof(a=r.getDerivedStateFromProps)&&(tT(t,r,a,n),s.state=t.memoizedState),"function"==typeof r.getDerivedStateFromProps||"function"==typeof s.getSnapshotBeforeUpdate||"function"!=typeof s.UNSAFE_componentWillMount&&"function"!=typeof s.componentWillMount||(a=s.state,"function"==typeof s.componentWillMount&&s.componentWillMount(),"function"==typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount(),a!==s.state&&og.enqueueReplaceState(s,s.state,null),eu(t,n,s,i),el(),s.state=t.memoizedState),"function"==typeof s.componentDidMount&&(t.flags|=4194308),n=!0}else if(null===e){s=t.stateNode;var o=t.memoizedProps,l=tR(r,o);s.props=l;var u=s.context,h=r.contextType;a=s6,"object"==typeof h&&null!==h&&(a=ro(h));var c=r.getDerivedStateFromProps;h="function"==typeof c||"function"==typeof s.getSnapshotBeforeUpdate,o=t.pendingProps!==o,h||"function"!=typeof s.UNSAFE_componentWillReceiveProps&&"function"!=typeof s.componentWillReceiveProps||(o||u!==a)&&tO(t,s,n,a),aX=!1;var d=t.memoizedState;s.state=d,eu(t,n,s,i),el(),u=t.memoizedState,o||d!==u||aX?("function"==typeof c&&(tT(t,r,c,n),u=t.memoizedState),(l=aX||tj(t,r,l,n,d,u,a))?(h||"function"!=typeof s.UNSAFE_componentWillMount&&"function"!=typeof s.componentWillMount||("function"==typeof s.componentWillMount&&s.componentWillMount(),"function"==typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"==typeof s.componentDidMount&&(t.flags|=4194308)):("function"==typeof s.componentDidMount&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=u),s.props=n,s.state=u,s.context=a,n=l):("function"==typeof s.componentDidMount&&(t.flags|=4194308),n=!1)}else{s=t.stateNode,en(e,t),h=tR(r,a=t.memoizedProps),s.props=h,c=t.pendingProps,d=s.context,u=r.contextType,l=s6,"object"==typeof u&&null!==u&&(l=ro(u)),(u="function"==typeof(o=r.getDerivedStateFromProps)||"function"==typeof s.getSnapshotBeforeUpdate)||"function"!=typeof s.UNSAFE_componentWillReceiveProps&&"function"!=typeof s.componentWillReceiveProps||(a!==c||d!==l)&&tO(t,s,n,l),aX=!1,d=t.memoizedState,s.state=d,eu(t,n,s,i),el();var f=t.memoizedState;a!==c||d!==f||aX||null!==e&&null!==e.dependencies&&rs(e.dependencies)?("function"==typeof o&&(tT(t,r,o,n),f=t.memoizedState),(h=aX||tj(t,r,h,n,d,f,l)||null!==e&&null!==e.dependencies&&rs(e.dependencies))?(u||"function"!=typeof s.UNSAFE_componentWillUpdate&&"function"!=typeof s.componentWillUpdate||("function"==typeof s.componentWillUpdate&&s.componentWillUpdate(n,f,l),"function"==typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(n,f,l)),"function"==typeof s.componentDidUpdate&&(t.flags|=4),"function"==typeof s.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof s.componentDidUpdate||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof s.getSnapshotBeforeUpdate||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=f),s.props=n,s.state=f,s.context=l,n=h):("function"!=typeof s.componentDidUpdate||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof s.getSnapshotBeforeUpdate||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),n=!1)}return s=n,tZ(e,t),n=0!=(128&t.flags),s||n?(s=t.stateNode,r=n&&"function"!=typeof r.getDerivedStateFromError?null:s.render(),t.flags|=1,null!==e&&n?(t.child=a4(t,e.child,null,i),t.child=a4(t,null,r,i)):tD(e,t,r,i),t.memoizedState=s.state,e=t.child):e=t8(e,t,i),e}function tQ(e,t,r,n){return B(),t.flags|=256,tD(e,t,r,n),t.child}function t$(e){return{baseLanes:e,cachePool:rp()}}function tK(e,t,r){return e=null!==e?e.childLanes&~r:0,t&&(e|=o9),e}function t0(e,r,n){var s,a=r.pendingProps,o=!1,l=0!=(128&r.flags);if((s=l)||(s=(null===e||null!==e.memoizedState)&&0!=(2&oe.current)),s&&(o=!0,r.flags&=-129),s=0!=(32&r.flags),r.flags&=-33,null===e){if(aT){if(o?ez(r):eC(r),aT){var u,h=aN;(u=h)&&(null!==(h=sk(h,aO))?(r.memoizedState={dehydrated:h,treeContext:null!==aM?{id:a_,overflow:az}:null,retryLane:0x20000000},(u=t(18,null,null,0)).stateNode=h,u.return=r,r.child=u,aA=r,aN=null,u=!0):u=!1),u||I(r)}if(null!==(h=r.memoizedState)&&null!==(h=h.dehydrated))return sm(h)?r.lanes=16:r.lanes=0x20000000,null;eP(r)}return(h=a.children,a=a.fallback,o)?(eC(r),h=t2({mode:"hidden",children:h},o=r.mode),a=nR(a,o,n,null),h.return=r,a.return=r,h.sibling=a,r.child=h,(o=r.child).memoizedState=t$(n),o.childLanes=tK(e,s,n),r.memoizedState=ow,a):(ez(r),t1(r,h))}if(null!==(u=e.memoizedState)&&null!==(h=u.dehydrated)){if(l)256&r.flags?(ez(r),r.flags&=-257,r=t3(e,r,n)):null!==r.memoizedState?(eC(r),r.child=e.child,r.flags|=128,r=null):(eC(r),o=a.fallback,h=r.mode,a=t2({mode:"visible",children:a.children},h),o=nR(o,h,n,null),o.flags|=2,a.return=r,o.return=r,a.sibling=o,r.child=a,a4(r,e.child,null,n),(a=r.child).memoizedState=t$(n),a.childLanes=tK(e,s,n),r.memoizedState=ow,r=o);else if(ez(r),sm(h))s=sy(h).digest,(a=Error(i(419))).stack="",a.digest=s,D({value:a,source:null,stack:null}),r=t3(e,r,n);else if(ov||ri(e,r,n,!1),s=0!=(n&e.childLanes),ov||s){if(null!==(s=oG)){if(0!=(42&(a=n&-n)))a=1;else switch(a){case 2:a=1;break;case 8:a=4;break;case 32:a=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 0x1000000:case 0x2000000:a=64;break;case 0x10000000:a=0x8000000;break;default:a=0}if(0!==(a=0!=(a&(s.suspendedLanes|n))?0:a)&&a!==u.retryLane)throw u.retryLane=a,q(e,a),nt(s,e,a),ob}sp(h)||nf(),r=t3(e,r,n)}else sp(h)?(r.flags|=128,r.child=e.child,sg(h,r=nP.bind(null,e)),r=null):(e=u.treeContext,iA&&(aN=sM(h),aA=r,aT=!0,aj=null,aO=!1,null!==e&&(aw[aS++]=a_,aw[aS++]=az,aw[aS++]=aM,a_=e.id,az=e.overflow,aM=r)),r=t1(r,a.children),r.flags|=4096);return r}return o?(eC(r),o=a.fallback,h=r.mode,l=(u=e.child).sibling,(a=nT(u,{mode:"hidden",children:a.children})).subtreeFlags=0x1e00000&u.subtreeFlags,null!==l?o=nT(l,o):(o=nR(o,h,n,null),o.flags|=2),o.return=r,a.return=r,a.sibling=o,r.child=a,a=o,o=r.child,null===(h=e.child.memoizedState)?h=t$(n):(null!==(u=h.cachePool)?(l=iC?oP._currentValue:oP._currentValue2,u=u.parent!==l?{parent:l,pool:l}:u):u=rp(),h={baseLanes:h.baseLanes|n,cachePool:u}),o.memoizedState=h,o.childLanes=tK(e,s,n),r.memoizedState=ow,a):(ez(r),e=(n=e.child).sibling,(n=nT(n,{mode:"visible",children:a.children})).return=r,n.sibling=null,null!==e&&(null===(s=r.deletions)?(r.deletions=[e],r.flags|=16):s.push(e)),r.child=n,r.memoizedState=null,n)}function t1(e,t){return(t=t2({mode:"visible",children:t},e.mode)).return=e,e.child=t}function t2(e,t){return nI(e,t,0,null)}function t3(e,t,r){return a4(t,e.child,null,r),e=t1(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function t4(e,t,r){e.lanes|=t;var n=e.alternate;null!==n&&(n.lanes|=t),rr(e.return,t,r)}function t5(e,t,r,n,i){var s=e.memoizedState;null===s?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=n,s.tail=r,s.tailMode=i)}function t6(e,t,r){var n=t.pendingProps,i=n.revealOrder,s=n.tail;if(tD(e,t,n.children,r),0!=(2&(n=oe.current)))n=1&n|2,t.flags|=128;else{if(null!==e&&0!=(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&t4(e,r,t);else if(19===e.tag)t4(e,r,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}switch(m(oe,n),i){case"forwards":for(i=null,r=t.child;null!==r;)null!==(e=r.alternate)&&null===eE(e)&&(i=r),r=r.sibling;null===(r=i)?(i=t.child,t.child=null):(i=r.sibling,r.sibling=null),t5(t,!1,i,r,s);break;case"backwards":for(r=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===eE(e)){t.child=i;break}e=i.sibling,i.sibling=r,r=i,i=e}t5(t,!0,r,null,s);break;case"together":t5(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function t8(e,t,r){if(null!==e&&(t.dependencies=e.dependencies),o6|=t.lanes,0==(r&t.childLanes)){if(null===e)return null;else if(ri(e,t,r,!1),0==(r&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(r=nT(e=t.child,e.pendingProps),t.child=r,r.return=t;null!==e.sibling;)e=e.sibling,(r=r.sibling=nT(e,e.pendingProps)).return=t;r.sibling=null}return t.child}function t7(e,t){return 0!=(e.lanes&t)||!!(null!==(e=e.dependencies)&&rs(e))}function t9(e,t,r){if(null!==e)if(e.memoizedProps!==t.pendingProps)ov=!0;else{if(!t7(e,r)&&0==(128&t.flags))return ov=!1,function(e,t,r){switch(t.tag){case 3:T(t,t.stateNode.containerInfo),re(t,oP,e.memoizedState.cache),B();break;case 27:case 5:O(t);break;case 4:T(t,t.stateNode.containerInfo);break;case 10:re(t,t.type,t.memoizedProps.value);break;case 13:var n=t.memoizedState;if(null!==n){if(null!==n.dehydrated)return ez(t),t.flags|=128,null;if(0!=(r&t.child.childLanes))return t0(e,t,r);return ez(t),null!==(e=t8(e,t,r))?e.sibling:null}ez(t);break;case 19:var i=0!=(128&e.flags);if((n=0!=(r&t.childLanes))||(ri(e,t,r,!1),n=0!=(r&t.childLanes)),i){if(n)return t6(e,t,r);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),m(oe,oe.current),!n)return null;break;case 22:case 23:return t.lanes=0,tq(e,t,r);case 24:re(t,oP,e.memoizedState.cache)}return t8(e,t,r)}(e,t,r);ov=0!=(131072&e.flags)}else ov=!1,aT&&0!=(1048576&t.flags)&&E(t,av,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var n=t.elementType,s=n._init;if(n=s(n._payload),t.type=n,"function"==typeof n)nN(n)?(e=tR(n,e),t.tag=1,t=tG(null,t,n,e,r)):(t.tag=0,t=tY(null,t,n,e,r));else{if(null!=n){if((s=n.$$typeof)===n5){t.tag=11,t=tU(null,t,n,e,r);break e}else if(s===n7){t.tag=14,t=tW(null,t,n,e,r);break e}}throw Error(i(306,t=a(n)||n,""))}}return t;case 0:return tY(e,t,t.type,t.pendingProps,r);case 1:return s=tR(n=t.type,t.pendingProps),tG(e,t,n,s,r);case 3:e:{if(T(t,t.stateNode.containerInfo),null===e)throw Error(i(387));var o=t.pendingProps;n=(s=t.memoizedState).element,en(e,t),eu(t,o,null,r);var l=t.memoizedState;if(re(t,oP,o=l.cache),o!==s.cache&&rn(t,[oP],r,!0),el(),o=l.element,iA&&s.isDehydrated)if(s={element:o,isDehydrated:!1,cache:l.cache},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=tQ(e,t,o,r);break e}else if(o!==n){D(n=C(Error(i(424)),t)),t=tQ(e,t,o,r);break e}else for(iA&&(aN=sS(t.stateNode.containerInfo),aA=t,aT=!0,aj=null,aO=!0),r=a5(t,null,o,r),t.child=r;r;)r.flags=-3&r.flags|4096,r=r.sibling;else{if(B(),o===n){t=t8(e,t,r);break e}tD(e,t,o,r)}t=t.child}return t;case 26:if(sF)return tZ(e,t),null===e?(r=sD(t.type,null,t.pendingProps,null))?t.memoizedState=r:aT||(t.stateNode=sZ(t.type,t.pendingProps,aP.current,t)):t.memoizedState=sD(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:if(s$)return O(t),null===e&&s$&&aT&&(n=t.stateNode=sK(t.type,t.pendingProps,aP.current,ak.current,!1),aA=t,aO=!0,aN=sw(n)),n=t.pendingProps.children,null!==e||aT?tD(e,t,n,r):t.child=a4(t,null,n,r),tZ(e,t),t.child;case 5:return null===e&&aT&&(sI(t.type,t.pendingProps,ak.current),(s=n=aN)&&(null!==(n=s_(n,t.type,t.pendingProps,aO))?(t.stateNode=n,aA=t,aN=sw(n),aO=!1,s=!0):s=!1),s||I(t)),O(t),s=t.type,o=t.pendingProps,l=null!==e?e.memoizedProps:null,n=o.children,iS(s,o)?n=null:null!==l&&iS(s,l)&&(t.flags|=32),null!==t.memoizedState&&(s=eT(e,t,eR,null,null,r),iC?iH._currentValue=s:iH._currentValue2=s),tZ(e,t),tD(e,t,n,r),t.child;case 6:return null===e&&aT&&(sL(t.pendingProps,ak.current),(e=r=aN)&&(null!==(r=sz(r,t.pendingProps,aO))?(t.stateNode=r,aA=t,aN=null,e=!0):e=!1),e||I(t)),null;case 13:return t0(e,t,r);case 4:return T(t,t.stateNode.containerInfo),n=t.pendingProps,null===e?t.child=a4(t,null,n,r):tD(e,t,n,r),t.child;case 11:return tU(e,t,t.type,t.pendingProps,r);case 7:return tD(e,t,t.pendingProps,r),t.child;case 8:case 12:return tD(e,t,t.pendingProps.children,r),t.child;case 10:return n=t.pendingProps,re(t,t.type,n.value),tD(e,t,n.children,r),t.child;case 9:return s=t.type._context,n=t.pendingProps.children,ra(t),n=n(s=ro(s)),t.flags|=1,tD(e,t,n,r),t.child;case 14:return tW(e,t,t.type,t.pendingProps,r);case 15:return tH(e,t,t.type,t.pendingProps,r);case 19:return t6(e,t,r);case 22:return tq(e,t,r);case 24:return ra(t),n=ro(oP),null===e?(null===(s=rd())&&(s=oG,o=rh(),s.pooledCache=o,o.refCount++,null!==o&&(s.pooledCacheLanes|=r),s=o),t.memoizedState={parent:n,cache:s},er(t),re(t,oP,s)):(0!=(e.lanes&r)&&(en(e,t),eu(t,null,null,r),el()),s=e.memoizedState,o=t.memoizedState,s.parent!==n?(s={parent:n,cache:n},t.memoizedState=s,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=s),re(t,oP,n)):(re(t,oP,n=o.cache),n!==s.cache&&rn(t,[oP],r,!0))),tD(e,t,t.pendingProps.children,r),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function re(e,t,r){iC?(m(oS,t._currentValue),t._currentValue=r):(m(oS,t._currentValue2),t._currentValue2=r)}function rt(e){var t=oS.current;iC?e._currentValue=t:e._currentValue2=t,p(oS)}function rr(e,t,r){for(;null!==e;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==n&&(n.childLanes|=t)):null!==n&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function rn(e,t,r,n){var s=e.child;for(null!==s&&(s.return=e);null!==s;){var a=s.dependencies;if(null!==a){var o=s.child;a=a.firstContext;e:for(;null!==a;){var l=a;a=s;for(var u=0;u<t.length;u++)if(l.context===t[u]){a.lanes|=r,null!==(l=a.alternate)&&(l.lanes|=r),rr(a.return,r,e),n||(o=null);break e}a=l.next}}else if(18===s.tag){if(null===(o=s.return))throw Error(i(341));o.lanes|=r,null!==(a=o.alternate)&&(a.lanes|=r),rr(o,r,e),o=null}else o=s.child;if(null!==o)o.return=s;else for(o=s;null!==o;){if(o===e){o=null;break}if(null!==(s=o.sibling)){s.return=o.return,o=s;break}o=o.return}s=o}}function ri(e,t,r,n){e=null;for(var s=t,a=!1;null!==s;){if(!a){if(0!=(524288&s.flags))a=!0;else if(0!=(262144&s.flags))break}if(10===s.tag){var o=s.alternate;if(null===o)throw Error(i(387));if(null!==(o=o.memoizedProps)){var l=s.type;am(s.pendingProps.value,o.value)||(null!==e?e.push(l):e=[l])}}else if(s===aE.current){if(null===(o=s.alternate))throw Error(i(387));o.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(null!==e?e.push(iH):e=[iH])}s=s.return}null!==e&&rn(t,e,r,n),t.flags|=262144}function rs(e){for(e=e.firstContext;null!==e;){var t=e.context;if(!am(iC?t._currentValue:t._currentValue2,e.memoizedValue))return!0;e=e.next}return!1}function ra(e){oM=e,o_=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function ro(e){return ru(oM,e)}function rl(e,t){return null===oM&&ra(e),ru(e,t)}function ru(e,t){var r=iC?t._currentValue:t._currentValue2;if(t={context:t,memoizedValue:r,next:null},null===o_){if(null===e)throw Error(i(308));o_=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else o_=o_.next=t;return r}function rh(){return{controller:new oz,data:new Map,refCount:0}}function rc(e){e.refCount--,0===e.refCount&&ok(oC,function(){e.controller.abort()})}function rd(){var e=oA.current;return null!==e?e:oG.pooledCache}function rf(e,t){null===t?m(oA,oA.current):m(oA,t.pool)}function rp(){var e=rd();return null===e?null:{parent:iC?oP._currentValue:oP._currentValue2,pool:e}}function rm(e){e.flags|=4}function ry(e,t){if(null!==e&&e.child===t.child)return!1;if(0!=(16&t.flags))return!0;for(e=t.child;null!==e;){if(0!=(13878&e.flags)||0!=(13878&e.subtreeFlags))return!0;e=e.sibling}return!1}function rg(e,t,r,n){if(iP)for(r=t.child;null!==r;){if(5===r.tag||6===r.tag)iv(e,r.stateNode);else if(!(4===r.tag||s$&&27===r.tag)&&null!==r.child){r.child.return=r,r=r.child;continue}if(r===t)break;for(;null===r.sibling;){if(null===r.return||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}else if(iE)for(var i=t.child;null!==i;){if(5===i.tag){var s=i.stateNode;r&&n&&(s=sd(s,i.type,i.memoizedProps)),iv(e,s)}else if(6===i.tag)s=i.stateNode,r&&n&&(s=sf(s,i.memoizedProps)),iv(e,s);else if(4!==i.tag){if(22===i.tag&&null!==i.memoizedState)null!==(s=i.child)&&(s.return=i),rg(e,i,!0,!0);else if(null!==i.child){i.child.return=i,i=i.child;continue}}if(i===t)break;for(;null===i.sibling;){if(null===i.return||i.return===t)return;i=i.return}i.sibling.return=i.return,i=i.sibling}}function rx(e,t){if(iE&&ry(e,t)){var r=(e=t.stateNode).containerInfo,n=sl();!function e(t,r,n,i){if(iE)for(var s=r.child;null!==s;){if(5===s.tag){var a=s.stateNode;n&&i&&(a=sd(a,s.type,s.memoizedProps)),su(t,a)}else if(6===s.tag)a=s.stateNode,n&&i&&(a=sf(a,s.memoizedProps)),su(t,a);else if(4!==s.tag){if(22===s.tag&&null!==s.memoizedState)null!==(a=s.child)&&(a.return=s),e(t,s,null===s.memoizedProps||"manual"!==s.memoizedProps.mode,!0);else if(null!==s.child){s.child.return=s,s=s.child;continue}}if(s===r)break;for(;null===s.sibling;){if(null===s.return||s.return===r)return;s=s.return}s.sibling.return=s.return,s=s.sibling}}(n,t,!1,!1),e.pendingChildren=n,rm(t),sh(r,n)}}function rb(e,t,r,n){if(iP)e.memoizedProps!==n&&rm(t);else if(iE){var i=e.stateNode,s=e.memoizedProps;if((e=ry(e,t))||s!==n){var a=ak.current;(s=so(i,r,s,n,!e,null))===i?t.stateNode=i:(iw(s,r,n,a)&&rm(t),t.stateNode=s,e?rg(s,t,!1,!1):rm(t))}else t.stateNode=i}}function rv(e,t,r){if(iF(t,r)){if(e.flags|=0x1000000,!iV(t,r))if(nh())e.flags|=8192;else throw a1=a0,aK}else e.flags&=-0x1000001}function rw(e,t){if(sX(t)){if(e.flags|=0x1000000,!sG(t))if(nh())e.flags|=8192;else throw a1=a0,aK}else e.flags&=-0x1000001}function rS(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?v():0x20000000,e.lanes|=t,le|=t)}function rM(e,t){if(!aT)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;null!==t;)null!==t.alternate&&(r=t),t=t.sibling;null===r?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;null!==r;)null!==r.alternate&&(n=r),r=r.sibling;null===n?t||null===e.tail?e.tail=null:e.tail.sibling=null:n.sibling=null}}function r_(e){var t=null!==e.alternate&&e.alternate.child===e.child,r=0,n=0;if(t)for(var i=e.child;null!==i;)r|=i.lanes|i.childLanes,n|=0x1e00000&i.subtreeFlags,n|=0x1e00000&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)r|=i.lanes|i.childLanes,n|=i.subtreeFlags,n|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function rz(e,t){switch(N(t),t.tag){case 3:rt(oP),j();break;case 26:case 27:case 5:R(t);break;case 4:j();break;case 13:eP(t);break;case 19:p(oe);break;case 10:rt(t.type);break;case 22:case 23:eP(t),e_(),null!==e&&p(oA);break;case 24:rt(oP)}}function rk(e,t){try{var r=t.updateQueue,n=null!==r?r.lastEffect:null;if(null!==n){var i=n.next;r=i;do{if((r.tag&e)===e){n=void 0;var s=r.create;r.inst.destroy=n=s()}r=r.next}while(r!==i)}}catch(e){n_(t,t.return,e)}}function rC(e,t,r){try{var n=t.updateQueue,i=null!==n?n.lastEffect:null;if(null!==i){var s=i.next;n=s;do{if((n.tag&e)===e){var a=n.inst,o=a.destroy;if(void 0!==o){a.destroy=void 0,i=t;try{o()}catch(e){n_(i,r,e)}}}n=n.next}while(n!==s)}}catch(e){n_(t,t.return,e)}}function rP(e){var t=e.updateQueue;if(null!==t){var r=e.stateNode;try{ec(t,r)}catch(t){n_(e,e.return,t)}}}function rE(e,t,r){r.props=tR(e.type,e.memoizedProps),r.state=e.memoizedState;try{r.componentWillUnmount()}catch(r){n_(e,t,r)}}function rA(e,t){try{var r=e.ref;if(null!==r){var n=e.stateNode;switch(e.tag){case 26:case 27:case 5:var i=ip(n);break;default:i=n}"function"==typeof r?e.refCleanup=r(i):r.current=i}}catch(r){n_(e,t,r)}}function rN(e,t){var r=e.ref,n=e.refCleanup;if(null!==r)if("function"==typeof n)try{n()}catch(r){n_(e,t,r)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof r)try{r(null)}catch(r){n_(e,t,r)}else r.current=null}function rT(e){var t=e.type,r=e.memoizedProps,n=e.stateNode;try{i5(n,t,r,e)}catch(t){n_(e,e.return,t)}}function rj(e,t,r){try{i6(e.stateNode,e.type,r,t,e)}catch(t){n_(e,e.return,t)}}function rO(e){return 5===e.tag||3===e.tag||!!sF&&26===e.tag||!!s$&&27===e.tag||4===e.tag}function rR(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||rO(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&(s$?27!==e.tag:1)&&18!==e.tag;){if(2&e.flags||null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function rI(e,t,r){var n=e.tag;if(5===n||6===n)e=e.stateNode,t?i8(r,e,t):i2(r,e);else if(!(4===n||s$&&27===n)&&null!==(e=e.child))for(rI(e,t,r),e=e.sibling;null!==e;)rI(e,t,r),e=e.sibling}function rL(e,t,r){e=e.containerInfo;try{sc(e,r)}catch(e){n_(t,t.return,e)}}function rF(e,t,r){var n=r.flags;switch(r.tag){case 0:case 11:case 15:rJ(e,r),4&n&&rk(5,r);break;case 1:if(rJ(e,r),4&n)if(e=r.stateNode,null===t)try{e.componentDidMount()}catch(e){n_(r,r.return,e)}else{var i=tR(r.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(e){n_(r,r.return,e)}}64&n&&rP(r),512&n&&rA(r,r.return);break;case 3:if(rJ(e,r),64&n&&null!==(n=r.updateQueue)){if(e=null,null!==r.child)switch(r.child.tag){case 27:case 5:e=ip(r.child.stateNode);break;case 1:e=r.child.stateNode}try{ec(n,e)}catch(e){n_(r,r.return,e)}}break;case 26:if(sF){rJ(e,r),512&n&&rA(r,r.return);break}case 27:case 5:rJ(e,r),null===t&&4&n&&rT(r),512&n&&rA(r,r.return);break;case 12:default:rJ(e,r);break;case 13:rJ(e,r),4&n&&rD(e,r);break;case 22:if(!(i=null!==r.memoizedState||oN)){t=null!==t&&null!==t.memoizedState||oT;var s=oN,a=oT;oN=i,(oT=t)&&!a?function e(t,r,n){for(n=n&&0!=(8772&r.subtreeFlags),r=r.child;null!==r;){var i=r.alternate,s=t,a=r,o=a.flags;switch(a.tag){case 0:case 11:case 15:e(s,a,n),rk(4,a);break;case 1:if(e(s,a,n),"function"==typeof(s=(i=a).stateNode).componentDidMount)try{s.componentDidMount()}catch(e){n_(i,i.return,e)}if(null!==(s=(i=a).updateQueue)){var l=i.stateNode;try{var u=s.shared.hiddenCallbacks;if(null!==u)for(s.shared.hiddenCallbacks=null,s=0;s<u.length;s++)eh(u[s],l)}catch(e){n_(i,i.return,e)}}n&&64&o&&rP(a),rA(a,a.return);break;case 26:case 27:case 5:e(s,a,n),n&&null===i&&4&o&&rT(a),rA(a,a.return);break;case 12:default:e(s,a,n);break;case 13:e(s,a,n),n&&4&o&&rD(s,a);break;case 22:null===a.memoizedState&&e(s,a,n),rA(a,a.return)}r=r.sibling}}(e,r,0!=(8772&r.subtreeFlags)):rJ(e,r),oN=s,oT=a}512&n&&("manual"===r.memoizedProps.mode?rA(r,r.return):rN(r,r.return))}}function rV(e,t,r){for(r=r.child;null!==r;)rB(e,t,r),r=r.sibling}function rB(e,t,r){if(ap&&"function"==typeof ap.onCommitFiberUnmount)try{ap.onCommitFiberUnmount(af,r)}catch(e){}switch(r.tag){case 26:if(sF){oT||rN(r,t),rV(e,t,r),r.memoizedState?sW(r.memoizedState):r.stateNode&&sJ(r.stateNode);break}case 27:if(s$){oT||rN(r,t);var n=oL,i=oF;oL=r.stateNode,rV(e,t,r),s2(r.stateNode),oL=n,oF=i;break}case 5:oT||rN(r,t);case 6:if(iP){if(n=oL,i=oF,oL=null,rV(e,t,r),oL=n,oF=i,null!==oL)if(oF)try{se(oL,r.stateNode)}catch(e){n_(r,t,e)}else try{i9(oL,r.stateNode)}catch(e){n_(r,t,e)}}else rV(e,t,r);break;case 18:iP&&null!==oL&&(oF?sO(oL,r.stateNode):sj(oL,r.stateNode));break;case 4:iP?(n=oL,i=oF,oL=r.stateNode.containerInfo,oF=!0,rV(e,t,r),oL=n,oF=i):(iE&&rL(r.stateNode,r,sl()),rV(e,t,r));break;case 0:case 11:case 14:case 15:oT||rC(2,r,t),oT||rC(4,r,t),rV(e,t,r);break;case 1:oT||(rN(r,t),"function"==typeof(n=r.stateNode).componentWillUnmount&&rE(r,t,n)),rV(e,t,r);break;case 21:default:rV(e,t,r);break;case 22:oT||rN(r,t),oT=(n=oT)||null!==r.memoizedState,rV(e,t,r),oT=n}}function rD(e,t){if(iA&&null===t.memoizedState&&null!==(e=t.alternate)&&null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))try{sT(e)}catch(e){n_(t,t.return,e)}}function rU(e,t){var r=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new oO),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new oO),t;default:throw Error(i(435,e.tag))}}(e);t.forEach(function(t){var n=nE.bind(null,e,t);r.has(t)||(r.add(t),t.then(n,n))})}function rW(e,t){var r=t.deletions;if(null!==r)for(var n=0;n<r.length;n++){var s=r[n],a=e,o=t;if(iP){var l=o;e:for(;null!==l;){switch(l.tag){case 27:case 5:oL=l.stateNode,oF=!1;break e;case 3:case 4:oL=l.stateNode.containerInfo,oF=!0;break e}l=l.return}if(null===oL)throw Error(i(160));rB(a,o,s),oL=null,oF=!1}else rB(a,o,s);null!==(a=s.alternate)&&(a.return=null),s.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)rH(t,e),t=t.sibling}function rH(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:rW(t,e),rq(e),4&n&&(rC(3,e,e.return),rk(3,e),rC(5,e,e.return));break;case 1:rW(t,e),rq(e),512&n&&(oT||null===r||rN(r,r.return)),64&n&&oN&&null!==(e=e.updateQueue)&&null!==(n=e.callbacks)&&(r=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===r?n:r.concat(n));break;case 26:if(sF){var s=oV;rW(t,e),rq(e),512&n&&(oT||null===r||rN(r,r.return)),4&n&&(n=null!==r?r.memoizedState:null,t=e.memoizedState,null===r?null===t?null===e.stateNode?e.stateNode=sH(s,e.type,e.memoizedProps,e):sq(s,e.type,e.stateNode):e.stateNode=sU(s,t,e.memoizedProps):n!==t?(null===n?null!==r.stateNode&&sJ(r.stateNode):sW(n),null===t?sq(s,e.type,e.stateNode):sU(s,t,e.memoizedProps)):null===t&&null!==e.stateNode&&rj(e,e.memoizedProps,r.memoizedProps));break}case 27:if(s$&&4&n&&null===e.alternate){s=e.stateNode;var a=e.memoizedProps;try{s0(s),s1(e.type,a,s,e)}catch(t){n_(e,e.return,t)}}case 5:if(rW(t,e),rq(e),512&n&&(oT||null===r||rN(r,r.return)),iP){if(32&e.flags){t=e.stateNode;try{st(t)}catch(t){n_(e,e.return,t)}}4&n&&null!=e.stateNode&&(t=e.memoizedProps,rj(e,t,null!==r?r.memoizedProps:t)),1024&n&&(oj=!0)}break;case 6:if(rW(t,e),rq(e),4&n&&iP){if(null===e.stateNode)throw Error(i(162));n=e.memoizedProps,r=null!==r?r.memoizedProps:n,t=e.stateNode;try{i4(t,r,n)}catch(t){n_(e,e.return,t)}}break;case 3:if(sF?(sY(),s=oV,oV=sB(t.containerInfo),rW(t,e),oV=s):rW(t,e),rq(e),4&n){if(iP&&iA&&null!==r&&r.memoizedState.isDehydrated)try{sN(t.containerInfo)}catch(t){n_(e,e.return,t)}if(iE){n=t.containerInfo,r=t.pendingChildren;try{sc(n,r)}catch(t){n_(e,e.return,t)}}}oj&&(oj=!1,function e(t){if(1024&t.subtreeFlags)for(t=t.child;null!==t;){var r=t;e(r),5===r.tag&&1024&r.flags&&iq(r.stateNode),t=t.sibling}}(e));break;case 4:sF?(r=oV,oV=sB(e.stateNode.containerInfo),rW(t,e),rq(e),oV=r):(rW(t,e),rq(e)),4&n&&iE&&rL(e.stateNode,e,e.stateNode.pendingChildren);break;case 12:default:rW(t,e),rq(e);break;case 13:rW(t,e),rq(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==r&&null!==r.memoizedState)&&(li=aa()),4&n&&null!==(n=e.updateQueue)&&(e.updateQueue=null,rU(e,n));break;case 22:512&n&&(oT||null===r||rN(r,r.return)),s=null!==e.memoizedState;var o=null!==r&&null!==r.memoizedState,l=oN,u=oT;if(oN=l||s,oT=u||o,rW(t,e),oT=u,oN=l,rq(e),(t=e.stateNode)._current=e,t._visibility&=-3,t._visibility|=2&t._pendingVisibility,8192&n&&(t._visibility=s?-2&t._visibility:1|t._visibility,s&&(t=oN||oT,null===r||o||t||function e(t){for(t=t.child;null!==t;){var r=t;switch(r.tag){case 0:case 11:case 14:case 15:rC(4,r,r.return),e(r);break;case 1:rN(r,r.return);var n=r.stateNode;"function"==typeof n.componentWillUnmount&&rE(r,r.return,n),e(r);break;case 26:case 27:case 5:rN(r,r.return),e(r);break;case 22:rN(r,r.return),null===r.memoizedState&&e(r);break;default:e(r)}t=t.sibling}}(e)),iP&&(null===e.memoizedProps||"manual"!==e.memoizedProps.mode))){e:if(r=null,iP)for(t=e;;){if(5===t.tag||sF&&26===t.tag||s$&&27===t.tag){if(null===r){o=r=t;try{a=o.stateNode,s?sr(a):si(o.stateNode,o.memoizedProps)}catch(e){n_(o,o.return,e)}}}else if(6===t.tag){if(null===r){o=t;try{var h=o.stateNode;s?sn(h):ss(h,o.memoizedProps)}catch(e){n_(o,o.return,e)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;r===t&&(r=null),t=t.return}r===t&&(r=null),t.sibling.return=t.return,t=t.sibling}}4&n&&null!==(n=e.updateQueue)&&null!==(r=n.retryQueue)&&(n.retryQueue=null,rU(e,r));break;case 19:rW(t,e),rq(e),4&n&&null!==(n=e.updateQueue)&&(e.updateQueue=null,rU(e,n));case 21:}}function rq(e){var t=e.flags;if(2&t){try{if(iP&&(!s$||27!==e.tag)){e:{for(var r=e.return;null!==r;){if(rO(r)){var n=r;break e}r=r.return}throw Error(i(160))}switch(n.tag){case 27:if(s$){var s=n.stateNode,a=rR(e);rI(e,a,s);break}case 5:var o=n.stateNode;32&n.flags&&(st(o),n.flags&=-33);var l=rR(e);rI(e,l,o);break;case 3:case 4:var u=n.stateNode.containerInfo,h=rR(e);!function e(t,r,n){var i=t.tag;if(5===i||6===i)t=t.stateNode,r?i7(n,t,r):i3(n,t);else if(!(4===i||s$&&27===i)&&null!==(t=t.child))for(e(t,r,n),t=t.sibling;null!==t;)e(t,r,n),t=t.sibling}(e,h,u);break;default:throw Error(i(161))}}}catch(t){n_(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function rJ(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)rF(e,t.alternate,t),t=t.sibling}function rZ(e,t){var r=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(r=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==r&&(null!=e&&e.refCount++,null!=r&&rc(r))}function rY(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&rc(e))}function rX(e,t,r,n){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)rG(e,t,r,n),t=t.sibling}function rG(e,t,r,n){var i=t.flags;switch(t.tag){case 0:case 11:case 15:rX(e,t,r,n),2048&i&&rk(9,t);break;case 3:rX(e,t,r,n),2048&i&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&rc(e)));break;case 12:if(2048&i){rX(e,t,r,n),e=t.stateNode;try{var s=t.memoizedProps,a=s.id,o=s.onPostCommit;"function"==typeof o&&o(a,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(e){n_(t,t.return,e)}}else rX(e,t,r,n);break;case 23:break;case 22:s=t.stateNode,null!==t.memoizedState?4&s._visibility?rX(e,t,r,n):rQ(e,t):4&s._visibility?rX(e,t,r,n):(s._visibility|=4,function e(t,r,n,i,s){for(s=s&&0!=(10256&r.subtreeFlags),r=r.child;null!==r;){var a=r,o=a.flags;switch(a.tag){case 0:case 11:case 15:e(t,a,n,i,s),rk(8,a);break;case 23:break;case 22:var l=a.stateNode;null!==a.memoizedState?4&l._visibility?e(t,a,n,i,s):rQ(t,a):(l._visibility|=4,e(t,a,n,i,s)),s&&2048&o&&rZ(a.alternate,a);break;case 24:e(t,a,n,i,s),s&&2048&o&&rY(a.alternate,a);break;default:e(t,a,n,i,s)}r=r.sibling}}(e,t,r,n,0!=(10256&t.subtreeFlags))),2048&i&&rZ(t.alternate,t);break;case 24:rX(e,t,r,n),2048&i&&rY(t.alternate,t);break;default:rX(e,t,r,n)}}function rQ(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var r=t,n=r.flags;switch(r.tag){case 22:rQ(e,r),2048&n&&rZ(r.alternate,r);break;case 24:rQ(e,r),2048&n&&rY(r.alternate,r);break;default:rQ(e,r)}t=t.sibling}}function r$(e){if(e.subtreeFlags&oB)for(e=e.child;null!==e;)rK(e),e=e.sibling}function rK(e){switch(e.tag){case 26:r$(e),e.flags&oB&&(null!==e.memoizedState?sQ(oV,e.memoizedState,e.memoizedProps):iD(e.type,e.memoizedProps));break;case 5:r$(e),e.flags&oB&&iD(e.type,e.memoizedProps);break;case 3:case 4:if(sF){var t=oV;oV=sB(e.stateNode.containerInfo),r$(e),oV=t}else r$(e);break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=oB,oB=0x1000000,r$(e),oB=t):r$(e));break;default:r$(e)}}function r0(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(null!==e)}}function r1(e){var t=e.deletions;if(0!=(16&e.flags)){if(null!==t)for(var r=0;r<t.length;r++){var n=t[r];oR=n,r3(n,e)}r0(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)r2(e),e=e.sibling}function r2(e){switch(e.tag){case 0:case 11:case 15:r1(e),2048&e.flags&&rC(9,e,e.return);break;case 3:case 12:default:r1(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&4&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-5,function e(t){var r=t.deletions;if(0!=(16&t.flags)){if(null!==r)for(var n=0;n<r.length;n++){var i=r[n];oR=i,r3(i,t)}r0(t)}for(t=t.child;null!==t;){switch((r=t).tag){case 0:case 11:case 15:rC(8,r,r.return),e(r);break;case 22:4&(n=r.stateNode)._visibility&&(n._visibility&=-5,e(r));break;default:e(r)}t=t.sibling}}(e)):r1(e)}}function r3(e,t){for(;null!==oR;){var r=oR;switch(r.tag){case 0:case 11:case 15:rC(8,r,t);break;case 23:case 22:if(null!==r.memoizedState&&null!==r.memoizedState.cachePool){var n=r.memoizedState.cachePool.pool;null!=n&&n.refCount++}break;case 24:rc(r.memoizedState.cache)}if(null!==(n=r.child))n.return=r,oR=n;else for(r=e;null!==oR;){var i=(n=oR).sibling,s=n.return;if(!function e(t){var r=t.alternate;null!==r&&(t.alternate=null,e(r)),t.child=null,t.deletions=null,t.sibling=null,5===t.tag&&null!==(r=t.stateNode)&&iL(r),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}(n),n===r){oR=null;break}if(null!==i){i.return=s,oR=i;break}oR=s}}}function r4(e){var t=iN(e);if(null!=t){if("string"!=typeof t.memoizedProps["data-testname"])throw Error(i(364));return t}if(null===(e=iX(e)))throw Error(i(362));return e.stateNode.current}function r5(e,t){var r=e.tag;switch(t.$$typeof){case oU:if(e.type===t.value)return!0;break;case oW:e:{for(r=0,t=t.value,e=[e,0];r<e.length;){var n=e[r++],s=n.tag,a=e[r++],o=t[a];if(5!==s&&26!==s&&27!==s||!i$(n)){for(;null!=o&&r5(n,o);)o=t[++a];if(a===t.length){t=!0;break e}for(n=n.child;null!==n;)e.push(n,a),n=n.sibling}}t=!1}return t;case oH:if((5===r||26===r||27===r)&&iK(e.stateNode,t.value))return!0;break;case oJ:if((5===r||6===r||26===r||27===r)&&null!==(e=iQ(e))&&0<=e.indexOf(t.value))return!0;break;case oq:if((5===r||26===r||27===r)&&"string"==typeof(e=e.memoizedProps["data-testname"])&&e.toLowerCase()===t.value.toLowerCase())return!0;break;default:throw Error(i(365))}return!1}function r6(e){switch(e.$$typeof){case oU:return"<"+(a(e.value)||"Unknown")+">";case oW:return":has("+(r6(e)||"")+")";case oH:return'[role="'+e.value+'"]';case oJ:return'"'+e.value+'"';case oq:return'[data-testname="'+e.value+'"]';default:throw Error(i(365))}}function r8(e,t){var r=[];e=[e,0];for(var n=0;n<e.length;){var i=e[n++],s=i.tag,a=e[n++],o=t[a];if(5!==s&&26!==s&&27!==s||!i$(i)){for(;null!=o&&r5(i,o);)o=t[++a];if(a===t.length)r.push(i);else for(i=i.child;null!==i;)e.push(i,a),i=i.sibling}}return r}function r7(e,t){if(!iY)throw Error(i(363));e=r8(e=r4(e),t),t=[],e=Array.from(e);for(var r=0;r<e.length;){var n=e[r++],s=n.tag;if(5===s||26===s||27===s)i$(n)||t.push(n.stateNode);else for(n=n.child;null!==n;)e.push(n),n=n.sibling}return t}function r9(){if(0!=(2&oX)&&0!==o$)return o$&-o$;if(null!==io.T){var e=aZ;return 0!==e?e:ee()}return iR()}function ne(){0===o9&&(o9=0==(0x20000000&o$)||aT?b():0x20000000);var e=a7.current;return null!==e&&(e.flags|=32),o9}function nt(e,t,r){(e===oG&&2===oK||null!==e.cancelPendingCommit)&&(nl(e,0),ns(e,o$,o9,!1)),S(e,r),(0==(2&oX)||e!==oG)&&(e===oG&&(0==(2&oX)&&(o8|=r),4===o5&&ns(e,o$,o9,!1)),Y(e))}function nr(e,t,r){if(0!=(6&oX))throw Error(i(327));for(var n=!r&&0==(60&t)&&0==(t&e.expiredLanes)||x(e,t),s=n?function(e,t){var r=oX;oX|=2;var n=nc(),s=nd();oG!==e||o$!==t?(la=null,ls=aa()+500,nl(e,t)):o2=x(e,t);e:for(;;)try{if(0!==oK&&null!==oQ){t=oQ;var a=o0;t:switch(oK){case 1:oK=0,o0=null,ng(e,t,a,1);break;case 2:if(ef(a)){oK=0,o0=null,ny(t);break}t=function(){2===oK&&oG===e&&(oK=7),Y(e)},a.then(t,t);break e;case 3:oK=7;break e;case 4:oK=5;break e;case 7:ef(a)?(oK=0,o0=null,ny(t)):(oK=0,o0=null,ng(e,t,a,7));break;case 5:var o=null;switch(oQ.tag){case 26:o=oQ.memoizedState;case 5:case 27:var l=oQ,u=l.type,h=l.pendingProps;if(o?sG(o):iV(u,h)){oK=0,o0=null;var c=l.sibling;if(null!==c)oQ=c;else{var d=l.return;null!==d?(oQ=d,nx(d)):oQ=null}break t}}oK=0,o0=null,ng(e,t,a,5);break;case 6:oK=0,o0=null,ng(e,t,a,6);break;case 8:no(),o5=6;break e;default:throw Error(i(462))}}for(;null!==oQ&&!ai();)nm(oQ);break}catch(t){nu(e,t)}return(o_=oM=null,io.H=n,io.A=s,oX=r,null!==oQ)?0:(oG=null,o$=0,U(),o5)}(e,t):np(e,t,!0),a=n;;){if(0===s)o2&&!n&&ns(e,t,0,!1);else if(6===s)ns(e,t,0,!o1);else{if(r=e.current.alternate,a&&!function(e){for(var t=e;;){var r=t.tag;if((0===r||11===r||15===r)&&16384&t.flags&&null!==(r=t.updateQueue)&&null!==(r=r.stores))for(var n=0;n<r.length;n++){var i=r[n],s=i.getSnapshot;i=i.value;try{if(!am(s(),i))return!1}catch(e){return!1}}if(r=t.child,16384&t.subtreeFlags&&null!==r)r.return=t,t=r;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(r)){s=np(e,t,!1),a=!1;continue}if(2===s){if(a=t,e.errorRecoveryDisabledLanes&a)var o=0;else o=0!=(o=-0x20000001&e.pendingLanes)?o:0x20000000&o?0x20000000:0;if(0!==o){t=o;e:{s=lt;var l=iA&&e.current.memoizedState.isDehydrated;if(l&&(nl(e,o).flags|=256),2!==(o=np(e,o,!1))){if(o3&&!l){e.errorRecoveryDisabledLanes|=a,o8|=a,s=4;break e}a=lr,lr=s,null!==a&&nn(a)}s=o}if(a=!1,2!==s)continue}}if(1===s){nl(e,0),ns(e,t,0,!0);break}e:{switch(n=e,s){case 0:case 1:throw Error(i(345));case 4:if((4194176&t)===t){ns(n,t,o9,!o1);break e}break;case 2:lr=null;break;case 3:case 5:break;default:throw Error(i(329))}if(n.finishedWork=r,n.finishedLanes=t,(0x3c00000&t)===t&&10<(a=li+300-aa())){if(ns(n,t,o9,!o1),0!==g(n,0))break e;n.timeoutHandle=i_(ni.bind(null,n,r,lr,la,ln,t,o9,o8,le,o1,2,-0,0),a);break e}ni(n,r,lr,la,ln,t,o9,o8,le,o1,0,-0,0)}}break}Y(e)}function nn(e){null===lr?lr=e:lr.push.apply(lr,e)}function ni(e,t,r,n,i,s,a,o,l,u,h,c,d){var f=t.subtreeFlags;if((8192&f||0x1002000==(0x1002000&f))&&(iB(),rK(t),null!==(t=iU()))){e.cancelPendingCommit=t(nv.bind(null,e,r,n,i,a,o,l,1,c,d)),ns(e,s,a,!u);return}nv(e,r,n,i,a,o,l,h,c,d)}function ns(e,t,r,n){t&=~o7,t&=~o8,e.suspendedLanes|=t,e.pingedLanes&=~t,n&&(e.warmLanes|=t),n=e.expirationTimes;for(var i=t;0<i;){var s=31-s8(i),a=1<<s;n[s]=-1,i&=~a}0!==r&&M(e,r,t)}function na(){return 0!=(6&oX)||(X(0,!1),!1)}function no(){if(null!==oQ){if(0===oK)var e=oQ.return;else e=oQ,o_=oM=null,eF(e),a2=null,a3=0,e=oQ;for(;null!==e;)rz(e.alternate,e),e=e.return;oQ=null}}function nl(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;r!==ik&&(e.timeoutHandle=ik,iz(r)),null!==(r=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,r()),no(),oG=e,oQ=r=nT(e.current,null),o$=t,oK=0,o0=null,o1=!1,o2=x(e,t),o3=!1,le=o9=o7=o8=o6=o5=0,lr=lt=null,ln=!1,0!=(8&t)&&(t|=32&t);var n=e.entangledLanes;if(0!==n)for(e=e.entanglements,n&=t;0<n;){var i=31-s8(n),s=1<<i;t|=e[i],n&=~s}return o4=t,U(),r}function nu(e,t){or=null,io.H=of,t===a$?(t=ey(),oK=3):t===aK?(t=ey(),oK=4):oK=t===ob?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,o0=t,null===oQ&&(o5=1,tI(e,C(t,e.current)))}function nh(){var e=a7.current;return null===e||((4194176&o$)===o$?null===a9:((0x3c00000&o$)===o$||0!=(0x20000000&o$))&&e===a9)}function nc(){var e=io.H;return io.H=of,null===e?of:e}function nd(){var e=io.A;return io.A=oD,e}function nf(){o5=4,o1||(4194176&o$)!==o$&&null!==a7.current||(o2=!0),0==(0x7ffffff&o6)&&0==(0x7ffffff&o8)||null===oG||ns(oG,o$,o9,!1)}function np(e,t,r){var n=oX;oX|=2;var i=nc(),s=nd();(oG!==e||o$!==t)&&(la=null,nl(e,t)),t=!1;var a=o5;e:for(;;)try{if(0!==oK&&null!==oQ){var o=oQ,l=o0;switch(oK){case 8:no(),a=6;break e;case 3:case 2:case 6:null===a7.current&&(t=!0);var u=oK;if(oK=0,o0=null,ng(e,o,l,u),r&&o2){a=0;break e}break;default:u=oK,oK=0,o0=null,ng(e,o,l,u)}}(function(){for(;null!==oQ;)nm(oQ)})(),a=o5;break}catch(t){nu(e,t)}return t&&e.shellSuspendCounter++,o_=oM=null,oX=n,io.H=i,io.A=s,null===oQ&&(oG=null,o$=0,U()),a}function nm(e){var t=t9(e.alternate,e,o4);e.memoizedProps=e.pendingProps,null===t?nx(e):oQ=t}function ny(e){var t=e,r=t.alternate;switch(t.tag){case 15:case 0:t=tX(r,t,t.pendingProps,t.type,void 0,o$);break;case 11:t=tX(r,t,t.pendingProps,t.type.render,t.ref,o$);break;case 5:eF(t);default:rz(r,t),t=t9(r,t=oQ=nj(t,o4),o4)}e.memoizedProps=e.pendingProps,null===t?nx(e):oQ=t}function ng(e,t,r,n){o_=oM=null,eF(t),a2=null,a3=0;var s=t.return;try{if(function(e,t,r,n,s){if(r.flags|=32768,null!==n&&"object"==typeof n&&"function"==typeof n.then){if(null!==(t=r.alternate)&&ri(t,r,s,!0),null!==(r=a7.current)){switch(r.tag){case 13:return null===a9?nf():null===r.alternate&&0===o5&&(o5=3),r.flags&=-257,r.flags|=65536,r.lanes=s,n===a0?r.flags|=16384:(null===(t=r.updateQueue)?r.updateQueue=new Set([n]):t.add(n),nz(e,n,s)),!1;case 22:return r.flags|=65536,n===a0?r.flags|=16384:(null===(t=r.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([n])},r.updateQueue=t):null===(r=t.retryQueue)?t.retryQueue=new Set([n]):r.add(n),nz(e,n,s)),!1}throw Error(i(435,r.tag))}return nz(e,n,s),nf(),!1}if(aT)return null!==(t=a7.current)?(0==(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=s,n!==aR&&D(C(e=Error(i(422),{cause:n}),r))):(n!==aR&&D(C(t=Error(i(423),{cause:n}),r)),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,n=C(n,r),s=tF(e.stateNode,n,s),eo(e,s),4!==o5&&(o5=2)),!1;var a=Error(i(520),{cause:n});if(a=C(a,r),null===lt?lt=[a]:lt.push(a),4!==o5&&(o5=2),null===t)return!0;n=C(n,r),r=t;do{switch(r.tag){case 3:return r.flags|=65536,e=s&-s,r.lanes|=e,e=tF(r.stateNode,n,e),eo(r,e),!1;case 1:if(t=r.type,a=r.stateNode,0==(128&r.flags)&&("function"==typeof t.getDerivedStateFromError||null!==a&&"function"==typeof a.componentDidCatch&&(null===lo||!lo.has(a))))return r.flags|=65536,s&=-s,r.lanes|=s,tB(s=tV(s),e,r,n),eo(r,s),!1}r=r.return}while(null!==r);return!1}(e,s,t,r,o$)){o5=1,tI(e,C(r,e.current)),oQ=null;return}}catch(t){if(null!==s)throw oQ=s,t;o5=1,tI(e,C(r,e.current)),oQ=null;return}32768&t.flags?(aT||1===n?e=!0:o2||0!=(0x20000000&o$)?e=!1:(o1=e=!0,(2===n||3===n||6===n)&&null!==(n=a7.current)&&13===n.tag&&(n.flags|=16384)),nb(t,e)):nx(t)}function nx(e){var t=e;do{if(0!=(32768&t.flags))return void nb(t,o1);e=t.return;var r=function(e,t,r){var n=t.pendingProps;switch(N(t),t.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return r_(t),null;case 3:return r=t.stateNode,n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),rt(oP),j(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(null===e||null===e.child)&&(V(t)?rm(t):null===e||e.memoizedState.isDehydrated&&0==(256&t.flags)||(t.flags|=1024,null!==aj&&(nn(aj),aj=null))),rx(e,t),r_(t),null;case 26:if(sF){r=t.type;var s=t.memoizedState;return null===e?(rm(t),null!==s?(r_(t),rw(t,s)):(r_(t),rv(t,r,n))):s?s!==e.memoizedState?(rm(t),r_(t),rw(t,s)):(r_(t),t.flags&=-0x1000001):(iP?e.memoizedProps!==n&&rm(t):rb(e,t,r,n),r_(t),rv(t,r,n)),null}case 27:if(s$){if(R(t),r=aP.current,s=t.type,null!==e&&null!=t.stateNode)iP?e.memoizedProps!==n&&rm(t):rb(e,t,s,n);else{if(!n){if(null===t.stateNode)throw Error(i(166));return r_(t),null}e=ak.current,V(t)?L(t,e):(e=sK(s,n,r,e,!0),t.stateNode=e,rm(t))}return r_(t),null}case 5:if(R(t),r=t.type,null!==e&&null!=t.stateNode)rb(e,t,r,n);else{if(!n){if(null===t.stateNode)throw Error(i(166));return r_(t),null}e=ak.current,V(t)?L(t,e):(rg(s=ib(r,n,aP.current,e,t),t,!1,!1),t.stateNode=s,iw(s,r,n,e)&&rm(t))}return r_(t),rv(t,t.type,t.pendingProps),null;case 6:if(e&&null!=t.stateNode)r=e.memoizedProps,iP?r!==n&&rm(t):iE&&(r!==n?(t.stateNode=iM(n,aP.current,ak.current,t),rm(t)):t.stateNode=e.stateNode);else{if("string"!=typeof n&&null===t.stateNode)throw Error(i(166));if(e=aP.current,r=ak.current,V(t)){if(!iA)throw Error(i(176));if(e=t.stateNode,r=t.memoizedProps,n=null,null!==(s=aA))switch(s.tag){case 27:case 5:n=s.memoizedProps}sP(e,r,t,n)||I(t)}else t.stateNode=iM(n,e,r,t)}return r_(t),null;case 13:if(n=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(s=V(t),null!==n&&null!==n.dehydrated){if(null===e){if(!s)throw Error(i(318));if(!iA)throw Error(i(344));if(!(s=null!==(s=t.memoizedState)?s.dehydrated:null))throw Error(i(317));sE(s,t)}else B(),0==(128&t.flags)&&(t.memoizedState=null),t.flags|=4;r_(t),s=!1}else null!==aj&&(nn(aj),aj=null),s=!0;if(!s){if(256&t.flags)return eP(t),t;return eP(t),null}}if(eP(t),0!=(128&t.flags))return t.lanes=r,t;if(r=null!==n,e=null!==e&&null!==e.memoizedState,r){n=t.child,s=null,null!==n.alternate&&null!==n.alternate.memoizedState&&null!==n.alternate.memoizedState.cachePool&&(s=n.alternate.memoizedState.cachePool.pool);var a=null;null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(a=n.memoizedState.cachePool.pool),a!==s&&(n.flags|=2048)}return r!==e&&r&&(t.child.flags|=8192),rS(t,t.updateQueue),r_(t),null;case 4:return j(),rx(e,t),null===e&&iT(t.stateNode.containerInfo),r_(t),null;case 10:return rt(t.type),r_(t),null;case 19:if(p(oe),null===(s=t.memoizedState))return r_(t),null;if(n=0!=(128&t.flags),null===(a=s.rendering))if(n)rM(s,!1);else{if(0!==o5||null!==e&&0!=(128&e.flags))for(e=t.child;null!==e;){if(null!==(a=eE(e))){for(t.flags|=128,rM(s,!1),e=a.updateQueue,t.updateQueue=e,rS(t,e),t.subtreeFlags=0,e=r,r=t.child;null!==r;)nj(r,e),r=r.sibling;return m(oe,1&oe.current|2),t.child}e=e.sibling}null!==s.tail&&aa()>ls&&(t.flags|=128,n=!0,rM(s,!1),t.lanes=4194304)}else{if(!n)if(null!==(e=eE(a))){if(t.flags|=128,n=!0,e=e.updateQueue,t.updateQueue=e,rS(t,e),rM(s,!0),null===s.tail&&"hidden"===s.tailMode&&!a.alternate&&!aT)return r_(t),null}else 2*aa()-s.renderingStartTime>ls&&0x20000000!==r&&(t.flags|=128,n=!0,rM(s,!1),t.lanes=4194304);s.isBackwards?(a.sibling=t.child,t.child=a):(null!==(e=s.last)?e.sibling=a:t.child=a,s.last=a)}if(null!==s.tail)return t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=aa(),t.sibling=null,e=oe.current,m(oe,n?1&e|2:1&e),t;return r_(t),null;case 22:case 23:return eP(t),e_(),n=null!==t.memoizedState,null!==e?null!==e.memoizedState!==n&&(t.flags|=8192):n&&(t.flags|=8192),n?0!=(0x20000000&r)&&0==(128&t.flags)&&(r_(t),6&t.subtreeFlags&&(t.flags|=8192)):r_(t),null!==(r=t.updateQueue)&&rS(t,r.retryQueue),r=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(r=e.memoizedState.cachePool.pool),n=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(n=t.memoizedState.cachePool.pool),n!==r&&(t.flags|=2048),null!==e&&p(oA),null;case 24:return r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),rt(oP),r_(t),null;case 25:return null}throw Error(i(156,t.tag))}(t.alternate,t,o4);if(null!==r){oQ=r;return}if(null!==(t=t.sibling)){oQ=t;return}oQ=t=e}while(null!==t);0===o5&&(o5=5)}function nb(e,t){do{var r=function(e,t){switch(N(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return rt(oP),j(),0!=(65536&(e=t.flags))&&0==(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return R(t),null;case 13:if(eP(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));B()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return p(oe),null;case 4:return j(),null;case 10:return rt(t.type),null;case 22:case 23:return eP(t),e_(),null!==e&&p(oA),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return rt(oP),null;default:return null}}(e.alternate,e);if(null!==r){r.flags&=32767,oQ=r;return}if(null!==(r=e.return)&&(r.flags|=32768,r.subtreeFlags=0,r.deletions=null),!t&&null!==(e=e.sibling)){oQ=e;return}oQ=e=r}while(null!==e);o5=6,oQ=null}function nv(e,t,r,n,s,a,o,l,u,h){var c=io.T,d=iO();try{ij(2),io.T=null,function(e,t,r,n,s,a,o,l){do nS();while(null!==lu);if(0!=(6&oX))throw Error(i(327));var u,h,c=e.finishedWork;if(n=e.finishedLanes,null!==c){if(e.finishedWork=null,e.finishedLanes=0,c===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var d=c.lanes|c.childLanes;!function(e,t,r,n,i,s){var a=e.pendingLanes;e.pendingLanes=r,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=r,e.entangledLanes&=r,e.errorRecoveryDisabledLanes&=r,e.shellSuspendCounter=0;var o=e.entanglements,l=e.expirationTimes,u=e.hiddenUpdates;for(r=a&~r;0<r;){var h=31-s8(r),c=1<<h;o[h]=0,l[h]=-1;var d=u[h];if(null!==d)for(u[h]=null,h=0;h<d.length;h++){var f=d[h];null!==f&&(f.lane&=-0x20000001)}r&=~c}0!==n&&M(e,n,0),0!==s&&0===i&&0!==e.tag&&(e.suspendedLanes|=s&~(a&~t))}(e,n,d|=aF,a,o,l),e===oG&&(oQ=oG=null,o$=0),0==(10256&c.subtreeFlags)&&0==(10256&c.flags)||ll||(ll=!0,lc=d,ld=r,u=au,h=function(){return nS(!0),null},ar(u,h)),r=0!=(15990&c.flags),0!=(15990&c.subtreeFlags)||r?(r=io.T,io.T=null,a=iO(),ij(2),o=oX,oX|=4,function(e,t){for(ig(e.containerInfo),oR=t;null!==oR;)if(t=(e=oR).child,0!=(1028&e.subtreeFlags)&&null!==t)t.return=e,oR=t;else for(;null!==oR;){var r=(e=oR).alternate;switch(t=e.flags,e.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!=(1024&t)&&null!==r){t=void 0;var n=e,s=r.memoizedProps;r=r.memoizedState;var a=n.stateNode;try{var o=tR(n.type,s,n.elementType===n.type);t=a.getSnapshotBeforeUpdate(o,r),a.__reactInternalSnapshotBeforeUpdate=t}catch(e){n_(n,n.return,e)}}break;case 3:0!=(1024&t)&&iP&&sa(e.stateNode.containerInfo);break;default:if(0!=(1024&t))throw Error(i(163))}if(null!==(t=e.sibling)){t.return=e.return,oR=t;break}oR=e.return}o=oI,oI=!1}(e,c),rH(c,e),ix(e.containerInfo),e.current=c,rF(e,c.alternate,c),as(),oX=o,ij(a),io.T=r):e.current=c,ll?(ll=!1,lu=e,lh=n):nw(e,d),0===(d=e.pendingLanes)&&(lo=null);var f=c.stateNode;if(ap&&"function"==typeof ap.onCommitFiberRoot)try{ap.onCommitFiberRoot(af,f,void 0,128==(128&f.current.flags))}catch(e){}if(Y(e),null!==t)for(s=e.onRecoverableError,c=0;c<t.length;c++)s((d=t[c]).value,{componentStack:d.stack});0!=(3&lh)&&nS(),d=e.pendingLanes,0!=(4194218&n)&&0!=(42&d)?e===lp?lf++:(lf=0,lp=e):lf=0,X(0,!1)}}(e,t,r,n,d,s,a,o,l,u,h)}finally{io.T=c,ij(d)}}function nw(e,t){0==(e.pooledCacheLanes&=t)&&null!=(t=e.pooledCache)&&(e.pooledCache=null,rc(t))}function nS(){if(null!==lu){var e=lu,t=lc;lc=0;var r=z(lh),n=32>r?32:r;r=io.T;var s=iO();try{if(ij(n),io.T=null,null===lu)var a=!1;else{n=ld,ld=null;var o=lu,l=lh;if(lu=null,lh=0,0!=(6&oX))throw Error(i(331));var u=oX;if(oX|=4,r2(o.current),rG(o,o.current,l,n),oX=u,X(0,!1),ap&&"function"==typeof ap.onPostCommitFiberRoot)try{ap.onPostCommitFiberRoot(af,o)}catch(e){}a=!0}return a}finally{ij(s),io.T=r,nw(e,t)}}return!1}function nM(e,t,r){t=C(r,t),t=tF(e.stateNode,t,2),null!==(e=es(e,t,2))&&(S(e,2),Y(e))}function n_(e,t,r){if(3===e.tag)nM(e,e,r);else for(;null!==t;){if(3===t.tag){nM(t,e,r);break}if(1===t.tag){var n=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof n.componentDidCatch&&(null===lo||!lo.has(n))){e=C(r,e),null!==(n=es(t,r=tV(2),2))&&(tB(r,n,t,e),S(n,2),Y(n));break}}t=t.return}}function nz(e,t,r){var n=e.pingCache;if(null===n){n=e.pingCache=new oY;var i=new Set;n.set(t,i)}else void 0===(i=n.get(t))&&(i=new Set,n.set(t,i));i.has(r)||(o3=!0,i.add(r),e=nk.bind(null,e,t,r),t.then(e,e))}function nk(e,t,r){var n=e.pingCache;null!==n&&n.delete(t),e.pingedLanes|=e.suspendedLanes&r,e.warmLanes&=~r,oG===e&&(o$&r)===r&&(4===o5||3===o5&&(0x3c00000&o$)===o$&&300>aa()-li?0==(2&oX)&&nl(e,0):o7|=r,le===o$&&(le=0)),Y(e)}function nC(e,t){0===t&&(t=v()),null!==(e=q(e,t))&&(S(e,t),Y(e))}function nP(e){var t=e.memoizedState,r=0;null!==t&&(r=t.retryLane),nC(e,r)}function nE(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,s=e.memoizedState;null!==s&&(r=s.retryLane);break;case 19:n=e.stateNode;break;case 22:n=e.stateNode._retryCache;break;default:throw Error(i(314))}null!==n&&n.delete(t),nC(e,r)}function nA(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nN(e){return!(!(e=e.prototype)||!e.isReactComponent)}function nT(e,r){var n=e.alternate;return null===n?((n=t(e.tag,r,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=r,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=0x1e00000&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,r=e.dependencies,n.dependencies=null===r?null:{lanes:r.lanes,firstContext:r.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function nj(e,t){e.flags&=0x1e00002;var r=e.alternate;return null===r?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=r.childLanes,e.lanes=r.lanes,e.child=r.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=r.memoizedProps,e.memoizedState=r.memoizedState,e.updateQueue=r.updateQueue,e.type=r.type,e.dependencies=null===(t=r.dependencies)?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function nO(e,r,n,s,a,o){var l=0;if(s=e,"function"==typeof e)nN(e)&&(l=1);else if("string"==typeof e)l=sF&&s$?sV(e,n,ak.current)?26:s3(e)?27:5:sF?sV(e,n,ak.current)?26:5:s$&&s3(e)?27:5;else e:switch(e){case nK:return nR(n.children,a,o,r);case n0:l=8,a|=24;break;case n1:return(e=t(12,n,r,2|a)).elementType=n1,e.lanes=o,e;case n6:return(e=t(13,n,r,a)).elementType=n6,e.lanes=o,e;case n8:return(e=t(19,n,r,a)).elementType=n8,e.lanes=o,e;case ie:return nI(n,a,o,r);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case n2:case n4:l=10;break e;case n3:l=9;break e;case n5:l=11;break e;case n7:l=14;break e;case n9:l=16,s=null;break e}l=29,n=Error(i(130,null===e?"null":typeof e,"")),s=null}return(r=t(l,n,r,a)).elementType=e,r.type=s,r.lanes=o,r}function nR(e,r,n,i){return(e=t(7,e,i,r)).lanes=n,e}function nI(e,r,n,s){(e=t(22,e,s,r)).elementType=ie,e.lanes=n;var a={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var e=a._current;if(null===e)throw Error(i(456));if(0==(2&a._pendingVisibility)){var t=q(e,2);null!==t&&(a._pendingVisibility|=2,nt(t,e,2))}},attach:function(){var e=a._current;if(null===e)throw Error(i(456));if(0!=(2&a._pendingVisibility)){var t=q(e,2);null!==t&&(a._pendingVisibility&=-3,nt(t,e,2))}}};return e.stateNode=a,e}function nL(e,r,n){return(e=t(6,e,null,r)).lanes=n,e}function nF(e,r,n){return(r=t(4,null!==e.children?e.children:[],e.key,r)).lanes=n,r.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},r}function nV(e,t,r,n,i,s,a,o){this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=ik,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=w(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=w(0),this.hiddenUpdates=w(null),this.identifierPrefix=n,this.onUncaughtError=i,this.onCaughtError=s,this.onRecoverableError=a,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function nB(e,r,n,i,s,a,o,l,u,h,c,d){return e=new nV(e,r,n,o,l,u,h,d),r=1,!0===a&&(r|=24),a=t(3,null,null,r),e.current=a,a.stateNode=e,r=rh(),r.refCount++,e.pooledCache=r,r.refCount++,a.memoizedState={element:i,isDehydrated:n,cache:r},er(a),e}function nD(e){return e?e=s6:s6}function nU(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(i(188));throw Error(i(268,e=Object.keys(e).join(",")))}return null===(e=null!==(e=d(t))?function e(t){var r=t.tag;if(5===r||26===r||27===r||6===r)return t;for(t=t.child;null!==t;){if(null!==(r=e(t)))return r;t=t.sibling}return null}(e):null)?null:ip(e.stateNode)}function nW(e,t,r,n,i,s){var a;i=(a=i)?a=s6:s6,null===n.context?n.context=i:n.pendingContext=i,(n=ei(t)).payload={element:r},null!==(s=void 0===s?null:s)&&(n.callback=s),null!==(r=es(e,n,t))&&(nt(r,e,t),ea(r,e,t))}function nH(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var r=e.retryLane;e.retryLane=0!==r&&r<t?r:t}}function nq(e,t){nH(e,t),(e=e.alternate)&&nH(e,t)}var nJ={},nZ=r(43210),nY=r(58471),nX=Object.assign,nG=Symbol.for("react.element"),nQ=Symbol.for("react.transitional.element"),n$=Symbol.for("react.portal"),nK=Symbol.for("react.fragment"),n0=Symbol.for("react.strict_mode"),n1=Symbol.for("react.profiler"),n2=Symbol.for("react.provider"),n3=Symbol.for("react.consumer"),n4=Symbol.for("react.context"),n5=Symbol.for("react.forward_ref"),n6=Symbol.for("react.suspense"),n8=Symbol.for("react.suspense_list"),n7=Symbol.for("react.memo"),n9=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var ie=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var it,ir,ii=Symbol.for("react.memo_cache_sentinel"),is=Symbol.iterator,ia=Symbol.for("react.client.reference"),io=nZ.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,il=!1,iu=Array.isArray,ih=e.rendererVersion,ic=e.rendererPackageName,id=e.extraDevToolsConfig,ip=e.getPublicInstance,im=e.getRootHostContext,iy=e.getChildHostContext,ig=e.prepareForCommit,ix=e.resetAfterCommit,ib=e.createInstance,iv=e.appendInitialChild,iw=e.finalizeInitialChildren,iS=e.shouldSetTextContent,iM=e.createTextInstance,i_=e.scheduleTimeout,iz=e.cancelTimeout,ik=e.noTimeout,iC=e.isPrimaryRenderer;e.warnsIfNotActing;var iP=e.supportsMutation,iE=e.supportsPersistence,iA=e.supportsHydration,iN=e.getInstanceFromNode;e.beforeActiveInstanceBlur,e.afterActiveInstanceBlur;var iT=e.preparePortalMount;e.prepareScopeUpdate,e.getInstanceFromScope;var ij=e.setCurrentUpdatePriority,iO=e.getCurrentUpdatePriority,iR=e.resolveUpdatePriority;e.resolveEventType,e.resolveEventTimeStamp;var iI=e.shouldAttemptEagerTransition,iL=e.detachDeletedInstance;e.requestPostPaintCallback;var iF=e.maySuspendCommit,iV=e.preloadInstance,iB=e.startSuspendingCommit,iD=e.suspendInstance,iU=e.waitForCommitToBeReady,iW=e.NotPendingTransition,iH=e.HostTransitionContext,iq=e.resetFormInstance;e.bindToConsole;var iJ=e.supportsMicrotasks,iZ=e.scheduleMicrotask,iY=e.supportsTestSelectors,iX=e.findFiberRoot,iG=e.getBoundingRect,iQ=e.getTextContent,i$=e.isHiddenSubtree,iK=e.matchAccessibilityRole,i0=e.setFocusIfFocusable,i1=e.setupIntersectionObserver,i2=e.appendChild,i3=e.appendChildToContainer,i4=e.commitTextUpdate,i5=e.commitMount,i6=e.commitUpdate,i8=e.insertBefore,i7=e.insertInContainerBefore,i9=e.removeChild,se=e.removeChildFromContainer,st=e.resetTextContent,sr=e.hideInstance,sn=e.hideTextInstance,si=e.unhideInstance,ss=e.unhideTextInstance,sa=e.clearContainer,so=e.cloneInstance,sl=e.createContainerChildSet,su=e.appendChildToContainerChildSet,sh=e.finalizeContainerChildren,sc=e.replaceContainerChildren,sd=e.cloneHiddenInstance,sf=e.cloneHiddenTextInstance,sp=e.isSuspenseInstancePending,sm=e.isSuspenseInstanceFallback,sy=e.getSuspenseInstanceFallbackErrorDetails,sg=e.registerSuspenseInstanceRetry,sx=e.canHydrateFormStateMarker,sb=e.isFormStateMarkerMatching,sv=e.getNextHydratableSibling,sw=e.getFirstHydratableChild,sS=e.getFirstHydratableChildWithinContainer,sM=e.getFirstHydratableChildWithinSuspenseInstance,s_=e.canHydrateInstance,sz=e.canHydrateTextInstance,sk=e.canHydrateSuspenseInstance,sC=e.hydrateInstance,sP=e.hydrateTextInstance,sE=e.hydrateSuspenseInstance,sA=e.getNextHydratableInstanceAfterSuspenseInstance,sN=e.commitHydratedContainer,sT=e.commitHydratedSuspenseInstance,sj=e.clearSuspenseBoundary,sO=e.clearSuspenseBoundaryFromContainer,sR=e.shouldDeleteUnhydratedTailInstances;e.diffHydratedPropsForDevWarnings,e.diffHydratedTextForDevWarnings,e.describeHydratableInstanceForDevWarnings;var sI=e.validateHydratableInstance,sL=e.validateHydratableTextInstance,sF=e.supportsResources,sV=e.isHostHoistableType,sB=e.getHoistableRoot,sD=e.getResource,sU=e.acquireResource,sW=e.releaseResource,sH=e.hydrateHoistable,sq=e.mountHoistable,sJ=e.unmountHoistable,sZ=e.createHoistableInstance,sY=e.prepareToCommitHoistables,sX=e.mayResourceSuspendCommit,sG=e.preloadResource,sQ=e.suspendResource,s$=e.supportsSingletons,sK=e.resolveSingletonInstance,s0=e.clearSingleton,s1=e.acquireSingletonInstance,s2=e.releaseSingletonInstance,s3=e.isHostSingletonType,s4=[],s5=-1,s6={},s8=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(s7(e)/s9|0)|0},s7=Math.log,s9=Math.LN2,ae=128,at=4194304,ar=nY.unstable_scheduleCallback,an=nY.unstable_cancelCallback,ai=nY.unstable_shouldYield,as=nY.unstable_requestPaint,aa=nY.unstable_now,ao=nY.unstable_ImmediatePriority,al=nY.unstable_UserBlockingPriority,au=nY.unstable_NormalPriority,ah=nY.unstable_IdlePriority,ac=nY.log,ad=nY.unstable_setDisableYieldValue,af=null,ap=null,am="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},ay=new WeakMap,ag=[],ax=0,ab=null,av=0,aw=[],aS=0,aM=null,a_=1,az="",ak=f(null),aC=f(null),aP=f(null),aE=f(null),aA=null,aN=null,aT=!1,aj=null,aO=!1,aR=Error(i(519)),aI=[],aL=0,aF=0,aV=null,aB=null,aD=!1,aU=!1,aW=!1,aH=0,aq=null,aJ=0,aZ=0,aY=null,aX=!1,aG=!1,aQ=Object.prototype.hasOwnProperty,a$=Error(i(460)),aK=Error(i(474)),a0={then:function(){}},a1=null,a2=null,a3=0,a4=ew(!0),a5=ew(!1),a6=f(null),a8=f(0),a7=f(null),a9=null,oe=f(0),ot=0,or=null,on=null,oi=null,os=!1,oa=!1,oo=!1,ol=0,ou=0,oh=null,oc=0,od=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}},of={readContext:ro,use:eU,useCallback:eA,useContext:eA,useEffect:eA,useImperativeHandle:eA,useLayoutEffect:eA,useInsertionEffect:eA,useMemo:eA,useReducer:eA,useRef:eA,useState:eA,useDebugValue:eA,useDeferredValue:eA,useTransition:eA,useSyncExternalStore:eA,useId:eA};of.useCacheRefresh=eA,of.useMemoCache=eA,of.useHostTransitionStatus=eA,of.useFormState=eA,of.useActionState=eA,of.useOptimistic=eA;var op={readContext:ro,use:eU,useCallback:function(e,t){return eV().memoizedState=[e,void 0===t?null:t],e},useContext:ro,useEffect:tl,useImperativeHandle:function(e,t,r){r=null!=r?r.concat([e]):null,ta(4194308,4,td.bind(null,t,e),r)},useLayoutEffect:function(e,t){return ta(4194308,4,e,t)},useInsertionEffect:function(e,t){ta(4,2,e,t)},useMemo:function(e,t){var r=eV();t=void 0===t?null:t;var n=e();if(oo){k(!0);try{e()}finally{k(!1)}}return r.memoizedState=[n,t],n},useReducer:function(e,t,r){var n=eV();if(void 0!==r){var i=r(t);if(oo){k(!0);try{r(t)}finally{k(!1)}}}else i=t;return n.memoizedState=n.baseState=i,n.queue=e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},e=e.dispatch=tz.bind(null,or,e),[n.memoizedState,e]},useRef:function(e){return eV().memoizedState=e={current:e}},useState:function(e){var t=(e=e0(e)).queue,r=tk.bind(null,or,t);return t.dispatch=r,[e.memoizedState,r]},useDebugValue:tp,useDeferredValue:function(e,t){return tg(eV(),e,t)},useTransition:function(){var e=e0(!1);return e=tb.bind(null,or,e.queue,!0,!1),eV().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,r){var n=or,s=eV();if(aT){if(void 0===r)throw Error(i(407));r=r()}else{if(r=t(),null===oG)throw Error(i(349));0!=(60&o$)||eX(n,t,r)}s.memoizedState=r;var a={value:r,getSnapshot:t};return s.queue=a,tl(eQ.bind(null,n,a,e),[e]),n.flags|=2048,ti(9,eG.bind(null,n,a,r,t),{destroy:void 0},null),r},useId:function(){var e=eV(),t=oG.identifierPrefix;if(aT){var r=az,n=a_;t=":"+t+"R"+(r=(n&~(1<<32-s8(n)-1)).toString(32)+r),0<(r=ol++)&&(t+="H"+r.toString(32)),t+=":"}else t=":"+t+"r"+(r=oc++).toString(32)+":";return e.memoizedState=t},useCacheRefresh:function(){return eV().memoizedState=t_.bind(null,or)}};op.useMemoCache=eW,op.useHostTransitionStatus=tw,op.useFormState=e9,op.useActionState=e9,op.useOptimistic=function(e){var t=eV();t.memoizedState=t.baseState=e;var r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=r,t=tP.bind(null,or,!0,r),r.dispatch=t,[e,t]};var om={readContext:ro,use:eU,useCallback:tm,useContext:ro,useEffect:tu,useImperativeHandle:tf,useInsertionEffect:th,useLayoutEffect:tc,useMemo:ty,useReducer:eq,useRef:ts,useState:function(){return eq(eH)},useDebugValue:tp,useDeferredValue:function(e,t){return tx(eB(),on.memoizedState,e,t)},useTransition:function(){var e=eq(eH)[0],t=eB().memoizedState;return["boolean"==typeof e?e:eD(e),t]},useSyncExternalStore:eY,useId:tS};om.useCacheRefresh=tM,om.useMemoCache=eW,om.useHostTransitionStatus=tw,om.useFormState=te,om.useActionState=te,om.useOptimistic=function(e,t){return e1(eB(),on,e,t)};var oy={readContext:ro,use:eU,useCallback:tm,useContext:ro,useEffect:tu,useImperativeHandle:tf,useInsertionEffect:th,useLayoutEffect:tc,useMemo:ty,useReducer:eZ,useRef:ts,useState:function(){return eZ(eH)},useDebugValue:tp,useDeferredValue:function(e,t){var r=eB();return null===on?tg(r,e,t):tx(r,on.memoizedState,e,t)},useTransition:function(){var e=eZ(eH)[0],t=eB().memoizedState;return["boolean"==typeof e?e:eD(e),t]},useSyncExternalStore:eY,useId:tS};oy.useCacheRefresh=tM,oy.useMemoCache=eW,oy.useHostTransitionStatus=tw,oy.useFormState=tn,oy.useActionState=tn,oy.useOptimistic=function(e,t){var r=eB();return null!==on?e1(r,on,e,t):(r.baseState=e,[e,r.queue.dispatch])};var og={isMounted:function(e){return!!(e=e._reactInternals)&&h(e)===e},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=r9(),i=ei(n);i.payload=t,null!=r&&(i.callback=r),null!==(t=es(e,i,n))&&(nt(t,e,n),ea(t,e,n))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=r9(),i=ei(n);i.tag=1,i.payload=t,null!=r&&(i.callback=r),null!==(t=es(e,i,n))&&(nt(t,e,n),ea(t,e,n))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=r9(),n=ei(r);n.tag=2,null!=t&&(n.callback=t),null!==(t=es(e,n,r))&&(nt(t,e,r),ea(t,e,r))}},ox="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)},ob=Error(i(461)),ov=!1,ow={dehydrated:null,treeContext:null,retryLane:0},oS=f(null),oM=null,o_=null,oz="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,r){e.push(r)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},ok=nY.unstable_scheduleCallback,oC=nY.unstable_NormalPriority,oP={$$typeof:n4,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0},oE=io.S;io.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===aq){var r=aq=[];aJ=0,aZ=ee(),aY={status:"pending",value:void 0,then:function(e){r.push(e)}}}aJ++,t.then(et,et)}(0,t),null!==oE&&oE(e,t)};var oA=f(null),oN=!1,oT=!1,oj=!1,oO="function"==typeof WeakSet?WeakSet:Set,oR=null,oI=!1,oL=null,oF=!1,oV=null,oB=8192,oD={getCacheForType:function(e){var t=ro(oP),r=t.data.get(e);return void 0===r&&(r=e(),t.data.set(e,r)),r}},oU=0,oW=1,oH=2,oq=3,oJ=4;if("function"==typeof Symbol&&Symbol.for){var oZ=Symbol.for;oU=oZ("selector.component"),oW=oZ("selector.has_pseudo_class"),oH=oZ("selector.role"),oq=oZ("selector.test_id"),oJ=oZ("selector.text")}var oY="function"==typeof WeakMap?WeakMap:Map,oX=0,oG=null,oQ=null,o$=0,oK=0,o0=null,o1=!1,o2=!1,o3=!1,o4=0,o5=0,o6=0,o8=0,o7=0,o9=0,le=0,lt=null,lr=null,ln=!1,li=0,ls=1/0,la=null,lo=null,ll=!1,lu=null,lh=0,lc=0,ld=null,lf=0,lp=null;return nJ.attemptContinuousHydration=function(e){if(13===e.tag){var t=q(e,0x4000000);null!==t&&nt(t,e,0x4000000),nq(e,0x4000000)}},nJ.attemptHydrationAtCurrentPriority=function(e){if(13===e.tag){var t=r9(),r=q(e,t);null!==r&&nt(r,e,t),nq(e,t)}},nJ.attemptSynchronousHydration=function(e){switch(e.tag){case 3:if((e=e.stateNode).current.memoizedState.isDehydrated){var t=y(e.pendingLanes);if(0!==t){for(e.pendingLanes|=2,e.entangledLanes|=2;t;){var r=1<<31-s8(t);e.entanglements[1]|=r,t&=~r}Y(e),0==(6&oX)&&(ls=aa()+500,X(0,!1))}}break;case 13:null!==(t=q(e,2))&&nt(t,e,2),na(),nq(e,2)}},nJ.batchedUpdates=function(e,t){return e(t)},nJ.createComponentSelector=function(e){return{$$typeof:oU,value:e}},nJ.createContainer=function(e,t,r,n,i,s,a,o,l,u){return nB(e,t,!1,null,r,n,s,a,o,l,u,null)},nJ.createHasPseudoClassSelector=function(e){return{$$typeof:oW,value:e}},nJ.createHydrationContainer=function(e,t,r,n,i,s,a,o,l,u,h,c,d){var f;return(e=nB(r,n,!0,e,i,s,o,l,u,h,c,d)).context=(f=null,s6),r=e.current,(i=ei(n=r9())).callback=null!=t?t:null,es(r,i,n),e.current.lanes=n,S(e,n),Y(e),e},nJ.createPortal=function(e,t,r){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:n$,key:null==n?null:""+n,children:e,containerInfo:t,implementation:r}},nJ.createRoleSelector=function(e){return{$$typeof:oH,value:e}},nJ.createTestNameSelector=function(e){return{$$typeof:oq,value:e}},nJ.createTextSelector=function(e){return{$$typeof:oJ,value:e}},nJ.defaultOnCaughtError=function(e){console.error(e)},nJ.defaultOnRecoverableError=function(e){ox(e)},nJ.defaultOnUncaughtError=function(e){ox(e)},nJ.deferredUpdates=function(e){var t=io.T,r=iO();try{return ij(32),io.T=null,e()}finally{ij(r),io.T=t}},nJ.discreteUpdates=function(e,t,r,n,i){var s=io.T,a=iO();try{return ij(2),io.T=null,e(t,r,n,i)}finally{ij(a),io.T=s,0===oX&&(ls=aa()+500)}},nJ.findAllNodes=r7,nJ.findBoundingRects=function(e,t){if(!iY)throw Error(i(363));t=r7(e,t),e=[];for(var r=0;r<t.length;r++)e.push(iG(t[r]));for(t=e.length-1;0<t;t--){r=e[t];for(var n=r.x,s=n+r.width,a=r.y,o=a+r.height,l=t-1;0<=l;l--)if(t!==l){var u=e[l],h=u.x,c=h+u.width,d=u.y,f=d+u.height;if(n>=h&&a>=d&&s<=c&&o<=f){e.splice(t,1);break}if(n!==h||r.width!==u.width||f<a||d>o){if(!(a!==d||r.height!==u.height||c<n||h>s)){h>n&&(u.width+=h-n,u.x=n),c<s&&(u.width=s-h),e.splice(t,1);break}}else{d>a&&(u.height+=d-a,u.y=a),f<o&&(u.height=o-d),e.splice(t,1);break}}}return e},nJ.findHostInstance=nU,nJ.findHostInstanceWithNoPortals=function(e){return null===(e=null!==(e=d(e))?function e(t){var r=t.tag;if(5===r||26===r||27===r||6===r)return t;for(t=t.child;null!==t;){if(4!==t.tag&&null!==(r=e(t)))return r;t=t.sibling}return null}(e):null)?null:ip(e.stateNode)},nJ.findHostInstanceWithWarning=function(e){return nU(e)},nJ.flushPassiveEffects=nS,nJ.flushSyncFromReconciler=function(e){var t=oX;oX|=1;var r=io.T,n=iO();try{if(ij(2),io.T=null,e)return e()}finally{ij(n),io.T=r,0==(6&(oX=t))&&X(0,!1)}},nJ.flushSyncWork=na,nJ.focusWithin=function(e,t){if(!iY)throw Error(i(363));for(t=Array.from(t=r8(e=r4(e),t)),e=0;e<t.length;){var r=t[e++],n=r.tag;if(!i$(r)){if((5===n||26===n||27===n)&&i0(r.stateNode))return!0;for(r=r.child;null!==r;)t.push(r),r=r.sibling}}return!1},nJ.getFindAllNodesFailureDescription=function(e,t){if(!iY)throw Error(i(363));var r=0,n=[];e=[r4(e),0];for(var s=0;s<e.length;){var a=e[s++],o=a.tag,l=e[s++],u=t[l];if((5!==o&&26!==o&&27!==o||!i$(a))&&(r5(a,u)&&(n.push(r6(u)),++l>r&&(r=l)),l<t.length))for(a=a.child;null!==a;)e.push(a,l),a=a.sibling}if(r<t.length){for(e=[];r<t.length;r++)e.push(r6(t[r]));return"findAllNodes was able to match part of the selector:\n  "+n.join(" > ")+"\n\nNo matching component was found for:\n  "+e.join(" > ")}return null},nJ.getPublicRootInstance=function(e){if(!(e=e.current).child)return null;switch(e.child.tag){case 27:case 5:return ip(e.child.stateNode);default:return e.child.stateNode}},nJ.injectIntoDevTools=function(){var e={bundleType:0,version:ih,rendererPackageName:ic,currentDispatcherRef:io,findFiberByHostInstance:iN,reconcilerVersion:"19.0.0"};if(null!==id&&(e.rendererConfig=id),"undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)e=!1;else{var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled||!t.supportsFiber)e=!0;else{try{af=t.inject(e),ap=t}catch(e){}e=!!t.checkDCE}}return e},nJ.isAlreadyRendering=function(){return!1},nJ.observeVisibleRects=function(e,t,r,n){if(!iY)throw Error(i(363));var s=i1(e=r7(e,t),r,n).disconnect;return{disconnect:function(){s()}}},nJ.shouldError=function(){return null},nJ.shouldSuspend=function(){return!1},nJ.startHostTransition=function(e,t,r,s){if(5!==e.tag)throw Error(i(476));var a=tv(e).queue;tb(e,a,t,iW,null===r?n:function(){var t=tv(e).next.queue;return tC(e,t,{},r9()),r(s)})},nJ.updateContainer=function(e,t,r,n){var i=t.current,s=r9();return nW(i,s,e,t,r,n),s},nJ.updateContainerSync=function(e,t,r,n){return 0===t.tag&&nS(),nW(t.current,2,e,t,r,n),2},nJ},e.exports.default=e.exports,Object.defineProperty(e.exports,"__esModule",{value:!0})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},14959:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.AmpContext},17903:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.ImageConfigContext},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19587:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},24464:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P,generateMetadata:()=>C});var n=r(37413),i=r(82473),s=r(78963),a=r(30084),o=r(61120),l=r(75321),u=r(10974);let h=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(l.Root,{ref:r,className:(0,u.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));h.displayName=l.Root.displayName;let c=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(l.Image,{ref:r,className:(0,u.cn)("aspect-square h-full w-full",e),...t}));c.displayName=l.Image.displayName;let d=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(l.Fallback,{ref:r,className:(0,u.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));d.displayName=l.Fallback.displayName;var f=r(97680),p=r(53263),m=r(49046),y=r(1215),g=r(85838),x=r(80210),b=r(26373);let v=(0,b.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),w=(0,b.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var S=r(90230);let M=(0,b.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),_=(0,b.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var z=r(97576);let k={id:"1",title:"Luxury 3-Bedroom Apartment in Victoria Island",description:"Experience this stunning luxury apartment featuring modern amenities, breathtaking views of Lagos lagoon, and premium finishes throughout. Perfect for executives and families looking for upscale living in the heart of Lagos.",slug:"luxury-apartment-victoria-island",category:"Real Estate",location:"Victoria Island, Lagos",address:"123 Ahmadu Bello Way, Victoria Island, Lagos State",price:25e5,currency:"NGN",views:2847,likes:89,shares:12,createdAt:"2024-01-15",user:{id:"user1",name:"John Doe",avatar:"/avatars/john.jpg",company:"Premium Properties Lagos"},scenes:[{id:"scene1",title:"Living Room",imageUrl:"/sample-360/living-room.jpg",isStarting:!0},{id:"scene2",title:"Master Bedroom",imageUrl:"/sample-360/bedroom.jpg",isStarting:!1},{id:"scene3",title:"Kitchen",imageUrl:"/sample-360/kitchen.jpg",isStarting:!1}],features:["3 Bedrooms, 2 Bathrooms","Fully Furnished","24/7 Security","Swimming Pool","Gym Access","Parking Space","Generator Backup","High-Speed Internet"]};async function C({params:e}){return k?{title:k.title,description:k.description,openGraph:{title:k.title,description:k.description,type:"website",images:[{url:k.scenes[0]?.imageUrl||"/og-image.jpg",width:1200,height:630,alt:k.title}]}}:{title:"Tour Not Found"}}function P({params:e}){k&&k.slug===e.slug||(0,z.notFound)();let t=k.scenes.find(e=>e.isStarting)||k.scenes[0];return(0,n.jsx)(f.s,{children:(0,n.jsx)("div",{className:"py-8",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsx)(p.TourViewer,{imageUrl:"/sample-360.jpg",title:t.title,autoRotate:!1,showControls:!0})}),(0,n.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,n.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,n.jsxs)(s.Zp,{children:[(0,n.jsx)(s.aR,{children:(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(a.E,{variant:"secondary",className:"mb-2",children:k.category}),(0,n.jsx)(s.ZB,{className:"text-2xl",children:k.title}),(0,n.jsxs)(s.BT,{className:"flex items-center mt-2",children:[(0,n.jsx)(m.A,{className:"h-4 w-4 mr-1"}),k.location]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-primary",children:["₦",k.price.toLocaleString()]}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"per month"})]})]})}),(0,n.jsxs)(s.Wu,{children:[(0,n.jsx)("p",{className:"text-muted-foreground mb-4",children:k.description}),(0,n.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-muted-foreground",children:[(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)(y.A,{className:"h-4 w-4 mr-1"}),k.views.toLocaleString()," views"]}),(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)(g.A,{className:"h-4 w-4 mr-1"}),k.likes," likes"]}),(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)(x.A,{className:"h-4 w-4 mr-1"}),k.shares," shares"]}),(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)(v,{className:"h-4 w-4 mr-1"}),new Date(k.createdAt).toLocaleDateString()]})]})]})]}),(0,n.jsxs)(s.Zp,{children:[(0,n.jsx)(s.aR,{children:(0,n.jsx)(s.ZB,{children:"Features & Amenities"})}),(0,n.jsx)(s.Wu,{children:(0,n.jsx)("div",{className:"grid grid-cols-2 gap-2",children:k.features.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center text-sm",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-2"}),e]},t))})})]}),(0,n.jsxs)(s.Zp,{children:[(0,n.jsxs)(s.aR,{children:[(0,n.jsx)(s.ZB,{children:"Tour Scenes"}),(0,n.jsx)(s.BT,{children:"Explore different areas of this property"})]}),(0,n.jsx)(s.Wu,{children:(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:k.scenes.map(e=>(0,n.jsxs)("div",{className:`relative aspect-video bg-muted rounded-lg overflow-hidden cursor-pointer border-2 transition-colors ${e.isStarting?"border-primary":"border-transparent hover:border-primary/50"}`,children:[(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,n.jsx)(y.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,n.jsx)("div",{className:"absolute bottom-2 left-2 right-2",children:(0,n.jsx)("div",{className:"bg-black/50 backdrop-blur-sm rounded px-2 py-1",children:(0,n.jsx)("p",{className:"text-white text-sm font-medium",children:e.title})})})]},e.id))})})]})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)(s.Zp,{children:[(0,n.jsx)(s.aR,{children:(0,n.jsxs)(s.ZB,{className:"flex items-center",children:[(0,n.jsx)(w,{className:"h-5 w-5 mr-2"}),"Created by"]})}),(0,n.jsxs)(s.Wu,{children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,n.jsxs)(h,{children:[(0,n.jsx)(c,{src:k.user.avatar,alt:k.user.name}),(0,n.jsx)(d,{children:k.user.name.split(" ").map(e=>e[0]).join("")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium",children:k.user.name}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:k.user.company})]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)(i.$,{className:"w-full",children:[(0,n.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Contact via WhatsApp"]}),(0,n.jsxs)(i.$,{variant:"outline",className:"w-full",children:[(0,n.jsx)(M,{className:"h-4 w-4 mr-2"}),"View Profile"]})]})]})]}),(0,n.jsxs)(s.Zp,{children:[(0,n.jsx)(s.aR,{children:(0,n.jsx)(s.ZB,{children:"Actions"})}),(0,n.jsxs)(s.Wu,{className:"space-y-2",children:[(0,n.jsxs)(i.$,{variant:"outline",className:"w-full",children:[(0,n.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Save to Favorites"]}),(0,n.jsxs)(i.$,{variant:"outline",className:"w-full",children:[(0,n.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Share Tour"]}),(0,n.jsxs)(i.$,{variant:"outline",className:"w-full",children:[(0,n.jsx)(_,{className:"h-4 w-4 mr-2"}),"Download VR"]})]})]}),(0,n.jsxs)(s.Zp,{children:[(0,n.jsx)(s.aR,{children:(0,n.jsxs)(s.ZB,{className:"flex items-center",children:[(0,n.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Location"]})}),(0,n.jsxs)(s.Wu,{children:[(0,n.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:k.address}),(0,n.jsx)("div",{className:"aspect-video bg-muted rounded-lg flex items-center justify-center",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"Map View"})})]})]})]})]})]})})})}},27910:e=>{"use strict";e.exports=require("stream")},29261:(e,t)=>{"use strict";function r(e,t){var r=e.length;for(e.push(t);0<r;){var n=r-1>>>1,i=e[n];if(0<s(i,t))e[n]=t,e[r]=i,r=n;else break}}function n(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],r=e.pop();if(r!==t){e[0]=r;for(var n=0,i=e.length,a=i>>>1;n<a;){var o=2*(n+1)-1,l=e[o],u=o+1,h=e[u];if(0>s(l,r))u<i&&0>s(h,l)?(e[n]=h,e[u]=r,n=u):(e[n]=l,e[o]=r,n=o);else if(u<i&&0>s(h,r))e[n]=h,e[u]=r,n=u;else break}}return t}function s(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,o=performance;t.unstable_now=function(){return o.now()}}else{var l=Date,u=l.now();t.unstable_now=function(){return l.now()-u}}var h=[],c=[],d=1,f=null,p=3,m=!1,y=!1,g=!1,x="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function w(e){for(var t=n(c);null!==t;){if(null===t.callback)i(c);else if(t.startTime<=e)i(c),t.sortIndex=t.expirationTime,r(h,t);else break;t=n(c)}}function S(e){if(g=!1,w(e),!y)if(null!==n(h))y=!0,N();else{var t=n(c);null!==t&&T(S,t.startTime-e)}}var M=!1,_=-1,z=5,k=-1;function C(){return!(t.unstable_now()-k<z)}function P(){if(M){var e=t.unstable_now();k=e;var r=!0;try{e:{y=!1,g&&(g=!1,b(_),_=-1),m=!0;var s=p;try{t:{for(w(e),f=n(h);null!==f&&!(f.expirationTime>e&&C());){var o=f.callback;if("function"==typeof o){f.callback=null,p=f.priorityLevel;var l=o(f.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof l){f.callback=l,w(e),r=!0;break t}f===n(h)&&i(h),w(e)}else i(h);f=n(h)}if(null!==f)r=!0;else{var u=n(c);null!==u&&T(S,u.startTime-e),r=!1}}break e}finally{f=null,p=s,m=!1}}}finally{r?a():M=!1}}}if("function"==typeof v)a=function(){v(P)};else if("undefined"!=typeof MessageChannel){var E=new MessageChannel,A=E.port2;E.port1.onmessage=P,a=function(){A.postMessage(null)}}else a=function(){x(P,0)};function N(){M||(M=!0,a())}function T(e,r){_=x(function(){e(t.unstable_now())},r)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){y||m||(y=!0,N())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return n(h)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var r=p;p=t;try{return e()}finally{p=r}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=p;p=e;try{return t()}finally{p=r}},t.unstable_scheduleCallback=function(e,i,s){var a=t.unstable_now();switch(s="object"==typeof s&&null!==s&&"number"==typeof(s=s.delay)&&0<s?a+s:a,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=0x3fffffff;break;case 4:o=1e4;break;default:o=5e3}return o=s+o,e={id:d++,callback:i,priorityLevel:e,startTime:s,expirationTime:o,sortIndex:-1},s>a?(e.sortIndex=s,r(c,e),null===n(h)&&e===n(c)&&(g?(b(_),_=-1):g=!0,T(S,s-a))):(e.sortIndex=o,r(h,e),y||m||(y=!0,N())),e},t.unstable_shouldYield=C,t.unstable_wrapCallback=function(e){var t=p;return function(){var r=p;p=t;try{return e.apply(this,arguments)}finally{p=r}}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29562:(e,t,r)=>{"use strict";e.exports=r(7026)},30084:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var n=r(37413);r(61120);var i=r(50662),s=r(10974);let a=(0,i.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,s.cn)(a({variant:t}),e),...r})}},30512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},defaultHead:function(){return c}});let n=r(14985),i=r(40740),s=r(60687),a=i._(r(43210)),o=n._(r(47755)),l=r(14959),u=r(89513),h=r(34604);function c(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(50148);let f=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(d,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return i=>{let s=!0,a=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){a=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?s=!1:t.add(i.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(i.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?s=!1:r.add(t);else{let e=i.props[t],r=n[t]||new Set;("name"!==t||!a)&&r.has(e)?s=!1:(r.add(e),n[t]=r)}}}return s}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:n})})}let m=function(e){let{children:t}=e,r=(0,a.useContext)(l.AmpStateContext),n=(0,a.useContext)(u.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:p,headManager:n,inAmpMode:(0,h.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let n=r(14985),i=r(44953),s=r(46533),a=n._(r(1933));function o(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=s.Image},33873:e=>{"use strict";e.exports=require("path")},34604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},34631:e=>{"use strict";e.exports=require("tls")},36978:(e,t)=>{"use strict";t.ContinuousEventPriority=8,t.DefaultEventPriority=32,t.DiscreteEventPriority=2},41480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:s,objectFit:a}=e,o=n?40*n:t,l=i?40*i:r,u=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},43619:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>h,routeModule:()=>d,tree:()=>u});var n=r(65239),i=r(48088),s=r(88170),a=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u={children:["",{children:["tours",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,24464)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\tours\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,h=["C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\tours\\[slug]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/tours/[slug]/page",pathname:"/tours/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},44953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(50148);let n=r(41480),i=r(12756),s=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let u,h,c,{src:d,sizes:f,unoptimized:p=!1,priority:m=!1,loading:y,className:g,quality:x,width:b,height:v,fill:w=!1,style:S,overrideSrc:M,onLoad:_,onLoadingComplete:z,placeholder:k="empty",blurDataURL:C,fetchPriority:P,decoding:E="async",layout:A,objectFit:N,objectPosition:T,lazyBoundary:j,lazyRoot:O,...R}=e,{imgConf:I,showAltText:L,blurComplete:F,defaultLoader:V}=t,B=I||i.imageConfigDefault;if("allSizes"in B)u=B;else{let e=[...B.deviceSizes,...B.imageSizes].sort((e,t)=>e-t),t=B.deviceSizes.sort((e,t)=>e-t),n=null==(r=B.qualities)?void 0:r.sort((e,t)=>e-t);u={...B,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===V)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let D=R.loader||V;delete R.loader,delete R.srcSet;let U="__next_img_default"in D;if(U){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=D;D=t=>{let{config:r,...n}=t;return e(n)}}if(A){"fill"===A&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[A];e&&(S={...S,...e});let t={responsive:"100vw",fill:"100vw"}[A];t&&!f&&(f=t)}let W="",H=o(b),q=o(v);if((l=d)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(d)?d.default:d;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(h=e.blurWidth,c=e.blurHeight,C=C||e.blurDataURL,W=e.src,!w)if(H||q){if(H&&!q){let t=H/e.width;q=Math.round(e.height*t)}else if(!H&&q){let t=q/e.height;H=Math.round(e.width*t)}}else H=e.width,q=e.height}let J=!m&&("lazy"===y||void 0===y);(!(d="string"==typeof d?d:W)||d.startsWith("data:")||d.startsWith("blob:"))&&(p=!0,J=!1),u.unoptimized&&(p=!0),U&&!u.dangerouslyAllowSVG&&d.split("?",1)[0].endsWith(".svg")&&(p=!0);let Z=o(x),Y=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:N,objectPosition:T}:{},L?{}:{color:"transparent"},S),X=F||"empty"===k?null:"blur"===k?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:H,heightInt:q,blurWidth:h,blurHeight:c,blurDataURL:C||"",objectFit:Y.objectFit})+'")':'url("'+k+'")',G=s.includes(Y.objectFit)?"fill"===Y.objectFit?"100% 100%":"cover":Y.objectFit,Q=X?{backgroundSize:G,backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:s,sizes:a,loader:o}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,a),h=l.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:l.map((e,n)=>o({config:t,src:r,quality:s,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:o({config:t,src:r,quality:s,width:l[h]})}}({config:u,src:d,unoptimized:p,width:H,quality:Z,sizes:f,loader:D});return{props:{...R,loading:J?"lazy":y,fetchPriority:P,width:H,height:q,decoding:E,className:g,style:{...Y,...Q},sizes:$.sizes,srcSet:$.srcSet,src:M||$.src},meta:{unoptimized:p,priority:m,placeholder:k,fill:w}}}},46533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return v}});let n=r(14985),i=r(40740),s=r(60687),a=i._(r(43210)),o=n._(r(51215)),l=n._(r(30512)),u=r(44953),h=r(12756),c=r(17903);r(50148);let d=r(69148),f=n._(r(1933)),p=r(53038),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function y(e,t,r,n,i,s,a){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function g(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let x=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:i,height:o,width:l,decoding:u,className:h,style:c,fetchPriority:d,placeholder:f,loading:m,unoptimized:x,fill:b,onLoadRef:v,onLoadingCompleteRef:w,setBlurComplete:S,setShowAltText:M,sizesInput:_,onLoad:z,onError:k,...C}=e,P=(0,a.useCallback)(e=>{e&&(k&&(e.src=e.src),e.complete&&y(e,f,v,w,S,x,_))},[r,f,v,w,S,k,x,_]),E=(0,p.useMergedRef)(t,P);return(0,s.jsx)("img",{...C,...g(d),loading:m,width:l,height:o,decoding:u,"data-nimg":b?"fill":"1",className:h,style:c,sizes:i,srcSet:n,src:r,ref:E,onLoad:e=>{y(e.currentTarget,f,v,w,S,x,_)},onError:e=>{M(!0),"empty"!==f&&S(!0),k&&k(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...g(r.fetchPriority)};return t&&o.default.preload?(o.default.preload(r.src,n),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let v=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(d.RouterContext),n=(0,a.useContext)(c.ImageConfigContext),i=(0,a.useMemo)(()=>{var e;let t=m||n||h.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:i,qualities:s}},[n]),{onLoad:o,onLoadingComplete:l}=e,p=(0,a.useRef)(o);(0,a.useEffect)(()=>{p.current=o},[o]);let y=(0,a.useRef)(l);(0,a.useEffect)(()=>{y.current=l},[l]);let[g,v]=(0,a.useState)(!1),[w,S]=(0,a.useState)(!1),{props:M,meta:_}=(0,u.getImgProps)(e,{defaultLoader:f.default,imgConf:i,blurComplete:g,showAltText:w});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x,{...M,unoptimized:_.unoptimized,placeholder:_.placeholder,fill:_.fill,onLoadRef:p,onLoadingCompleteRef:y,setBlurComplete:v,setShowAltText:S,sizesInput:e.sizes,ref:t}),_.priority?(0,s.jsx)(b,{isAppRouter:!r,imgAttributes:M}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(43210),i=()=>{},s=()=>{};function a(e){var t;let{headManager:r,reduceComponentsToState:a}=e;function o(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(a(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),o(),i(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),i(()=>(r&&(r._pendingUpdate=o),()=>{r&&(r._pendingUpdate=o)})),s(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},48976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49587:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(14985)._(r(64963));function i(e,t){var r;let i={};"function"==typeof e&&(i.loader=e);let s={...i,...t};return(0,n.default)({...s,modules:null==(r=s.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53263:(e,t,r)=>{"use strict";r.d(t,{TourViewer:()=>i});var n=r(12907);let i=(0,n.registerClientReference)(function(){throw Error("Attempted to call TourViewer() from the server but TourViewer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\3d\\tour-viewer.tsx","TourViewer");(0,n.registerClientReference)(function(){throw Error("Attempted to call TourPreview() from the server but TourPreview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\3d\\tour-viewer.tsx","TourPreview")},53762:(e,t,r)=>{"use strict";e.exports=r(29261)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56780:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return i}});let n=r(81208);function i(e){let{reason:t,children:r}=e;throw Object.defineProperty(new n.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},58471:(e,t,r)=>{"use strict";e.exports=r(97374)},59721:(e,t,r)=>{Promise.resolve().then(r.bind(r,75321)),Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,53263)),Promise.resolve().then(r.bind(r,10590))},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64777:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return o}});let n=r(60687),i=r(51215),s=r(29294),a=r(19587);function o(e){let{moduleIds:t}=e,r=s.workAsyncStorage.getStore();if(void 0===r)return null;let o=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;o.push(...t)}}return 0===o.length?null:(0,n.jsx)(n.Fragment,{children:o.map(e=>{let t=r.assetPrefix+"/_next/"+(0,a.encodeURIPath)(e);return e.endsWith(".css")?(0,n.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,i.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(60687),i=r(43210),s=r(56780),a=r(64777);function o(e){return{default:e&&"default"in e?e.default:e}}let l={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},u=function(e){let t={...l,...e},r=(0,i.lazy)(()=>t.loader().then(o)),u=t.loading;function h(e){let o=u?(0,n.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,l=!t.ssr||!!t.loading,h=l?i.Suspense:i.Fragment,c=t.ssr?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.PreloadChunks,{moduleIds:t.modules}),(0,n.jsx)(r,{...e})]}):(0,n.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(h,{...l?{fallback:o}:{},children:c})}return h.displayName="LoadableComponent",h}},69148:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.RouterContext},70899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,s.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),i=r(52637),s=r(51846),a=r(31162),o=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74075:e=>{"use strict";e.exports=require("zlib")},75321:(e,t,r)=>{"use strict";r.d(t,{Fallback:()=>i,Image:()=>s,Root:()=>a});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","Avatar"),(0,n.registerClientReference)(function(){throw Error("Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","AvatarFallback"),(0,n.registerClientReference)(function(){throw Error("Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","AvatarImage");let i=(0,n.registerClientReference)(function(){throw Error("Attempted to call Fallback() from the server but Fallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","Fallback"),s=(0,n.registerClientReference)(function(){throw Error("Attempted to call Image() from the server but Image is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","Image"),a=(0,n.registerClientReference)(function(){throw Error("Attempted to call Root() from the server but Root is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","Root");(0,n.registerClientReference)(function(){throw Error("Attempted to call createAvatarScope() from the server but createAvatarScope is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","createAvatarScope")},78648:(e,t,r)=>{"use strict";let n,i,s,a,o,l;r.d(t,{TourViewer:()=>iq});var u=r(60687),h=r(43210);let c={ROTATE:0,DOLLY:1,PAN:2},d={ROTATE:0,PAN:1,DOLLY_PAN:2,DOLLY_ROTATE:3},f="srgb",p="srgb-linear",m="linear",y="srgb";class g{addEventListener(e,t){void 0===this._listeners&&(this._listeners={});let r=this._listeners;void 0===r[e]&&(r[e]=[]),-1===r[e].indexOf(t)&&r[e].push(t)}hasEventListener(e,t){let r=this._listeners;return void 0!==r&&void 0!==r[e]&&-1!==r[e].indexOf(t)}removeEventListener(e,t){let r=this._listeners;if(void 0===r)return;let n=r[e];if(void 0!==n){let e=n.indexOf(t);-1!==e&&n.splice(e,1)}}dispatchEvent(e){let t=this._listeners;if(void 0===t)return;let r=t[e.type];if(void 0!==r){e.target=this;let t=r.slice(0);for(let r=0,n=t.length;r<n;r++)t[r].call(this,e);e.target=null}}}let x=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"],b=Math.PI/180,v=180/Math.PI;function w(){let e=0xffffffff*Math.random()|0,t=0xffffffff*Math.random()|0,r=0xffffffff*Math.random()|0,n=0xffffffff*Math.random()|0;return(x[255&e]+x[e>>8&255]+x[e>>16&255]+x[e>>24&255]+"-"+x[255&t]+x[t>>8&255]+"-"+x[t>>16&15|64]+x[t>>24&255]+"-"+x[63&r|128]+x[r>>8&255]+"-"+x[r>>16&255]+x[r>>24&255]+x[255&n]+x[n>>8&255]+x[n>>16&255]+x[n>>24&255]).toLowerCase()}function S(e,t,r){return Math.max(t,Math.min(r,e))}function M(e,t){switch(t.constructor){case Float32Array:return e;case Uint32Array:return e/0xffffffff;case Uint16Array:return e/65535;case Uint8Array:return e/255;case Int32Array:return Math.max(e/0x7fffffff,-1);case Int16Array:return Math.max(e/32767,-1);case Int8Array:return Math.max(e/127,-1);default:throw Error("Invalid component type.")}}function _(e,t){switch(t.constructor){case Float32Array:return e;case Uint32Array:return Math.round(0xffffffff*e);case Uint16Array:return Math.round(65535*e);case Uint8Array:return Math.round(255*e);case Int32Array:return Math.round(0x7fffffff*e);case Int16Array:return Math.round(32767*e);case Int8Array:return Math.round(127*e);default:throw Error("Invalid component type.")}}class z{constructor(e=0,t=0){z.prototype.isVector2=!0,this.x=e,this.y=t}get width(){return this.x}set width(e){this.x=e}get height(){return this.y}set height(e){this.y=e}set(e,t){return this.x=e,this.y=t,this}setScalar(e){return this.x=e,this.y=e,this}setX(e){return this.x=e,this}setY(e){return this.y=e,this}setComponent(e,t){switch(e){case 0:this.x=t;break;case 1:this.y=t;break;default:throw Error("index is out of range: "+e)}return this}getComponent(e){switch(e){case 0:return this.x;case 1:return this.y;default:throw Error("index is out of range: "+e)}}clone(){return new this.constructor(this.x,this.y)}copy(e){return this.x=e.x,this.y=e.y,this}add(e){return this.x+=e.x,this.y+=e.y,this}addScalar(e){return this.x+=e,this.y+=e,this}addVectors(e,t){return this.x=e.x+t.x,this.y=e.y+t.y,this}addScaledVector(e,t){return this.x+=e.x*t,this.y+=e.y*t,this}sub(e){return this.x-=e.x,this.y-=e.y,this}subScalar(e){return this.x-=e,this.y-=e,this}subVectors(e,t){return this.x=e.x-t.x,this.y=e.y-t.y,this}multiply(e){return this.x*=e.x,this.y*=e.y,this}multiplyScalar(e){return this.x*=e,this.y*=e,this}divide(e){return this.x/=e.x,this.y/=e.y,this}divideScalar(e){return this.multiplyScalar(1/e)}applyMatrix3(e){let t=this.x,r=this.y,n=e.elements;return this.x=n[0]*t+n[3]*r+n[6],this.y=n[1]*t+n[4]*r+n[7],this}min(e){return this.x=Math.min(this.x,e.x),this.y=Math.min(this.y,e.y),this}max(e){return this.x=Math.max(this.x,e.x),this.y=Math.max(this.y,e.y),this}clamp(e,t){return this.x=S(this.x,e.x,t.x),this.y=S(this.y,e.y,t.y),this}clampScalar(e,t){return this.x=S(this.x,e,t),this.y=S(this.y,e,t),this}clampLength(e,t){let r=this.length();return this.divideScalar(r||1).multiplyScalar(S(r,e,t))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this}negate(){return this.x=-this.x,this.y=-this.y,this}dot(e){return this.x*e.x+this.y*e.y}cross(e){return this.x*e.y-this.y*e.x}lengthSq(){return this.x*this.x+this.y*this.y}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)}normalize(){return this.divideScalar(this.length()||1)}angle(){return Math.atan2(-this.y,-this.x)+Math.PI}angleTo(e){let t=Math.sqrt(this.lengthSq()*e.lengthSq());return 0===t?Math.PI/2:Math.acos(S(this.dot(e)/t,-1,1))}distanceTo(e){return Math.sqrt(this.distanceToSquared(e))}distanceToSquared(e){let t=this.x-e.x,r=this.y-e.y;return t*t+r*r}manhattanDistanceTo(e){return Math.abs(this.x-e.x)+Math.abs(this.y-e.y)}setLength(e){return this.normalize().multiplyScalar(e)}lerp(e,t){return this.x+=(e.x-this.x)*t,this.y+=(e.y-this.y)*t,this}lerpVectors(e,t,r){return this.x=e.x+(t.x-e.x)*r,this.y=e.y+(t.y-e.y)*r,this}equals(e){return e.x===this.x&&e.y===this.y}fromArray(e,t=0){return this.x=e[t],this.y=e[t+1],this}toArray(e=[],t=0){return e[t]=this.x,e[t+1]=this.y,e}fromBufferAttribute(e,t){return this.x=e.getX(t),this.y=e.getY(t),this}rotateAround(e,t){let r=Math.cos(t),n=Math.sin(t),i=this.x-e.x,s=this.y-e.y;return this.x=i*r-s*n+e.x,this.y=i*n+s*r+e.y,this}random(){return this.x=Math.random(),this.y=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y}}class k{constructor(e=0,t=0,r=0,n=1){this.isQuaternion=!0,this._x=e,this._y=t,this._z=r,this._w=n}static slerpFlat(e,t,r,n,i,s,a){let o=r[n+0],l=r[n+1],u=r[n+2],h=r[n+3],c=i[s+0],d=i[s+1],f=i[s+2],p=i[s+3];if(0===a){e[t+0]=o,e[t+1]=l,e[t+2]=u,e[t+3]=h;return}if(1===a){e[t+0]=c,e[t+1]=d,e[t+2]=f,e[t+3]=p;return}if(h!==p||o!==c||l!==d||u!==f){let e=1-a,t=o*c+l*d+u*f+h*p,r=t>=0?1:-1,n=1-t*t;if(n>Number.EPSILON){let i=Math.sqrt(n),s=Math.atan2(i,t*r);e=Math.sin(e*s)/i,a=Math.sin(a*s)/i}let i=a*r;if(o=o*e+c*i,l=l*e+d*i,u=u*e+f*i,h=h*e+p*i,e===1-a){let e=1/Math.sqrt(o*o+l*l+u*u+h*h);o*=e,l*=e,u*=e,h*=e}}e[t]=o,e[t+1]=l,e[t+2]=u,e[t+3]=h}static multiplyQuaternionsFlat(e,t,r,n,i,s){let a=r[n],o=r[n+1],l=r[n+2],u=r[n+3],h=i[s],c=i[s+1],d=i[s+2],f=i[s+3];return e[t]=a*f+u*h+o*d-l*c,e[t+1]=o*f+u*c+l*h-a*d,e[t+2]=l*f+u*d+a*c-o*h,e[t+3]=u*f-a*h-o*c-l*d,e}get x(){return this._x}set x(e){this._x=e,this._onChangeCallback()}get y(){return this._y}set y(e){this._y=e,this._onChangeCallback()}get z(){return this._z}set z(e){this._z=e,this._onChangeCallback()}get w(){return this._w}set w(e){this._w=e,this._onChangeCallback()}set(e,t,r,n){return this._x=e,this._y=t,this._z=r,this._w=n,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._w)}copy(e){return this._x=e.x,this._y=e.y,this._z=e.z,this._w=e.w,this._onChangeCallback(),this}setFromEuler(e,t=!0){let r=e._x,n=e._y,i=e._z,s=e._order,a=Math.cos,o=Math.sin,l=a(r/2),u=a(n/2),h=a(i/2),c=o(r/2),d=o(n/2),f=o(i/2);switch(s){case"XYZ":this._x=c*u*h+l*d*f,this._y=l*d*h-c*u*f,this._z=l*u*f+c*d*h,this._w=l*u*h-c*d*f;break;case"YXZ":this._x=c*u*h+l*d*f,this._y=l*d*h-c*u*f,this._z=l*u*f-c*d*h,this._w=l*u*h+c*d*f;break;case"ZXY":this._x=c*u*h-l*d*f,this._y=l*d*h+c*u*f,this._z=l*u*f+c*d*h,this._w=l*u*h-c*d*f;break;case"ZYX":this._x=c*u*h-l*d*f,this._y=l*d*h+c*u*f,this._z=l*u*f-c*d*h,this._w=l*u*h+c*d*f;break;case"YZX":this._x=c*u*h+l*d*f,this._y=l*d*h+c*u*f,this._z=l*u*f-c*d*h,this._w=l*u*h-c*d*f;break;case"XZY":this._x=c*u*h-l*d*f,this._y=l*d*h-c*u*f,this._z=l*u*f+c*d*h,this._w=l*u*h+c*d*f;break;default:console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: "+s)}return!0===t&&this._onChangeCallback(),this}setFromAxisAngle(e,t){let r=t/2,n=Math.sin(r);return this._x=e.x*n,this._y=e.y*n,this._z=e.z*n,this._w=Math.cos(r),this._onChangeCallback(),this}setFromRotationMatrix(e){let t=e.elements,r=t[0],n=t[4],i=t[8],s=t[1],a=t[5],o=t[9],l=t[2],u=t[6],h=t[10],c=r+a+h;if(c>0){let e=.5/Math.sqrt(c+1);this._w=.25/e,this._x=(u-o)*e,this._y=(i-l)*e,this._z=(s-n)*e}else if(r>a&&r>h){let e=2*Math.sqrt(1+r-a-h);this._w=(u-o)/e,this._x=.25*e,this._y=(n+s)/e,this._z=(i+l)/e}else if(a>h){let e=2*Math.sqrt(1+a-r-h);this._w=(i-l)/e,this._x=(n+s)/e,this._y=.25*e,this._z=(o+u)/e}else{let e=2*Math.sqrt(1+h-r-a);this._w=(s-n)/e,this._x=(i+l)/e,this._y=(o+u)/e,this._z=.25*e}return this._onChangeCallback(),this}setFromUnitVectors(e,t){let r=e.dot(t)+1;return r<Number.EPSILON?(r=0,Math.abs(e.x)>Math.abs(e.z)?(this._x=-e.y,this._y=e.x,this._z=0):(this._x=0,this._y=-e.z,this._z=e.y)):(this._x=e.y*t.z-e.z*t.y,this._y=e.z*t.x-e.x*t.z,this._z=e.x*t.y-e.y*t.x),this._w=r,this.normalize()}angleTo(e){return 2*Math.acos(Math.abs(S(this.dot(e),-1,1)))}rotateTowards(e,t){let r=this.angleTo(e);if(0===r)return this;let n=Math.min(1,t/r);return this.slerp(e,n),this}identity(){return this.set(0,0,0,1)}invert(){return this.conjugate()}conjugate(){return this._x*=-1,this._y*=-1,this._z*=-1,this._onChangeCallback(),this}dot(e){return this._x*e._x+this._y*e._y+this._z*e._z+this._w*e._w}lengthSq(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}length(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)}normalize(){let e=this.length();return 0===e?(this._x=0,this._y=0,this._z=0,this._w=1):(e=1/e,this._x=this._x*e,this._y=this._y*e,this._z=this._z*e,this._w=this._w*e),this._onChangeCallback(),this}multiply(e){return this.multiplyQuaternions(this,e)}premultiply(e){return this.multiplyQuaternions(e,this)}multiplyQuaternions(e,t){let r=e._x,n=e._y,i=e._z,s=e._w,a=t._x,o=t._y,l=t._z,u=t._w;return this._x=r*u+s*a+n*l-i*o,this._y=n*u+s*o+i*a-r*l,this._z=i*u+s*l+r*o-n*a,this._w=s*u-r*a-n*o-i*l,this._onChangeCallback(),this}slerp(e,t){if(0===t)return this;if(1===t)return this.copy(e);let r=this._x,n=this._y,i=this._z,s=this._w,a=s*e._w+r*e._x+n*e._y+i*e._z;if(a<0?(this._w=-e._w,this._x=-e._x,this._y=-e._y,this._z=-e._z,a=-a):this.copy(e),a>=1)return this._w=s,this._x=r,this._y=n,this._z=i,this;let o=1-a*a;if(o<=Number.EPSILON){let e=1-t;return this._w=e*s+t*this._w,this._x=e*r+t*this._x,this._y=e*n+t*this._y,this._z=e*i+t*this._z,this.normalize(),this}let l=Math.sqrt(o),u=Math.atan2(l,a),h=Math.sin((1-t)*u)/l,c=Math.sin(t*u)/l;return this._w=s*h+this._w*c,this._x=r*h+this._x*c,this._y=n*h+this._y*c,this._z=i*h+this._z*c,this._onChangeCallback(),this}slerpQuaternions(e,t,r){return this.copy(e).slerp(t,r)}random(){let e=2*Math.PI*Math.random(),t=2*Math.PI*Math.random(),r=Math.random(),n=Math.sqrt(1-r),i=Math.sqrt(r);return this.set(n*Math.sin(e),n*Math.cos(e),i*Math.sin(t),i*Math.cos(t))}equals(e){return e._x===this._x&&e._y===this._y&&e._z===this._z&&e._w===this._w}fromArray(e,t=0){return this._x=e[t],this._y=e[t+1],this._z=e[t+2],this._w=e[t+3],this._onChangeCallback(),this}toArray(e=[],t=0){return e[t]=this._x,e[t+1]=this._y,e[t+2]=this._z,e[t+3]=this._w,e}fromBufferAttribute(e,t){return this._x=e.getX(t),this._y=e.getY(t),this._z=e.getZ(t),this._w=e.getW(t),this._onChangeCallback(),this}toJSON(){return this.toArray()}_onChange(e){return this._onChangeCallback=e,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._w}}class C{constructor(e=0,t=0,r=0){C.prototype.isVector3=!0,this.x=e,this.y=t,this.z=r}set(e,t,r){return void 0===r&&(r=this.z),this.x=e,this.y=t,this.z=r,this}setScalar(e){return this.x=e,this.y=e,this.z=e,this}setX(e){return this.x=e,this}setY(e){return this.y=e,this}setZ(e){return this.z=e,this}setComponent(e,t){switch(e){case 0:this.x=t;break;case 1:this.y=t;break;case 2:this.z=t;break;default:throw Error("index is out of range: "+e)}return this}getComponent(e){switch(e){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw Error("index is out of range: "+e)}}clone(){return new this.constructor(this.x,this.y,this.z)}copy(e){return this.x=e.x,this.y=e.y,this.z=e.z,this}add(e){return this.x+=e.x,this.y+=e.y,this.z+=e.z,this}addScalar(e){return this.x+=e,this.y+=e,this.z+=e,this}addVectors(e,t){return this.x=e.x+t.x,this.y=e.y+t.y,this.z=e.z+t.z,this}addScaledVector(e,t){return this.x+=e.x*t,this.y+=e.y*t,this.z+=e.z*t,this}sub(e){return this.x-=e.x,this.y-=e.y,this.z-=e.z,this}subScalar(e){return this.x-=e,this.y-=e,this.z-=e,this}subVectors(e,t){return this.x=e.x-t.x,this.y=e.y-t.y,this.z=e.z-t.z,this}multiply(e){return this.x*=e.x,this.y*=e.y,this.z*=e.z,this}multiplyScalar(e){return this.x*=e,this.y*=e,this.z*=e,this}multiplyVectors(e,t){return this.x=e.x*t.x,this.y=e.y*t.y,this.z=e.z*t.z,this}applyEuler(e){return this.applyQuaternion(E.setFromEuler(e))}applyAxisAngle(e,t){return this.applyQuaternion(E.setFromAxisAngle(e,t))}applyMatrix3(e){let t=this.x,r=this.y,n=this.z,i=e.elements;return this.x=i[0]*t+i[3]*r+i[6]*n,this.y=i[1]*t+i[4]*r+i[7]*n,this.z=i[2]*t+i[5]*r+i[8]*n,this}applyNormalMatrix(e){return this.applyMatrix3(e).normalize()}applyMatrix4(e){let t=this.x,r=this.y,n=this.z,i=e.elements,s=1/(i[3]*t+i[7]*r+i[11]*n+i[15]);return this.x=(i[0]*t+i[4]*r+i[8]*n+i[12])*s,this.y=(i[1]*t+i[5]*r+i[9]*n+i[13])*s,this.z=(i[2]*t+i[6]*r+i[10]*n+i[14])*s,this}applyQuaternion(e){let t=this.x,r=this.y,n=this.z,i=e.x,s=e.y,a=e.z,o=e.w,l=2*(s*n-a*r),u=2*(a*t-i*n),h=2*(i*r-s*t);return this.x=t+o*l+s*h-a*u,this.y=r+o*u+a*l-i*h,this.z=n+o*h+i*u-s*l,this}project(e){return this.applyMatrix4(e.matrixWorldInverse).applyMatrix4(e.projectionMatrix)}unproject(e){return this.applyMatrix4(e.projectionMatrixInverse).applyMatrix4(e.matrixWorld)}transformDirection(e){let t=this.x,r=this.y,n=this.z,i=e.elements;return this.x=i[0]*t+i[4]*r+i[8]*n,this.y=i[1]*t+i[5]*r+i[9]*n,this.z=i[2]*t+i[6]*r+i[10]*n,this.normalize()}divide(e){return this.x/=e.x,this.y/=e.y,this.z/=e.z,this}divideScalar(e){return this.multiplyScalar(1/e)}min(e){return this.x=Math.min(this.x,e.x),this.y=Math.min(this.y,e.y),this.z=Math.min(this.z,e.z),this}max(e){return this.x=Math.max(this.x,e.x),this.y=Math.max(this.y,e.y),this.z=Math.max(this.z,e.z),this}clamp(e,t){return this.x=S(this.x,e.x,t.x),this.y=S(this.y,e.y,t.y),this.z=S(this.z,e.z,t.z),this}clampScalar(e,t){return this.x=S(this.x,e,t),this.y=S(this.y,e,t),this.z=S(this.z,e,t),this}clampLength(e,t){let r=this.length();return this.divideScalar(r||1).multiplyScalar(S(r,e,t))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this}dot(e){return this.x*e.x+this.y*e.y+this.z*e.z}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)}normalize(){return this.divideScalar(this.length()||1)}setLength(e){return this.normalize().multiplyScalar(e)}lerp(e,t){return this.x+=(e.x-this.x)*t,this.y+=(e.y-this.y)*t,this.z+=(e.z-this.z)*t,this}lerpVectors(e,t,r){return this.x=e.x+(t.x-e.x)*r,this.y=e.y+(t.y-e.y)*r,this.z=e.z+(t.z-e.z)*r,this}cross(e){return this.crossVectors(this,e)}crossVectors(e,t){let r=e.x,n=e.y,i=e.z,s=t.x,a=t.y,o=t.z;return this.x=n*o-i*a,this.y=i*s-r*o,this.z=r*a-n*s,this}projectOnVector(e){let t=e.lengthSq();if(0===t)return this.set(0,0,0);let r=e.dot(this)/t;return this.copy(e).multiplyScalar(r)}projectOnPlane(e){return P.copy(this).projectOnVector(e),this.sub(P)}reflect(e){return this.sub(P.copy(e).multiplyScalar(2*this.dot(e)))}angleTo(e){let t=Math.sqrt(this.lengthSq()*e.lengthSq());return 0===t?Math.PI/2:Math.acos(S(this.dot(e)/t,-1,1))}distanceTo(e){return Math.sqrt(this.distanceToSquared(e))}distanceToSquared(e){let t=this.x-e.x,r=this.y-e.y,n=this.z-e.z;return t*t+r*r+n*n}manhattanDistanceTo(e){return Math.abs(this.x-e.x)+Math.abs(this.y-e.y)+Math.abs(this.z-e.z)}setFromSpherical(e){return this.setFromSphericalCoords(e.radius,e.phi,e.theta)}setFromSphericalCoords(e,t,r){let n=Math.sin(t)*e;return this.x=n*Math.sin(r),this.y=Math.cos(t)*e,this.z=n*Math.cos(r),this}setFromCylindrical(e){return this.setFromCylindricalCoords(e.radius,e.theta,e.y)}setFromCylindricalCoords(e,t,r){return this.x=e*Math.sin(t),this.y=r,this.z=e*Math.cos(t),this}setFromMatrixPosition(e){let t=e.elements;return this.x=t[12],this.y=t[13],this.z=t[14],this}setFromMatrixScale(e){let t=this.setFromMatrixColumn(e,0).length(),r=this.setFromMatrixColumn(e,1).length(),n=this.setFromMatrixColumn(e,2).length();return this.x=t,this.y=r,this.z=n,this}setFromMatrixColumn(e,t){return this.fromArray(e.elements,4*t)}setFromMatrix3Column(e,t){return this.fromArray(e.elements,3*t)}setFromEuler(e){return this.x=e._x,this.y=e._y,this.z=e._z,this}setFromColor(e){return this.x=e.r,this.y=e.g,this.z=e.b,this}equals(e){return e.x===this.x&&e.y===this.y&&e.z===this.z}fromArray(e,t=0){return this.x=e[t],this.y=e[t+1],this.z=e[t+2],this}toArray(e=[],t=0){return e[t]=this.x,e[t+1]=this.y,e[t+2]=this.z,e}fromBufferAttribute(e,t){return this.x=e.getX(t),this.y=e.getY(t),this.z=e.getZ(t),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this}randomDirection(){let e=Math.random()*Math.PI*2,t=2*Math.random()-1,r=Math.sqrt(1-t*t);return this.x=r*Math.cos(e),this.y=t,this.z=r*Math.sin(e),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z}}let P=new C,E=new k;class A{constructor(e,t,r,n,i,s,a,o,l){A.prototype.isMatrix3=!0,this.elements=[1,0,0,0,1,0,0,0,1],void 0!==e&&this.set(e,t,r,n,i,s,a,o,l)}set(e,t,r,n,i,s,a,o,l){let u=this.elements;return u[0]=e,u[1]=n,u[2]=a,u[3]=t,u[4]=i,u[5]=o,u[6]=r,u[7]=s,u[8]=l,this}identity(){return this.set(1,0,0,0,1,0,0,0,1),this}copy(e){let t=this.elements,r=e.elements;return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t[4]=r[4],t[5]=r[5],t[6]=r[6],t[7]=r[7],t[8]=r[8],this}extractBasis(e,t,r){return e.setFromMatrix3Column(this,0),t.setFromMatrix3Column(this,1),r.setFromMatrix3Column(this,2),this}setFromMatrix4(e){let t=e.elements;return this.set(t[0],t[4],t[8],t[1],t[5],t[9],t[2],t[6],t[10]),this}multiply(e){return this.multiplyMatrices(this,e)}premultiply(e){return this.multiplyMatrices(e,this)}multiplyMatrices(e,t){let r=e.elements,n=t.elements,i=this.elements,s=r[0],a=r[3],o=r[6],l=r[1],u=r[4],h=r[7],c=r[2],d=r[5],f=r[8],p=n[0],m=n[3],y=n[6],g=n[1],x=n[4],b=n[7],v=n[2],w=n[5],S=n[8];return i[0]=s*p+a*g+o*v,i[3]=s*m+a*x+o*w,i[6]=s*y+a*b+o*S,i[1]=l*p+u*g+h*v,i[4]=l*m+u*x+h*w,i[7]=l*y+u*b+h*S,i[2]=c*p+d*g+f*v,i[5]=c*m+d*x+f*w,i[8]=c*y+d*b+f*S,this}multiplyScalar(e){let t=this.elements;return t[0]*=e,t[3]*=e,t[6]*=e,t[1]*=e,t[4]*=e,t[7]*=e,t[2]*=e,t[5]*=e,t[8]*=e,this}determinant(){let e=this.elements,t=e[0],r=e[1],n=e[2],i=e[3],s=e[4],a=e[5],o=e[6],l=e[7],u=e[8];return t*s*u-t*a*l-r*i*u+r*a*o+n*i*l-n*s*o}invert(){let e=this.elements,t=e[0],r=e[1],n=e[2],i=e[3],s=e[4],a=e[5],o=e[6],l=e[7],u=e[8],h=u*s-a*l,c=a*o-u*i,d=l*i-s*o,f=t*h+r*c+n*d;if(0===f)return this.set(0,0,0,0,0,0,0,0,0);let p=1/f;return e[0]=h*p,e[1]=(n*l-u*r)*p,e[2]=(a*r-n*s)*p,e[3]=c*p,e[4]=(u*t-n*o)*p,e[5]=(n*i-a*t)*p,e[6]=d*p,e[7]=(r*o-l*t)*p,e[8]=(s*t-r*i)*p,this}transpose(){let e,t=this.elements;return e=t[1],t[1]=t[3],t[3]=e,e=t[2],t[2]=t[6],t[6]=e,e=t[5],t[5]=t[7],t[7]=e,this}getNormalMatrix(e){return this.setFromMatrix4(e).invert().transpose()}transposeIntoArray(e){let t=this.elements;return e[0]=t[0],e[1]=t[3],e[2]=t[6],e[3]=t[1],e[4]=t[4],e[5]=t[7],e[6]=t[2],e[7]=t[5],e[8]=t[8],this}setUvTransform(e,t,r,n,i,s,a){let o=Math.cos(i),l=Math.sin(i);return this.set(r*o,r*l,-r*(o*s+l*a)+s+e,-n*l,n*o,-n*(-l*s+o*a)+a+t,0,0,1),this}scale(e,t){return this.premultiply(N.makeScale(e,t)),this}rotate(e){return this.premultiply(N.makeRotation(-e)),this}translate(e,t){return this.premultiply(N.makeTranslation(e,t)),this}makeTranslation(e,t){return e.isVector2?this.set(1,0,e.x,0,1,e.y,0,0,1):this.set(1,0,e,0,1,t,0,0,1),this}makeRotation(e){let t=Math.cos(e),r=Math.sin(e);return this.set(t,-r,0,r,t,0,0,0,1),this}makeScale(e,t){return this.set(e,0,0,0,t,0,0,0,1),this}equals(e){let t=this.elements,r=e.elements;for(let e=0;e<9;e++)if(t[e]!==r[e])return!1;return!0}fromArray(e,t=0){for(let r=0;r<9;r++)this.elements[r]=e[r+t];return this}toArray(e=[],t=0){let r=this.elements;return e[t]=r[0],e[t+1]=r[1],e[t+2]=r[2],e[t+3]=r[3],e[t+4]=r[4],e[t+5]=r[5],e[t+6]=r[6],e[t+7]=r[7],e[t+8]=r[8],e}clone(){return new this.constructor().fromArray(this.elements)}}let N=new A;function T(e){return document.createElementNS("http://www.w3.org/1999/xhtml",e)}Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array;let j={};function O(e){e in j||(j[e]=!0,console.warn(e))}let R=new A().set(.4123908,.3575843,.1804808,.212639,.7151687,.0721923,.0193308,.1191948,.9505322),I=new A().set(3.2409699,-1.5373832,-.4986108,-.9692436,1.8759675,.0415551,.0556301,-.203977,1.0569715),L=function(){let e={enabled:!0,workingColorSpace:p,spaces:{},convert:function(e,t,r){return!1!==this.enabled&&t!==r&&t&&r&&(this.spaces[t].transfer===y&&(e.r=F(e.r),e.g=F(e.g),e.b=F(e.b)),this.spaces[t].primaries!==this.spaces[r].primaries&&(e.applyMatrix3(this.spaces[t].toXYZ),e.applyMatrix3(this.spaces[r].fromXYZ)),this.spaces[r].transfer===y&&(e.r=V(e.r),e.g=V(e.g),e.b=V(e.b))),e},workingToColorSpace:function(e,t){return this.convert(e,this.workingColorSpace,t)},colorSpaceToWorking:function(e,t){return this.convert(e,t,this.workingColorSpace)},getPrimaries:function(e){return this.spaces[e].primaries},getTransfer:function(e){return""===e?m:this.spaces[e].transfer},getLuminanceCoefficients:function(e,t=this.workingColorSpace){return e.fromArray(this.spaces[t].luminanceCoefficients)},define:function(e){Object.assign(this.spaces,e)},_getMatrix:function(e,t,r){return e.copy(this.spaces[t].toXYZ).multiply(this.spaces[r].fromXYZ)},_getDrawingBufferColorSpace:function(e){return this.spaces[e].outputColorSpaceConfig.drawingBufferColorSpace},_getUnpackColorSpace:function(e=this.workingColorSpace){return this.spaces[e].workingColorSpaceConfig.unpackColorSpace},fromWorkingColorSpace:function(t,r){return O("THREE.ColorManagement: .fromWorkingColorSpace() has been renamed to .workingToColorSpace()."),e.workingToColorSpace(t,r)},toWorkingColorSpace:function(t,r){return O("THREE.ColorManagement: .toWorkingColorSpace() has been renamed to .colorSpaceToWorking()."),e.colorSpaceToWorking(t,r)}},t=[.64,.33,.3,.6,.15,.06],r=[.2126,.7152,.0722],n=[.3127,.329];return e.define({[p]:{primaries:t,whitePoint:n,transfer:m,toXYZ:R,fromXYZ:I,luminanceCoefficients:r,workingColorSpaceConfig:{unpackColorSpace:f},outputColorSpaceConfig:{drawingBufferColorSpace:f}},[f]:{primaries:t,whitePoint:n,transfer:y,toXYZ:R,fromXYZ:I,luminanceCoefficients:r,outputColorSpaceConfig:{drawingBufferColorSpace:f}}}),e}();function F(e){return e<.04045?.0773993808*e:Math.pow(.9478672986*e+.0521327014,2.4)}function V(e){return e<.0031308?12.92*e:1.055*Math.pow(e,.41666)-.055}class B{static getDataURL(e,t="image/png"){let r;if(/^data:/i.test(e.src)||"undefined"==typeof HTMLCanvasElement)return e.src;if(e instanceof HTMLCanvasElement)r=e;else{void 0===n&&(n=T("canvas")),n.width=e.width,n.height=e.height;let t=n.getContext("2d");e instanceof ImageData?t.putImageData(e,0,0):t.drawImage(e,0,0,e.width,e.height),r=n}return r.toDataURL(t)}static sRGBToLinear(e){if("undefined"!=typeof HTMLImageElement&&e instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&e instanceof ImageBitmap){let t=T("canvas");t.width=e.width,t.height=e.height;let r=t.getContext("2d");r.drawImage(e,0,0,e.width,e.height);let n=r.getImageData(0,0,e.width,e.height),i=n.data;for(let e=0;e<i.length;e++)i[e]=255*F(i[e]/255);return r.putImageData(n,0,0),t}if(!e.data)return console.warn("THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."),e;{let t=e.data.slice(0);for(let e=0;e<t.length;e++)t instanceof Uint8Array||t instanceof Uint8ClampedArray?t[e]=Math.floor(255*F(t[e]/255)):t[e]=F(t[e]);return{data:t,width:e.width,height:e.height}}}}let D=0;class U{constructor(e=null){this.isSource=!0,Object.defineProperty(this,"id",{value:D++}),this.uuid=w(),this.data=e,this.dataReady=!0,this.version=0}getSize(e){let t=this.data;return t instanceof HTMLVideoElement?e.set(t.videoWidth,t.videoHeight):null!==t?e.set(t.width,t.height,t.depth||0):e.set(0,0,0),e}set needsUpdate(e){!0===e&&this.version++}toJSON(e){let t=void 0===e||"string"==typeof e;if(!t&&void 0!==e.images[this.uuid])return e.images[this.uuid];let r={uuid:this.uuid,url:""},n=this.data;if(null!==n){let e;if(Array.isArray(n)){e=[];for(let t=0,r=n.length;t<r;t++)n[t].isDataTexture?e.push(W(n[t].image)):e.push(W(n[t]))}else e=W(n);r.url=e}return t||(e.images[this.uuid]=r),r}}function W(e){return"undefined"!=typeof HTMLImageElement&&e instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&e instanceof ImageBitmap?B.getDataURL(e):e.data?{data:Array.from(e.data),width:e.width,height:e.height,type:e.data.constructor.name}:(console.warn("THREE.Texture: Unable to serialize Texture."),{})}let H=0,q=new C;class J extends g{constructor(e=J.DEFAULT_IMAGE,t=J.DEFAULT_MAPPING,r=1001,n=1001,i=1006,s=1008,a=1023,o=1009,l=J.DEFAULT_ANISOTROPY,u=""){super(),this.isTexture=!0,Object.defineProperty(this,"id",{value:H++}),this.uuid=w(),this.name="",this.source=new U(e),this.mipmaps=[],this.mapping=t,this.channel=0,this.wrapS=r,this.wrapT=n,this.magFilter=i,this.minFilter=s,this.anisotropy=l,this.format=a,this.internalFormat=null,this.type=o,this.offset=new z(0,0),this.repeat=new z(1,1),this.center=new z(0,0),this.rotation=0,this.matrixAutoUpdate=!0,this.matrix=new A,this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,this.colorSpace=u,this.userData={},this.updateRanges=[],this.version=0,this.onUpdate=null,this.renderTarget=null,this.isRenderTargetTexture=!1,this.isArrayTexture=!!e&&!!e.depth&&e.depth>1,this.pmremVersion=0}get width(){return this.source.getSize(q).x}get height(){return this.source.getSize(q).y}get depth(){return this.source.getSize(q).z}get image(){return this.source.data}set image(e=null){this.source.data=e}updateMatrix(){this.matrix.setUvTransform(this.offset.x,this.offset.y,this.repeat.x,this.repeat.y,this.rotation,this.center.x,this.center.y)}addUpdateRange(e,t){this.updateRanges.push({start:e,count:t})}clearUpdateRanges(){this.updateRanges.length=0}clone(){return new this.constructor().copy(this)}copy(e){return this.name=e.name,this.source=e.source,this.mipmaps=e.mipmaps.slice(0),this.mapping=e.mapping,this.channel=e.channel,this.wrapS=e.wrapS,this.wrapT=e.wrapT,this.magFilter=e.magFilter,this.minFilter=e.minFilter,this.anisotropy=e.anisotropy,this.format=e.format,this.internalFormat=e.internalFormat,this.type=e.type,this.offset.copy(e.offset),this.repeat.copy(e.repeat),this.center.copy(e.center),this.rotation=e.rotation,this.matrixAutoUpdate=e.matrixAutoUpdate,this.matrix.copy(e.matrix),this.generateMipmaps=e.generateMipmaps,this.premultiplyAlpha=e.premultiplyAlpha,this.flipY=e.flipY,this.unpackAlignment=e.unpackAlignment,this.colorSpace=e.colorSpace,this.renderTarget=e.renderTarget,this.isRenderTargetTexture=e.isRenderTargetTexture,this.isArrayTexture=e.isArrayTexture,this.userData=JSON.parse(JSON.stringify(e.userData)),this.needsUpdate=!0,this}setValues(e){for(let t in e){let r=e[t];if(void 0===r){console.warn(`THREE.Texture.setValues(): parameter '${t}' has value of undefined.`);continue}let n=this[t];if(void 0===n){console.warn(`THREE.Texture.setValues(): property '${t}' does not exist.`);continue}n&&r&&n.isVector2&&r.isVector2||n&&r&&n.isVector3&&r.isVector3||n&&r&&n.isMatrix3&&r.isMatrix3?n.copy(r):this[t]=r}}toJSON(e){let t=void 0===e||"string"==typeof e;if(!t&&void 0!==e.textures[this.uuid])return e.textures[this.uuid];let r={metadata:{version:4.7,type:"Texture",generator:"Texture.toJSON"},uuid:this.uuid,name:this.name,image:this.source.toJSON(e).uuid,mapping:this.mapping,channel:this.channel,repeat:[this.repeat.x,this.repeat.y],offset:[this.offset.x,this.offset.y],center:[this.center.x,this.center.y],rotation:this.rotation,wrap:[this.wrapS,this.wrapT],format:this.format,internalFormat:this.internalFormat,type:this.type,colorSpace:this.colorSpace,minFilter:this.minFilter,magFilter:this.magFilter,anisotropy:this.anisotropy,flipY:this.flipY,generateMipmaps:this.generateMipmaps,premultiplyAlpha:this.premultiplyAlpha,unpackAlignment:this.unpackAlignment};return Object.keys(this.userData).length>0&&(r.userData=this.userData),t||(e.textures[this.uuid]=r),r}dispose(){this.dispatchEvent({type:"dispose"})}transformUv(e){if(300!==this.mapping)return e;if(e.applyMatrix3(this.matrix),e.x<0||e.x>1)switch(this.wrapS){case 1e3:e.x=e.x-Math.floor(e.x);break;case 1001:e.x=e.x<0?0:1;break;case 1002:1===Math.abs(Math.floor(e.x)%2)?e.x=Math.ceil(e.x)-e.x:e.x=e.x-Math.floor(e.x)}if(e.y<0||e.y>1)switch(this.wrapT){case 1e3:e.y=e.y-Math.floor(e.y);break;case 1001:e.y=e.y<0?0:1;break;case 1002:1===Math.abs(Math.floor(e.y)%2)?e.y=Math.ceil(e.y)-e.y:e.y=e.y-Math.floor(e.y)}return this.flipY&&(e.y=1-e.y),e}set needsUpdate(e){!0===e&&(this.version++,this.source.needsUpdate=!0)}set needsPMREMUpdate(e){!0===e&&this.pmremVersion++}}J.DEFAULT_IMAGE=null,J.DEFAULT_MAPPING=300,J.DEFAULT_ANISOTROPY=1;class Z{constructor(e=0,t=0,r=0,n=1){Z.prototype.isVector4=!0,this.x=e,this.y=t,this.z=r,this.w=n}get width(){return this.z}set width(e){this.z=e}get height(){return this.w}set height(e){this.w=e}set(e,t,r,n){return this.x=e,this.y=t,this.z=r,this.w=n,this}setScalar(e){return this.x=e,this.y=e,this.z=e,this.w=e,this}setX(e){return this.x=e,this}setY(e){return this.y=e,this}setZ(e){return this.z=e,this}setW(e){return this.w=e,this}setComponent(e,t){switch(e){case 0:this.x=t;break;case 1:this.y=t;break;case 2:this.z=t;break;case 3:this.w=t;break;default:throw Error("index is out of range: "+e)}return this}getComponent(e){switch(e){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw Error("index is out of range: "+e)}}clone(){return new this.constructor(this.x,this.y,this.z,this.w)}copy(e){return this.x=e.x,this.y=e.y,this.z=e.z,this.w=void 0!==e.w?e.w:1,this}add(e){return this.x+=e.x,this.y+=e.y,this.z+=e.z,this.w+=e.w,this}addScalar(e){return this.x+=e,this.y+=e,this.z+=e,this.w+=e,this}addVectors(e,t){return this.x=e.x+t.x,this.y=e.y+t.y,this.z=e.z+t.z,this.w=e.w+t.w,this}addScaledVector(e,t){return this.x+=e.x*t,this.y+=e.y*t,this.z+=e.z*t,this.w+=e.w*t,this}sub(e){return this.x-=e.x,this.y-=e.y,this.z-=e.z,this.w-=e.w,this}subScalar(e){return this.x-=e,this.y-=e,this.z-=e,this.w-=e,this}subVectors(e,t){return this.x=e.x-t.x,this.y=e.y-t.y,this.z=e.z-t.z,this.w=e.w-t.w,this}multiply(e){return this.x*=e.x,this.y*=e.y,this.z*=e.z,this.w*=e.w,this}multiplyScalar(e){return this.x*=e,this.y*=e,this.z*=e,this.w*=e,this}applyMatrix4(e){let t=this.x,r=this.y,n=this.z,i=this.w,s=e.elements;return this.x=s[0]*t+s[4]*r+s[8]*n+s[12]*i,this.y=s[1]*t+s[5]*r+s[9]*n+s[13]*i,this.z=s[2]*t+s[6]*r+s[10]*n+s[14]*i,this.w=s[3]*t+s[7]*r+s[11]*n+s[15]*i,this}divide(e){return this.x/=e.x,this.y/=e.y,this.z/=e.z,this.w/=e.w,this}divideScalar(e){return this.multiplyScalar(1/e)}setAxisAngleFromQuaternion(e){this.w=2*Math.acos(e.w);let t=Math.sqrt(1-e.w*e.w);return t<1e-4?(this.x=1,this.y=0,this.z=0):(this.x=e.x/t,this.y=e.y/t,this.z=e.z/t),this}setAxisAngleFromRotationMatrix(e){let t,r,n,i,s=e.elements,a=s[0],o=s[4],l=s[8],u=s[1],h=s[5],c=s[9],d=s[2],f=s[6],p=s[10];if(.01>Math.abs(o-u)&&.01>Math.abs(l-d)&&.01>Math.abs(c-f)){if(.1>Math.abs(o+u)&&.1>Math.abs(l+d)&&.1>Math.abs(c+f)&&.1>Math.abs(a+h+p-3))return this.set(1,0,0,0),this;t=Math.PI;let e=(a+1)/2,s=(h+1)/2,m=(p+1)/2,y=(o+u)/4,g=(l+d)/4,x=(c+f)/4;return e>s&&e>m?e<.01?(r=0,n=.*********,i=.*********):(n=y/(r=Math.sqrt(e)),i=g/r):s>m?s<.01?(r=.*********,n=0,i=.*********):(r=y/(n=Math.sqrt(s)),i=x/n):m<.01?(r=.*********,n=.*********,i=0):(r=g/(i=Math.sqrt(m)),n=x/i),this.set(r,n,i,t),this}let m=Math.sqrt((f-c)*(f-c)+(l-d)*(l-d)+(u-o)*(u-o));return .001>Math.abs(m)&&(m=1),this.x=(f-c)/m,this.y=(l-d)/m,this.z=(u-o)/m,this.w=Math.acos((a+h+p-1)/2),this}setFromMatrixPosition(e){let t=e.elements;return this.x=t[12],this.y=t[13],this.z=t[14],this.w=t[15],this}min(e){return this.x=Math.min(this.x,e.x),this.y=Math.min(this.y,e.y),this.z=Math.min(this.z,e.z),this.w=Math.min(this.w,e.w),this}max(e){return this.x=Math.max(this.x,e.x),this.y=Math.max(this.y,e.y),this.z=Math.max(this.z,e.z),this.w=Math.max(this.w,e.w),this}clamp(e,t){return this.x=S(this.x,e.x,t.x),this.y=S(this.y,e.y,t.y),this.z=S(this.z,e.z,t.z),this.w=S(this.w,e.w,t.w),this}clampScalar(e,t){return this.x=S(this.x,e,t),this.y=S(this.y,e,t),this.z=S(this.z,e,t),this.w=S(this.w,e,t),this}clampLength(e,t){let r=this.length();return this.divideScalar(r||1).multiplyScalar(S(r,e,t))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this.w=Math.floor(this.w),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this.w=Math.ceil(this.w),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this.w=Math.round(this.w),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this.w=Math.trunc(this.w),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this}dot(e){return this.x*e.x+this.y*e.y+this.z*e.z+this.w*e.w}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)}normalize(){return this.divideScalar(this.length()||1)}setLength(e){return this.normalize().multiplyScalar(e)}lerp(e,t){return this.x+=(e.x-this.x)*t,this.y+=(e.y-this.y)*t,this.z+=(e.z-this.z)*t,this.w+=(e.w-this.w)*t,this}lerpVectors(e,t,r){return this.x=e.x+(t.x-e.x)*r,this.y=e.y+(t.y-e.y)*r,this.z=e.z+(t.z-e.z)*r,this.w=e.w+(t.w-e.w)*r,this}equals(e){return e.x===this.x&&e.y===this.y&&e.z===this.z&&e.w===this.w}fromArray(e,t=0){return this.x=e[t],this.y=e[t+1],this.z=e[t+2],this.w=e[t+3],this}toArray(e=[],t=0){return e[t]=this.x,e[t+1]=this.y,e[t+2]=this.z,e[t+3]=this.w,e}fromBufferAttribute(e,t){return this.x=e.getX(t),this.y=e.getY(t),this.z=e.getZ(t),this.w=e.getW(t),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this.w=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z,yield this.w}}class Y{constructor(e=new C(Infinity,Infinity,Infinity),t=new C(-1/0,-1/0,-1/0)){this.isBox3=!0,this.min=e,this.max=t}set(e,t){return this.min.copy(e),this.max.copy(t),this}setFromArray(e){this.makeEmpty();for(let t=0,r=e.length;t<r;t+=3)this.expandByPoint(G.fromArray(e,t));return this}setFromBufferAttribute(e){this.makeEmpty();for(let t=0,r=e.count;t<r;t++)this.expandByPoint(G.fromBufferAttribute(e,t));return this}setFromPoints(e){this.makeEmpty();for(let t=0,r=e.length;t<r;t++)this.expandByPoint(e[t]);return this}setFromCenterAndSize(e,t){let r=G.copy(t).multiplyScalar(.5);return this.min.copy(e).sub(r),this.max.copy(e).add(r),this}setFromObject(e,t=!1){return this.makeEmpty(),this.expandByObject(e,t)}clone(){return new this.constructor().copy(this)}copy(e){return this.min.copy(e.min),this.max.copy(e.max),this}makeEmpty(){return this.min.x=this.min.y=this.min.z=Infinity,this.max.x=this.max.y=this.max.z=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z}getCenter(e){return this.isEmpty()?e.set(0,0,0):e.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(e){return this.isEmpty()?e.set(0,0,0):e.subVectors(this.max,this.min)}expandByPoint(e){return this.min.min(e),this.max.max(e),this}expandByVector(e){return this.min.sub(e),this.max.add(e),this}expandByScalar(e){return this.min.addScalar(-e),this.max.addScalar(e),this}expandByObject(e,t=!1){e.updateWorldMatrix(!1,!1);let r=e.geometry;if(void 0!==r){let n=r.getAttribute("position");if(!0===t&&void 0!==n&&!0!==e.isInstancedMesh)for(let t=0,r=n.count;t<r;t++)!0===e.isMesh?e.getVertexPosition(t,G):G.fromBufferAttribute(n,t),G.applyMatrix4(e.matrixWorld),this.expandByPoint(G);else void 0!==e.boundingBox?(null===e.boundingBox&&e.computeBoundingBox(),Q.copy(e.boundingBox)):(null===r.boundingBox&&r.computeBoundingBox(),Q.copy(r.boundingBox)),Q.applyMatrix4(e.matrixWorld),this.union(Q)}let n=e.children;for(let e=0,r=n.length;e<r;e++)this.expandByObject(n[e],t);return this}containsPoint(e){return e.x>=this.min.x&&e.x<=this.max.x&&e.y>=this.min.y&&e.y<=this.max.y&&e.z>=this.min.z&&e.z<=this.max.z}containsBox(e){return this.min.x<=e.min.x&&e.max.x<=this.max.x&&this.min.y<=e.min.y&&e.max.y<=this.max.y&&this.min.z<=e.min.z&&e.max.z<=this.max.z}getParameter(e,t){return t.set((e.x-this.min.x)/(this.max.x-this.min.x),(e.y-this.min.y)/(this.max.y-this.min.y),(e.z-this.min.z)/(this.max.z-this.min.z))}intersectsBox(e){return e.max.x>=this.min.x&&e.min.x<=this.max.x&&e.max.y>=this.min.y&&e.min.y<=this.max.y&&e.max.z>=this.min.z&&e.min.z<=this.max.z}intersectsSphere(e){return this.clampPoint(e.center,G),G.distanceToSquared(e.center)<=e.radius*e.radius}intersectsPlane(e){let t,r;return e.normal.x>0?(t=e.normal.x*this.min.x,r=e.normal.x*this.max.x):(t=e.normal.x*this.max.x,r=e.normal.x*this.min.x),e.normal.y>0?(t+=e.normal.y*this.min.y,r+=e.normal.y*this.max.y):(t+=e.normal.y*this.max.y,r+=e.normal.y*this.min.y),e.normal.z>0?(t+=e.normal.z*this.min.z,r+=e.normal.z*this.max.z):(t+=e.normal.z*this.max.z,r+=e.normal.z*this.min.z),t<=-e.constant&&r>=-e.constant}intersectsTriangle(e){if(this.isEmpty())return!1;this.getCenter(ei),es.subVectors(this.max,ei),$.subVectors(e.a,ei),K.subVectors(e.b,ei),ee.subVectors(e.c,ei),et.subVectors(K,$),er.subVectors(ee,K),en.subVectors($,ee);let t=[0,-et.z,et.y,0,-er.z,er.y,0,-en.z,en.y,et.z,0,-et.x,er.z,0,-er.x,en.z,0,-en.x,-et.y,et.x,0,-er.y,er.x,0,-en.y,en.x,0];return!!el(t,$,K,ee,es)&&!!el(t=[1,0,0,0,1,0,0,0,1],$,K,ee,es)&&(ea.crossVectors(et,er),el(t=[ea.x,ea.y,ea.z],$,K,ee,es))}clampPoint(e,t){return t.copy(e).clamp(this.min,this.max)}distanceToPoint(e){return this.clampPoint(e,G).distanceTo(e)}getBoundingSphere(e){return this.isEmpty()?e.makeEmpty():(this.getCenter(e.center),e.radius=.5*this.getSize(G).length()),e}intersect(e){return this.min.max(e.min),this.max.min(e.max),this.isEmpty()&&this.makeEmpty(),this}union(e){return this.min.min(e.min),this.max.max(e.max),this}applyMatrix4(e){return this.isEmpty()||(X[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(e),X[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(e),X[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(e),X[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(e),X[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(e),X[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(e),X[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(e),X[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(e),this.setFromPoints(X)),this}translate(e){return this.min.add(e),this.max.add(e),this}equals(e){return e.min.equals(this.min)&&e.max.equals(this.max)}toJSON(){return{min:this.min.toArray(),max:this.max.toArray()}}fromJSON(e){return this.min.fromArray(e.min),this.max.fromArray(e.max),this}}let X=[new C,new C,new C,new C,new C,new C,new C,new C],G=new C,Q=new Y,$=new C,K=new C,ee=new C,et=new C,er=new C,en=new C,ei=new C,es=new C,ea=new C,eo=new C;function el(e,t,r,n,i){for(let s=0,a=e.length-3;s<=a;s+=3){eo.fromArray(e,s);let a=i.x*Math.abs(eo.x)+i.y*Math.abs(eo.y)+i.z*Math.abs(eo.z),o=t.dot(eo),l=r.dot(eo),u=n.dot(eo);if(Math.max(-Math.max(o,l,u),Math.min(o,l,u))>a)return!1}return!0}let eu=new Y,eh=new C,ec=new C;class ed{constructor(e=new C,t=-1){this.isSphere=!0,this.center=e,this.radius=t}set(e,t){return this.center.copy(e),this.radius=t,this}setFromPoints(e,t){let r=this.center;void 0!==t?r.copy(t):eu.setFromPoints(e).getCenter(r);let n=0;for(let t=0,i=e.length;t<i;t++)n=Math.max(n,r.distanceToSquared(e[t]));return this.radius=Math.sqrt(n),this}copy(e){return this.center.copy(e.center),this.radius=e.radius,this}isEmpty(){return this.radius<0}makeEmpty(){return this.center.set(0,0,0),this.radius=-1,this}containsPoint(e){return e.distanceToSquared(this.center)<=this.radius*this.radius}distanceToPoint(e){return e.distanceTo(this.center)-this.radius}intersectsSphere(e){let t=this.radius+e.radius;return e.center.distanceToSquared(this.center)<=t*t}intersectsBox(e){return e.intersectsSphere(this)}intersectsPlane(e){return Math.abs(e.distanceToPoint(this.center))<=this.radius}clampPoint(e,t){let r=this.center.distanceToSquared(e);return t.copy(e),r>this.radius*this.radius&&(t.sub(this.center).normalize(),t.multiplyScalar(this.radius).add(this.center)),t}getBoundingBox(e){return this.isEmpty()?e.makeEmpty():(e.set(this.center,this.center),e.expandByScalar(this.radius)),e}applyMatrix4(e){return this.center.applyMatrix4(e),this.radius=this.radius*e.getMaxScaleOnAxis(),this}translate(e){return this.center.add(e),this}expandByPoint(e){if(this.isEmpty())return this.center.copy(e),this.radius=0,this;eh.subVectors(e,this.center);let t=eh.lengthSq();if(t>this.radius*this.radius){let e=Math.sqrt(t),r=(e-this.radius)*.5;this.center.addScaledVector(eh,r/e),this.radius+=r}return this}union(e){return e.isEmpty()||(this.isEmpty()?this.copy(e):!0===this.center.equals(e.center)?this.radius=Math.max(this.radius,e.radius):(ec.subVectors(e.center,this.center).setLength(e.radius),this.expandByPoint(eh.copy(e.center).add(ec)),this.expandByPoint(eh.copy(e.center).sub(ec)))),this}equals(e){return e.center.equals(this.center)&&e.radius===this.radius}clone(){return new this.constructor().copy(this)}toJSON(){return{radius:this.radius,center:this.center.toArray()}}fromJSON(e){return this.radius=e.radius,this.center.fromArray(e.center),this}}let ef=new C,ep=new C,em=new C,ey=new C,eg=new C,ex=new C,eb=new C;class ev{constructor(e=new C,t=new C(0,0,-1)){this.origin=e,this.direction=t}set(e,t){return this.origin.copy(e),this.direction.copy(t),this}copy(e){return this.origin.copy(e.origin),this.direction.copy(e.direction),this}at(e,t){return t.copy(this.origin).addScaledVector(this.direction,e)}lookAt(e){return this.direction.copy(e).sub(this.origin).normalize(),this}recast(e){return this.origin.copy(this.at(e,ef)),this}closestPointToPoint(e,t){t.subVectors(e,this.origin);let r=t.dot(this.direction);return r<0?t.copy(this.origin):t.copy(this.origin).addScaledVector(this.direction,r)}distanceToPoint(e){return Math.sqrt(this.distanceSqToPoint(e))}distanceSqToPoint(e){let t=ef.subVectors(e,this.origin).dot(this.direction);return t<0?this.origin.distanceToSquared(e):(ef.copy(this.origin).addScaledVector(this.direction,t),ef.distanceToSquared(e))}distanceSqToSegment(e,t,r,n){let i,s,a,o;ep.copy(e).add(t).multiplyScalar(.5),em.copy(t).sub(e).normalize(),ey.copy(this.origin).sub(ep);let l=.5*e.distanceTo(t),u=-this.direction.dot(em),h=ey.dot(this.direction),c=-ey.dot(em),d=ey.lengthSq(),f=Math.abs(1-u*u);if(f>0)if(i=u*c-h,s=u*h-c,o=l*f,i>=0)if(s>=-o)if(s<=o){let e=1/f;i*=e,s*=e,a=i*(i+u*s+2*h)+s*(u*i+s+2*c)+d}else a=-(i=Math.max(0,-(u*(s=l)+h)))*i+s*(s+2*c)+d;else a=-(i=Math.max(0,-(u*(s=-l)+h)))*i+s*(s+2*c)+d;else s<=-o?(s=(i=Math.max(0,-(-u*l+h)))>0?-l:Math.min(Math.max(-l,-c),l),a=-i*i+s*(s+2*c)+d):s<=o?(i=0,a=(s=Math.min(Math.max(-l,-c),l))*(s+2*c)+d):(s=(i=Math.max(0,-(u*l+h)))>0?l:Math.min(Math.max(-l,-c),l),a=-i*i+s*(s+2*c)+d);else s=u>0?-l:l,a=-(i=Math.max(0,-(u*s+h)))*i+s*(s+2*c)+d;return r&&r.copy(this.origin).addScaledVector(this.direction,i),n&&n.copy(ep).addScaledVector(em,s),a}intersectSphere(e,t){ef.subVectors(e.center,this.origin);let r=ef.dot(this.direction),n=ef.dot(ef)-r*r,i=e.radius*e.radius;if(n>i)return null;let s=Math.sqrt(i-n),a=r-s,o=r+s;return o<0?null:a<0?this.at(o,t):this.at(a,t)}intersectsSphere(e){return!(e.radius<0)&&this.distanceSqToPoint(e.center)<=e.radius*e.radius}distanceToPlane(e){let t=e.normal.dot(this.direction);if(0===t)return 0===e.distanceToPoint(this.origin)?0:null;let r=-(this.origin.dot(e.normal)+e.constant)/t;return r>=0?r:null}intersectPlane(e,t){let r=this.distanceToPlane(e);return null===r?null:this.at(r,t)}intersectsPlane(e){let t=e.distanceToPoint(this.origin);return!!(0===t||e.normal.dot(this.direction)*t<0)}intersectBox(e,t){let r,n,i,s,a,o,l=1/this.direction.x,u=1/this.direction.y,h=1/this.direction.z,c=this.origin;return(l>=0?(r=(e.min.x-c.x)*l,n=(e.max.x-c.x)*l):(r=(e.max.x-c.x)*l,n=(e.min.x-c.x)*l),u>=0?(i=(e.min.y-c.y)*u,s=(e.max.y-c.y)*u):(i=(e.max.y-c.y)*u,s=(e.min.y-c.y)*u),r>s||i>n||((i>r||isNaN(r))&&(r=i),(s<n||isNaN(n))&&(n=s),h>=0?(a=(e.min.z-c.z)*h,o=(e.max.z-c.z)*h):(a=(e.max.z-c.z)*h,o=(e.min.z-c.z)*h),r>o||a>n||((a>r||r!=r)&&(r=a),(o<n||n!=n)&&(n=o),n<0)))?null:this.at(r>=0?r:n,t)}intersectsBox(e){return null!==this.intersectBox(e,ef)}intersectTriangle(e,t,r,n,i){let s;eg.subVectors(t,e),ex.subVectors(r,e),eb.crossVectors(eg,ex);let a=this.direction.dot(eb);if(a>0){if(n)return null;s=1}else{if(!(a<0))return null;s=-1,a=-a}ey.subVectors(this.origin,e);let o=s*this.direction.dot(ex.crossVectors(ey,ex));if(o<0)return null;let l=s*this.direction.dot(eg.cross(ey));if(l<0||o+l>a)return null;let u=-s*ey.dot(eb);return u<0?null:this.at(u/a,i)}applyMatrix4(e){return this.origin.applyMatrix4(e),this.direction.transformDirection(e),this}equals(e){return e.origin.equals(this.origin)&&e.direction.equals(this.direction)}clone(){return new this.constructor().copy(this)}}class ew{constructor(e,t,r,n,i,s,a,o,l,u,h,c,d,f,p,m){ew.prototype.isMatrix4=!0,this.elements=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],void 0!==e&&this.set(e,t,r,n,i,s,a,o,l,u,h,c,d,f,p,m)}set(e,t,r,n,i,s,a,o,l,u,h,c,d,f,p,m){let y=this.elements;return y[0]=e,y[4]=t,y[8]=r,y[12]=n,y[1]=i,y[5]=s,y[9]=a,y[13]=o,y[2]=l,y[6]=u,y[10]=h,y[14]=c,y[3]=d,y[7]=f,y[11]=p,y[15]=m,this}identity(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this}clone(){return new ew().fromArray(this.elements)}copy(e){let t=this.elements,r=e.elements;return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t[4]=r[4],t[5]=r[5],t[6]=r[6],t[7]=r[7],t[8]=r[8],t[9]=r[9],t[10]=r[10],t[11]=r[11],t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15],this}copyPosition(e){let t=this.elements,r=e.elements;return t[12]=r[12],t[13]=r[13],t[14]=r[14],this}setFromMatrix3(e){let t=e.elements;return this.set(t[0],t[3],t[6],0,t[1],t[4],t[7],0,t[2],t[5],t[8],0,0,0,0,1),this}extractBasis(e,t,r){return e.setFromMatrixColumn(this,0),t.setFromMatrixColumn(this,1),r.setFromMatrixColumn(this,2),this}makeBasis(e,t,r){return this.set(e.x,t.x,r.x,0,e.y,t.y,r.y,0,e.z,t.z,r.z,0,0,0,0,1),this}extractRotation(e){let t=this.elements,r=e.elements,n=1/eS.setFromMatrixColumn(e,0).length(),i=1/eS.setFromMatrixColumn(e,1).length(),s=1/eS.setFromMatrixColumn(e,2).length();return t[0]=r[0]*n,t[1]=r[1]*n,t[2]=r[2]*n,t[3]=0,t[4]=r[4]*i,t[5]=r[5]*i,t[6]=r[6]*i,t[7]=0,t[8]=r[8]*s,t[9]=r[9]*s,t[10]=r[10]*s,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,this}makeRotationFromEuler(e){let t=this.elements,r=e.x,n=e.y,i=e.z,s=Math.cos(r),a=Math.sin(r),o=Math.cos(n),l=Math.sin(n),u=Math.cos(i),h=Math.sin(i);if("XYZ"===e.order){let e=s*u,r=s*h,n=a*u,i=a*h;t[0]=o*u,t[4]=-o*h,t[8]=l,t[1]=r+n*l,t[5]=e-i*l,t[9]=-a*o,t[2]=i-e*l,t[6]=n+r*l,t[10]=s*o}else if("YXZ"===e.order){let e=o*u,r=o*h,n=l*u,i=l*h;t[0]=e+i*a,t[4]=n*a-r,t[8]=s*l,t[1]=s*h,t[5]=s*u,t[9]=-a,t[2]=r*a-n,t[6]=i+e*a,t[10]=s*o}else if("ZXY"===e.order){let e=o*u,r=o*h,n=l*u,i=l*h;t[0]=e-i*a,t[4]=-s*h,t[8]=n+r*a,t[1]=r+n*a,t[5]=s*u,t[9]=i-e*a,t[2]=-s*l,t[6]=a,t[10]=s*o}else if("ZYX"===e.order){let e=s*u,r=s*h,n=a*u,i=a*h;t[0]=o*u,t[4]=n*l-r,t[8]=e*l+i,t[1]=o*h,t[5]=i*l+e,t[9]=r*l-n,t[2]=-l,t[6]=a*o,t[10]=s*o}else if("YZX"===e.order){let e=s*o,r=s*l,n=a*o,i=a*l;t[0]=o*u,t[4]=i-e*h,t[8]=n*h+r,t[1]=h,t[5]=s*u,t[9]=-a*u,t[2]=-l*u,t[6]=r*h+n,t[10]=e-i*h}else if("XZY"===e.order){let e=s*o,r=s*l,n=a*o,i=a*l;t[0]=o*u,t[4]=-h,t[8]=l*u,t[1]=e*h+i,t[5]=s*u,t[9]=r*h-n,t[2]=n*h-r,t[6]=a*u,t[10]=i*h+e}return t[3]=0,t[7]=0,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,this}makeRotationFromQuaternion(e){return this.compose(e_,e,ez)}lookAt(e,t,r){let n=this.elements;return eP.subVectors(e,t),0===eP.lengthSq()&&(eP.z=1),eP.normalize(),ek.crossVectors(r,eP),0===ek.lengthSq()&&(1===Math.abs(r.z)?eP.x+=1e-4:eP.z+=1e-4,eP.normalize(),ek.crossVectors(r,eP)),ek.normalize(),eC.crossVectors(eP,ek),n[0]=ek.x,n[4]=eC.x,n[8]=eP.x,n[1]=ek.y,n[5]=eC.y,n[9]=eP.y,n[2]=ek.z,n[6]=eC.z,n[10]=eP.z,this}multiply(e){return this.multiplyMatrices(this,e)}premultiply(e){return this.multiplyMatrices(e,this)}multiplyMatrices(e,t){let r=e.elements,n=t.elements,i=this.elements,s=r[0],a=r[4],o=r[8],l=r[12],u=r[1],h=r[5],c=r[9],d=r[13],f=r[2],p=r[6],m=r[10],y=r[14],g=r[3],x=r[7],b=r[11],v=r[15],w=n[0],S=n[4],M=n[8],_=n[12],z=n[1],k=n[5],C=n[9],P=n[13],E=n[2],A=n[6],N=n[10],T=n[14],j=n[3],O=n[7],R=n[11],I=n[15];return i[0]=s*w+a*z+o*E+l*j,i[4]=s*S+a*k+o*A+l*O,i[8]=s*M+a*C+o*N+l*R,i[12]=s*_+a*P+o*T+l*I,i[1]=u*w+h*z+c*E+d*j,i[5]=u*S+h*k+c*A+d*O,i[9]=u*M+h*C+c*N+d*R,i[13]=u*_+h*P+c*T+d*I,i[2]=f*w+p*z+m*E+y*j,i[6]=f*S+p*k+m*A+y*O,i[10]=f*M+p*C+m*N+y*R,i[14]=f*_+p*P+m*T+y*I,i[3]=g*w+x*z+b*E+v*j,i[7]=g*S+x*k+b*A+v*O,i[11]=g*M+x*C+b*N+v*R,i[15]=g*_+x*P+b*T+v*I,this}multiplyScalar(e){let t=this.elements;return t[0]*=e,t[4]*=e,t[8]*=e,t[12]*=e,t[1]*=e,t[5]*=e,t[9]*=e,t[13]*=e,t[2]*=e,t[6]*=e,t[10]*=e,t[14]*=e,t[3]*=e,t[7]*=e,t[11]*=e,t[15]*=e,this}determinant(){let e=this.elements,t=e[0],r=e[4],n=e[8],i=e[12],s=e[1],a=e[5],o=e[9],l=e[13],u=e[2],h=e[6],c=e[10],d=e[14],f=e[3],p=e[7];return f*(i*o*h-n*l*h-i*a*c+r*l*c+n*a*d-r*o*d)+p*(t*o*d-t*l*c+i*s*c-n*s*d+n*l*u-i*o*u)+e[11]*(t*l*h-t*a*d-i*s*h+r*s*d+i*a*u-r*l*u)+e[15]*(-n*a*u-t*o*h+t*a*c+n*s*h-r*s*c+r*o*u)}transpose(){let e,t=this.elements;return e=t[1],t[1]=t[4],t[4]=e,e=t[2],t[2]=t[8],t[8]=e,e=t[6],t[6]=t[9],t[9]=e,e=t[3],t[3]=t[12],t[12]=e,e=t[7],t[7]=t[13],t[13]=e,e=t[11],t[11]=t[14],t[14]=e,this}setPosition(e,t,r){let n=this.elements;return e.isVector3?(n[12]=e.x,n[13]=e.y,n[14]=e.z):(n[12]=e,n[13]=t,n[14]=r),this}invert(){let e=this.elements,t=e[0],r=e[1],n=e[2],i=e[3],s=e[4],a=e[5],o=e[6],l=e[7],u=e[8],h=e[9],c=e[10],d=e[11],f=e[12],p=e[13],m=e[14],y=e[15],g=h*m*l-p*c*l+p*o*d-a*m*d-h*o*y+a*c*y,x=f*c*l-u*m*l-f*o*d+s*m*d+u*o*y-s*c*y,b=u*p*l-f*h*l+f*a*d-s*p*d-u*a*y+s*h*y,v=f*h*o-u*p*o-f*a*c+s*p*c+u*a*m-s*h*m,w=t*g+r*x+n*b+i*v;if(0===w)return this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);let S=1/w;return e[0]=g*S,e[1]=(p*c*i-h*m*i-p*n*d+r*m*d+h*n*y-r*c*y)*S,e[2]=(a*m*i-p*o*i+p*n*l-r*m*l-a*n*y+r*o*y)*S,e[3]=(h*o*i-a*c*i-h*n*l+r*c*l+a*n*d-r*o*d)*S,e[4]=x*S,e[5]=(u*m*i-f*c*i+f*n*d-t*m*d-u*n*y+t*c*y)*S,e[6]=(f*o*i-s*m*i-f*n*l+t*m*l+s*n*y-t*o*y)*S,e[7]=(s*c*i-u*o*i+u*n*l-t*c*l-s*n*d+t*o*d)*S,e[8]=b*S,e[9]=(f*h*i-u*p*i-f*r*d+t*p*d+u*r*y-t*h*y)*S,e[10]=(s*p*i-f*a*i+f*r*l-t*p*l-s*r*y+t*a*y)*S,e[11]=(u*a*i-s*h*i-u*r*l+t*h*l+s*r*d-t*a*d)*S,e[12]=v*S,e[13]=(u*p*n-f*h*n+f*r*c-t*p*c-u*r*m+t*h*m)*S,e[14]=(f*a*n-s*p*n-f*r*o+t*p*o+s*r*m-t*a*m)*S,e[15]=(s*h*n-u*a*n+u*r*o-t*h*o-s*r*c+t*a*c)*S,this}scale(e){let t=this.elements,r=e.x,n=e.y,i=e.z;return t[0]*=r,t[4]*=n,t[8]*=i,t[1]*=r,t[5]*=n,t[9]*=i,t[2]*=r,t[6]*=n,t[10]*=i,t[3]*=r,t[7]*=n,t[11]*=i,this}getMaxScaleOnAxis(){let e=this.elements,t=e[0]*e[0]+e[1]*e[1]+e[2]*e[2];return Math.sqrt(Math.max(t,e[4]*e[4]+e[5]*e[5]+e[6]*e[6],e[8]*e[8]+e[9]*e[9]+e[10]*e[10]))}makeTranslation(e,t,r){return e.isVector3?this.set(1,0,0,e.x,0,1,0,e.y,0,0,1,e.z,0,0,0,1):this.set(1,0,0,e,0,1,0,t,0,0,1,r,0,0,0,1),this}makeRotationX(e){let t=Math.cos(e),r=Math.sin(e);return this.set(1,0,0,0,0,t,-r,0,0,r,t,0,0,0,0,1),this}makeRotationY(e){let t=Math.cos(e),r=Math.sin(e);return this.set(t,0,r,0,0,1,0,0,-r,0,t,0,0,0,0,1),this}makeRotationZ(e){let t=Math.cos(e),r=Math.sin(e);return this.set(t,-r,0,0,r,t,0,0,0,0,1,0,0,0,0,1),this}makeRotationAxis(e,t){let r=Math.cos(t),n=Math.sin(t),i=1-r,s=e.x,a=e.y,o=e.z,l=i*s,u=i*a;return this.set(l*s+r,l*a-n*o,l*o+n*a,0,l*a+n*o,u*a+r,u*o-n*s,0,l*o-n*a,u*o+n*s,i*o*o+r,0,0,0,0,1),this}makeScale(e,t,r){return this.set(e,0,0,0,0,t,0,0,0,0,r,0,0,0,0,1),this}makeShear(e,t,r,n,i,s){return this.set(1,r,i,0,e,1,s,0,t,n,1,0,0,0,0,1),this}compose(e,t,r){let n=this.elements,i=t._x,s=t._y,a=t._z,o=t._w,l=i+i,u=s+s,h=a+a,c=i*l,d=i*u,f=i*h,p=s*u,m=s*h,y=a*h,g=o*l,x=o*u,b=o*h,v=r.x,w=r.y,S=r.z;return n[0]=(1-(p+y))*v,n[1]=(d+b)*v,n[2]=(f-x)*v,n[3]=0,n[4]=(d-b)*w,n[5]=(1-(c+y))*w,n[6]=(m+g)*w,n[7]=0,n[8]=(f+x)*S,n[9]=(m-g)*S,n[10]=(1-(c+p))*S,n[11]=0,n[12]=e.x,n[13]=e.y,n[14]=e.z,n[15]=1,this}decompose(e,t,r){let n=this.elements,i=eS.set(n[0],n[1],n[2]).length(),s=eS.set(n[4],n[5],n[6]).length(),a=eS.set(n[8],n[9],n[10]).length();0>this.determinant()&&(i=-i),e.x=n[12],e.y=n[13],e.z=n[14],eM.copy(this);let o=1/i,l=1/s,u=1/a;return eM.elements[0]*=o,eM.elements[1]*=o,eM.elements[2]*=o,eM.elements[4]*=l,eM.elements[5]*=l,eM.elements[6]*=l,eM.elements[8]*=u,eM.elements[9]*=u,eM.elements[10]*=u,t.setFromRotationMatrix(eM),r.x=i,r.y=s,r.z=a,this}makePerspective(e,t,r,n,i,s,a=2e3){let o,l,u=this.elements;if(2e3===a)o=-(s+i)/(s-i),l=-2*s*i/(s-i);else if(2001===a)o=-s/(s-i),l=-s*i/(s-i);else throw Error("THREE.Matrix4.makePerspective(): Invalid coordinate system: "+a);return u[0]=2*i/(t-e),u[4]=0,u[8]=(t+e)/(t-e),u[12]=0,u[1]=0,u[5]=2*i/(r-n),u[9]=(r+n)/(r-n),u[13]=0,u[2]=0,u[6]=0,u[10]=o,u[14]=l,u[3]=0,u[7]=0,u[11]=-1,u[15]=0,this}makeOrthographic(e,t,r,n,i,s,a=2e3){let o,l,u=this.elements,h=1/(t-e),c=1/(r-n),d=1/(s-i);if(2e3===a)o=(s+i)*d,l=-2*d;else if(2001===a)o=i*d,l=-1*d;else throw Error("THREE.Matrix4.makeOrthographic(): Invalid coordinate system: "+a);return u[0]=2*h,u[4]=0,u[8]=0,u[12]=-((t+e)*h),u[1]=0,u[5]=2*c,u[9]=0,u[13]=-((r+n)*c),u[2]=0,u[6]=0,u[10]=l,u[14]=-o,u[3]=0,u[7]=0,u[11]=0,u[15]=1,this}equals(e){let t=this.elements,r=e.elements;for(let e=0;e<16;e++)if(t[e]!==r[e])return!1;return!0}fromArray(e,t=0){for(let r=0;r<16;r++)this.elements[r]=e[r+t];return this}toArray(e=[],t=0){let r=this.elements;return e[t]=r[0],e[t+1]=r[1],e[t+2]=r[2],e[t+3]=r[3],e[t+4]=r[4],e[t+5]=r[5],e[t+6]=r[6],e[t+7]=r[7],e[t+8]=r[8],e[t+9]=r[9],e[t+10]=r[10],e[t+11]=r[11],e[t+12]=r[12],e[t+13]=r[13],e[t+14]=r[14],e[t+15]=r[15],e}}let eS=new C,eM=new ew,e_=new C(0,0,0),ez=new C(1,1,1),ek=new C,eC=new C,eP=new C,eE=new ew,eA=new k;class eN{constructor(e=0,t=0,r=0,n=eN.DEFAULT_ORDER){this.isEuler=!0,this._x=e,this._y=t,this._z=r,this._order=n}get x(){return this._x}set x(e){this._x=e,this._onChangeCallback()}get y(){return this._y}set y(e){this._y=e,this._onChangeCallback()}get z(){return this._z}set z(e){this._z=e,this._onChangeCallback()}get order(){return this._order}set order(e){this._order=e,this._onChangeCallback()}set(e,t,r,n=this._order){return this._x=e,this._y=t,this._z=r,this._order=n,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._order)}copy(e){return this._x=e._x,this._y=e._y,this._z=e._z,this._order=e._order,this._onChangeCallback(),this}setFromRotationMatrix(e,t=this._order,r=!0){let n=e.elements,i=n[0],s=n[4],a=n[8],o=n[1],l=n[5],u=n[9],h=n[2],c=n[6],d=n[10];switch(t){case"XYZ":this._y=Math.asin(S(a,-1,1)),.9999999>Math.abs(a)?(this._x=Math.atan2(-u,d),this._z=Math.atan2(-s,i)):(this._x=Math.atan2(c,l),this._z=0);break;case"YXZ":this._x=Math.asin(-S(u,-1,1)),.9999999>Math.abs(u)?(this._y=Math.atan2(a,d),this._z=Math.atan2(o,l)):(this._y=Math.atan2(-h,i),this._z=0);break;case"ZXY":this._x=Math.asin(S(c,-1,1)),.9999999>Math.abs(c)?(this._y=Math.atan2(-h,d),this._z=Math.atan2(-s,l)):(this._y=0,this._z=Math.atan2(o,i));break;case"ZYX":this._y=Math.asin(-S(h,-1,1)),.9999999>Math.abs(h)?(this._x=Math.atan2(c,d),this._z=Math.atan2(o,i)):(this._x=0,this._z=Math.atan2(-s,l));break;case"YZX":this._z=Math.asin(S(o,-1,1)),.9999999>Math.abs(o)?(this._x=Math.atan2(-u,l),this._y=Math.atan2(-h,i)):(this._x=0,this._y=Math.atan2(a,d));break;case"XZY":this._z=Math.asin(-S(s,-1,1)),.9999999>Math.abs(s)?(this._x=Math.atan2(c,l),this._y=Math.atan2(a,i)):(this._x=Math.atan2(-u,d),this._y=0);break;default:console.warn("THREE.Euler: .setFromRotationMatrix() encountered an unknown order: "+t)}return this._order=t,!0===r&&this._onChangeCallback(),this}setFromQuaternion(e,t,r){return eE.makeRotationFromQuaternion(e),this.setFromRotationMatrix(eE,t,r)}setFromVector3(e,t=this._order){return this.set(e.x,e.y,e.z,t)}reorder(e){return eA.setFromEuler(this),this.setFromQuaternion(eA,e)}equals(e){return e._x===this._x&&e._y===this._y&&e._z===this._z&&e._order===this._order}fromArray(e){return this._x=e[0],this._y=e[1],this._z=e[2],void 0!==e[3]&&(this._order=e[3]),this._onChangeCallback(),this}toArray(e=[],t=0){return e[t]=this._x,e[t+1]=this._y,e[t+2]=this._z,e[t+3]=this._order,e}_onChange(e){return this._onChangeCallback=e,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._order}}eN.DEFAULT_ORDER="XYZ";class eT{constructor(){this.mask=1}set(e){this.mask=1<<e>>>0}enable(e){this.mask|=1<<e}enableAll(){this.mask=-1}toggle(e){this.mask^=1<<e}disable(e){this.mask&=~(1<<e)}disableAll(){this.mask=0}test(e){return(this.mask&e.mask)!=0}isEnabled(e){return(this.mask&1<<e)!=0}}let ej=0,eO=new C,eR=new k,eI=new ew,eL=new C,eF=new C,eV=new C,eB=new k,eD=new C(1,0,0),eU=new C(0,1,0),eW=new C(0,0,1),eH={type:"added"},eq={type:"removed"},eJ={type:"childadded",child:null},eZ={type:"childremoved",child:null};class eY extends g{constructor(){super(),this.isObject3D=!0,Object.defineProperty(this,"id",{value:ej++}),this.uuid=w(),this.name="",this.type="Object3D",this.parent=null,this.children=[],this.up=eY.DEFAULT_UP.clone();let e=new C,t=new eN,r=new k,n=new C(1,1,1);t._onChange(function(){r.setFromEuler(t,!1)}),r._onChange(function(){t.setFromQuaternion(r,void 0,!1)}),Object.defineProperties(this,{position:{configurable:!0,enumerable:!0,value:e},rotation:{configurable:!0,enumerable:!0,value:t},quaternion:{configurable:!0,enumerable:!0,value:r},scale:{configurable:!0,enumerable:!0,value:n},modelViewMatrix:{value:new ew},normalMatrix:{value:new A}}),this.matrix=new ew,this.matrixWorld=new ew,this.matrixAutoUpdate=eY.DEFAULT_MATRIX_AUTO_UPDATE,this.matrixWorldAutoUpdate=eY.DEFAULT_MATRIX_WORLD_AUTO_UPDATE,this.matrixWorldNeedsUpdate=!1,this.layers=new eT,this.visible=!0,this.castShadow=!1,this.receiveShadow=!1,this.frustumCulled=!0,this.renderOrder=0,this.animations=[],this.customDepthMaterial=void 0,this.customDistanceMaterial=void 0,this.userData={}}onBeforeShadow(){}onAfterShadow(){}onBeforeRender(){}onAfterRender(){}applyMatrix4(e){this.matrixAutoUpdate&&this.updateMatrix(),this.matrix.premultiply(e),this.matrix.decompose(this.position,this.quaternion,this.scale)}applyQuaternion(e){return this.quaternion.premultiply(e),this}setRotationFromAxisAngle(e,t){this.quaternion.setFromAxisAngle(e,t)}setRotationFromEuler(e){this.quaternion.setFromEuler(e,!0)}setRotationFromMatrix(e){this.quaternion.setFromRotationMatrix(e)}setRotationFromQuaternion(e){this.quaternion.copy(e)}rotateOnAxis(e,t){return eR.setFromAxisAngle(e,t),this.quaternion.multiply(eR),this}rotateOnWorldAxis(e,t){return eR.setFromAxisAngle(e,t),this.quaternion.premultiply(eR),this}rotateX(e){return this.rotateOnAxis(eD,e)}rotateY(e){return this.rotateOnAxis(eU,e)}rotateZ(e){return this.rotateOnAxis(eW,e)}translateOnAxis(e,t){return eO.copy(e).applyQuaternion(this.quaternion),this.position.add(eO.multiplyScalar(t)),this}translateX(e){return this.translateOnAxis(eD,e)}translateY(e){return this.translateOnAxis(eU,e)}translateZ(e){return this.translateOnAxis(eW,e)}localToWorld(e){return this.updateWorldMatrix(!0,!1),e.applyMatrix4(this.matrixWorld)}worldToLocal(e){return this.updateWorldMatrix(!0,!1),e.applyMatrix4(eI.copy(this.matrixWorld).invert())}lookAt(e,t,r){e.isVector3?eL.copy(e):eL.set(e,t,r);let n=this.parent;this.updateWorldMatrix(!0,!1),eF.setFromMatrixPosition(this.matrixWorld),this.isCamera||this.isLight?eI.lookAt(eF,eL,this.up):eI.lookAt(eL,eF,this.up),this.quaternion.setFromRotationMatrix(eI),n&&(eI.extractRotation(n.matrixWorld),eR.setFromRotationMatrix(eI),this.quaternion.premultiply(eR.invert()))}add(e){if(arguments.length>1){for(let e=0;e<arguments.length;e++)this.add(arguments[e]);return this}return e===this?console.error("THREE.Object3D.add: object can't be added as a child of itself.",e):e&&e.isObject3D?(e.removeFromParent(),e.parent=this,this.children.push(e),e.dispatchEvent(eH),eJ.child=e,this.dispatchEvent(eJ),eJ.child=null):console.error("THREE.Object3D.add: object not an instance of THREE.Object3D.",e),this}remove(e){if(arguments.length>1){for(let e=0;e<arguments.length;e++)this.remove(arguments[e]);return this}let t=this.children.indexOf(e);return -1!==t&&(e.parent=null,this.children.splice(t,1),e.dispatchEvent(eq),eZ.child=e,this.dispatchEvent(eZ),eZ.child=null),this}removeFromParent(){let e=this.parent;return null!==e&&e.remove(this),this}clear(){return this.remove(...this.children)}attach(e){return this.updateWorldMatrix(!0,!1),eI.copy(this.matrixWorld).invert(),null!==e.parent&&(e.parent.updateWorldMatrix(!0,!1),eI.multiply(e.parent.matrixWorld)),e.applyMatrix4(eI),e.removeFromParent(),e.parent=this,this.children.push(e),e.updateWorldMatrix(!1,!0),e.dispatchEvent(eH),eJ.child=e,this.dispatchEvent(eJ),eJ.child=null,this}getObjectById(e){return this.getObjectByProperty("id",e)}getObjectByName(e){return this.getObjectByProperty("name",e)}getObjectByProperty(e,t){if(this[e]===t)return this;for(let r=0,n=this.children.length;r<n;r++){let n=this.children[r].getObjectByProperty(e,t);if(void 0!==n)return n}}getObjectsByProperty(e,t,r=[]){this[e]===t&&r.push(this);let n=this.children;for(let i=0,s=n.length;i<s;i++)n[i].getObjectsByProperty(e,t,r);return r}getWorldPosition(e){return this.updateWorldMatrix(!0,!1),e.setFromMatrixPosition(this.matrixWorld)}getWorldQuaternion(e){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(eF,e,eV),e}getWorldScale(e){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(eF,eB,e),e}getWorldDirection(e){this.updateWorldMatrix(!0,!1);let t=this.matrixWorld.elements;return e.set(t[8],t[9],t[10]).normalize()}raycast(){}traverse(e){e(this);let t=this.children;for(let r=0,n=t.length;r<n;r++)t[r].traverse(e)}traverseVisible(e){if(!1===this.visible)return;e(this);let t=this.children;for(let r=0,n=t.length;r<n;r++)t[r].traverseVisible(e)}traverseAncestors(e){let t=this.parent;null!==t&&(e(t),t.traverseAncestors(e))}updateMatrix(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0}updateMatrixWorld(e){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||e)&&(!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),this.matrixWorldNeedsUpdate=!1,e=!0);let t=this.children;for(let r=0,n=t.length;r<n;r++)t[r].updateMatrixWorld(e)}updateWorldMatrix(e,t){let r=this.parent;if(!0===e&&null!==r&&r.updateWorldMatrix(!0,!1),this.matrixAutoUpdate&&this.updateMatrix(),!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),!0===t){let e=this.children;for(let t=0,r=e.length;t<r;t++)e[t].updateWorldMatrix(!1,!0)}}toJSON(e){let t=void 0===e||"string"==typeof e,r={};t&&(e={geometries:{},materials:{},textures:{},images:{},shapes:{},skeletons:{},animations:{},nodes:{}},r.metadata={version:4.7,type:"Object",generator:"Object3D.toJSON"});let n={};function i(t,r){return void 0===t[r.uuid]&&(t[r.uuid]=r.toJSON(e)),r.uuid}if(n.uuid=this.uuid,n.type=this.type,""!==this.name&&(n.name=this.name),!0===this.castShadow&&(n.castShadow=!0),!0===this.receiveShadow&&(n.receiveShadow=!0),!1===this.visible&&(n.visible=!1),!1===this.frustumCulled&&(n.frustumCulled=!1),0!==this.renderOrder&&(n.renderOrder=this.renderOrder),Object.keys(this.userData).length>0&&(n.userData=this.userData),n.layers=this.layers.mask,n.matrix=this.matrix.toArray(),n.up=this.up.toArray(),!1===this.matrixAutoUpdate&&(n.matrixAutoUpdate=!1),this.isInstancedMesh&&(n.type="InstancedMesh",n.count=this.count,n.instanceMatrix=this.instanceMatrix.toJSON(),null!==this.instanceColor&&(n.instanceColor=this.instanceColor.toJSON())),this.isBatchedMesh&&(n.type="BatchedMesh",n.perObjectFrustumCulled=this.perObjectFrustumCulled,n.sortObjects=this.sortObjects,n.drawRanges=this._drawRanges,n.reservedRanges=this._reservedRanges,n.geometryInfo=this._geometryInfo.map(e=>({...e,boundingBox:e.boundingBox?e.boundingBox.toJSON():void 0,boundingSphere:e.boundingSphere?e.boundingSphere.toJSON():void 0})),n.instanceInfo=this._instanceInfo.map(e=>({...e})),n.availableInstanceIds=this._availableInstanceIds.slice(),n.availableGeometryIds=this._availableGeometryIds.slice(),n.nextIndexStart=this._nextIndexStart,n.nextVertexStart=this._nextVertexStart,n.geometryCount=this._geometryCount,n.maxInstanceCount=this._maxInstanceCount,n.maxVertexCount=this._maxVertexCount,n.maxIndexCount=this._maxIndexCount,n.geometryInitialized=this._geometryInitialized,n.matricesTexture=this._matricesTexture.toJSON(e),n.indirectTexture=this._indirectTexture.toJSON(e),null!==this._colorsTexture&&(n.colorsTexture=this._colorsTexture.toJSON(e)),null!==this.boundingSphere&&(n.boundingSphere=this.boundingSphere.toJSON()),null!==this.boundingBox&&(n.boundingBox=this.boundingBox.toJSON())),this.isScene)this.background&&(this.background.isColor?n.background=this.background.toJSON():this.background.isTexture&&(n.background=this.background.toJSON(e).uuid)),this.environment&&this.environment.isTexture&&!0!==this.environment.isRenderTargetTexture&&(n.environment=this.environment.toJSON(e).uuid);else if(this.isMesh||this.isLine||this.isPoints){n.geometry=i(e.geometries,this.geometry);let t=this.geometry.parameters;if(void 0!==t&&void 0!==t.shapes){let r=t.shapes;if(Array.isArray(r))for(let t=0,n=r.length;t<n;t++){let n=r[t];i(e.shapes,n)}else i(e.shapes,r)}}if(this.isSkinnedMesh&&(n.bindMode=this.bindMode,n.bindMatrix=this.bindMatrix.toArray(),void 0!==this.skeleton&&(i(e.skeletons,this.skeleton),n.skeleton=this.skeleton.uuid)),void 0!==this.material)if(Array.isArray(this.material)){let t=[];for(let r=0,n=this.material.length;r<n;r++)t.push(i(e.materials,this.material[r]));n.material=t}else n.material=i(e.materials,this.material);if(this.children.length>0){n.children=[];for(let t=0;t<this.children.length;t++)n.children.push(this.children[t].toJSON(e).object)}if(this.animations.length>0){n.animations=[];for(let t=0;t<this.animations.length;t++){let r=this.animations[t];n.animations.push(i(e.animations,r))}}if(t){let t=s(e.geometries),n=s(e.materials),i=s(e.textures),a=s(e.images),o=s(e.shapes),l=s(e.skeletons),u=s(e.animations),h=s(e.nodes);t.length>0&&(r.geometries=t),n.length>0&&(r.materials=n),i.length>0&&(r.textures=i),a.length>0&&(r.images=a),o.length>0&&(r.shapes=o),l.length>0&&(r.skeletons=l),u.length>0&&(r.animations=u),h.length>0&&(r.nodes=h)}return r.object=n,r;function s(e){let t=[];for(let r in e){let n=e[r];delete n.metadata,t.push(n)}return t}}clone(e){return new this.constructor().copy(this,e)}copy(e,t=!0){if(this.name=e.name,this.up.copy(e.up),this.position.copy(e.position),this.rotation.order=e.rotation.order,this.quaternion.copy(e.quaternion),this.scale.copy(e.scale),this.matrix.copy(e.matrix),this.matrixWorld.copy(e.matrixWorld),this.matrixAutoUpdate=e.matrixAutoUpdate,this.matrixWorldAutoUpdate=e.matrixWorldAutoUpdate,this.matrixWorldNeedsUpdate=e.matrixWorldNeedsUpdate,this.layers.mask=e.layers.mask,this.visible=e.visible,this.castShadow=e.castShadow,this.receiveShadow=e.receiveShadow,this.frustumCulled=e.frustumCulled,this.renderOrder=e.renderOrder,this.animations=e.animations.slice(),this.userData=JSON.parse(JSON.stringify(e.userData)),!0===t)for(let t=0;t<e.children.length;t++){let r=e.children[t];this.add(r.clone())}return this}}eY.DEFAULT_UP=new C(0,1,0),eY.DEFAULT_MATRIX_AUTO_UPDATE=!0,eY.DEFAULT_MATRIX_WORLD_AUTO_UPDATE=!0;let eX=new C,eG=new C,eQ=new C,e$=new C,eK=new C,e0=new C,e1=new C,e2=new C,e3=new C,e4=new C,e5=new Z,e6=new Z,e8=new Z;class e7{constructor(e=new C,t=new C,r=new C){this.a=e,this.b=t,this.c=r}static getNormal(e,t,r,n){n.subVectors(r,t),eX.subVectors(e,t),n.cross(eX);let i=n.lengthSq();return i>0?n.multiplyScalar(1/Math.sqrt(i)):n.set(0,0,0)}static getBarycoord(e,t,r,n,i){eX.subVectors(n,t),eG.subVectors(r,t),eQ.subVectors(e,t);let s=eX.dot(eX),a=eX.dot(eG),o=eX.dot(eQ),l=eG.dot(eG),u=eG.dot(eQ),h=s*l-a*a;if(0===h)return i.set(0,0,0),null;let c=1/h,d=(l*o-a*u)*c,f=(s*u-a*o)*c;return i.set(1-d-f,f,d)}static containsPoint(e,t,r,n){return null!==this.getBarycoord(e,t,r,n,e$)&&e$.x>=0&&e$.y>=0&&e$.x+e$.y<=1}static getInterpolation(e,t,r,n,i,s,a,o){return null===this.getBarycoord(e,t,r,n,e$)?(o.x=0,o.y=0,"z"in o&&(o.z=0),"w"in o&&(o.w=0),null):(o.setScalar(0),o.addScaledVector(i,e$.x),o.addScaledVector(s,e$.y),o.addScaledVector(a,e$.z),o)}static getInterpolatedAttribute(e,t,r,n,i,s){return e5.setScalar(0),e6.setScalar(0),e8.setScalar(0),e5.fromBufferAttribute(e,t),e6.fromBufferAttribute(e,r),e8.fromBufferAttribute(e,n),s.setScalar(0),s.addScaledVector(e5,i.x),s.addScaledVector(e6,i.y),s.addScaledVector(e8,i.z),s}static isFrontFacing(e,t,r,n){return eX.subVectors(r,t),eG.subVectors(e,t),0>eX.cross(eG).dot(n)}set(e,t,r){return this.a.copy(e),this.b.copy(t),this.c.copy(r),this}setFromPointsAndIndices(e,t,r,n){return this.a.copy(e[t]),this.b.copy(e[r]),this.c.copy(e[n]),this}setFromAttributeAndIndices(e,t,r,n){return this.a.fromBufferAttribute(e,t),this.b.fromBufferAttribute(e,r),this.c.fromBufferAttribute(e,n),this}clone(){return new this.constructor().copy(this)}copy(e){return this.a.copy(e.a),this.b.copy(e.b),this.c.copy(e.c),this}getArea(){return eX.subVectors(this.c,this.b),eG.subVectors(this.a,this.b),.5*eX.cross(eG).length()}getMidpoint(e){return e.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)}getNormal(e){return e7.getNormal(this.a,this.b,this.c,e)}getPlane(e){return e.setFromCoplanarPoints(this.a,this.b,this.c)}getBarycoord(e,t){return e7.getBarycoord(e,this.a,this.b,this.c,t)}getInterpolation(e,t,r,n,i){return e7.getInterpolation(e,this.a,this.b,this.c,t,r,n,i)}containsPoint(e){return e7.containsPoint(e,this.a,this.b,this.c)}isFrontFacing(e){return e7.isFrontFacing(this.a,this.b,this.c,e)}intersectsBox(e){return e.intersectsTriangle(this)}closestPointToPoint(e,t){let r,n,i=this.a,s=this.b,a=this.c;eK.subVectors(s,i),e0.subVectors(a,i),e2.subVectors(e,i);let o=eK.dot(e2),l=e0.dot(e2);if(o<=0&&l<=0)return t.copy(i);e3.subVectors(e,s);let u=eK.dot(e3),h=e0.dot(e3);if(u>=0&&h<=u)return t.copy(s);let c=o*h-u*l;if(c<=0&&o>=0&&u<=0)return r=o/(o-u),t.copy(i).addScaledVector(eK,r);e4.subVectors(e,a);let d=eK.dot(e4),f=e0.dot(e4);if(f>=0&&d<=f)return t.copy(a);let p=d*l-o*f;if(p<=0&&l>=0&&f<=0)return n=l/(l-f),t.copy(i).addScaledVector(e0,n);let m=u*f-d*h;if(m<=0&&h-u>=0&&d-f>=0)return e1.subVectors(a,s),n=(h-u)/(h-u+(d-f)),t.copy(s).addScaledVector(e1,n);let y=1/(m+p+c);return r=p*y,n=c*y,t.copy(i).addScaledVector(eK,r).addScaledVector(e0,n)}equals(e){return e.a.equals(this.a)&&e.b.equals(this.b)&&e.c.equals(this.c)}}let e9={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32},te={h:0,s:0,l:0},tt={h:0,s:0,l:0};function tr(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*6*(2/3-r):e}class tn{constructor(e,t,r){return this.isColor=!0,this.r=1,this.g=1,this.b=1,this.set(e,t,r)}set(e,t,r){return void 0===t&&void 0===r?e&&e.isColor?this.copy(e):"number"==typeof e?this.setHex(e):"string"==typeof e&&this.setStyle(e):this.setRGB(e,t,r),this}setScalar(e){return this.r=e,this.g=e,this.b=e,this}setHex(e,t=f){return e=Math.floor(e),this.r=(e>>16&255)/255,this.g=(e>>8&255)/255,this.b=(255&e)/255,L.colorSpaceToWorking(this,t),this}setRGB(e,t,r,n=L.workingColorSpace){return this.r=e,this.g=t,this.b=r,L.colorSpaceToWorking(this,n),this}setHSL(e,t,r,n=L.workingColorSpace){if(e=(e%1+1)%1,t=S(t,0,1),r=S(r,0,1),0===t)this.r=this.g=this.b=r;else{let n=r<=.5?r*(1+t):r+t-r*t,i=2*r-n;this.r=tr(i,n,e+1/3),this.g=tr(i,n,e),this.b=tr(i,n,e-1/3)}return L.colorSpaceToWorking(this,n),this}setStyle(e,t=f){let r;function n(t){void 0!==t&&1>parseFloat(t)&&console.warn("THREE.Color: Alpha component of "+e+" will be ignored.")}if(r=/^(\w+)\(([^\)]*)\)/.exec(e)){let i,s=r[1],a=r[2];switch(s){case"rgb":case"rgba":if(i=/^\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return n(i[4]),this.setRGB(Math.min(255,parseInt(i[1],10))/255,Math.min(255,parseInt(i[2],10))/255,Math.min(255,parseInt(i[3],10))/255,t);if(i=/^\s*(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return n(i[4]),this.setRGB(Math.min(100,parseInt(i[1],10))/100,Math.min(100,parseInt(i[2],10))/100,Math.min(100,parseInt(i[3],10))/100,t);break;case"hsl":case"hsla":if(i=/^\s*(\d*\.?\d+)\s*,\s*(\d*\.?\d+)\%\s*,\s*(\d*\.?\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return n(i[4]),this.setHSL(parseFloat(i[1])/360,parseFloat(i[2])/100,parseFloat(i[3])/100,t);break;default:console.warn("THREE.Color: Unknown color model "+e)}}else if(r=/^\#([A-Fa-f\d]+)$/.exec(e)){let n=r[1],i=n.length;if(3===i)return this.setRGB(parseInt(n.charAt(0),16)/15,parseInt(n.charAt(1),16)/15,parseInt(n.charAt(2),16)/15,t);if(6===i)return this.setHex(parseInt(n,16),t);console.warn("THREE.Color: Invalid hex color "+e)}else if(e&&e.length>0)return this.setColorName(e,t);return this}setColorName(e,t=f){let r=e9[e.toLowerCase()];return void 0!==r?this.setHex(r,t):console.warn("THREE.Color: Unknown color "+e),this}clone(){return new this.constructor(this.r,this.g,this.b)}copy(e){return this.r=e.r,this.g=e.g,this.b=e.b,this}copySRGBToLinear(e){return this.r=F(e.r),this.g=F(e.g),this.b=F(e.b),this}copyLinearToSRGB(e){return this.r=V(e.r),this.g=V(e.g),this.b=V(e.b),this}convertSRGBToLinear(){return this.copySRGBToLinear(this),this}convertLinearToSRGB(){return this.copyLinearToSRGB(this),this}getHex(e=f){return L.workingToColorSpace(ti.copy(this),e),65536*Math.round(S(255*ti.r,0,255))+256*Math.round(S(255*ti.g,0,255))+Math.round(S(255*ti.b,0,255))}getHexString(e=f){return("000000"+this.getHex(e).toString(16)).slice(-6)}getHSL(e,t=L.workingColorSpace){let r,n;L.workingToColorSpace(ti.copy(this),t);let i=ti.r,s=ti.g,a=ti.b,o=Math.max(i,s,a),l=Math.min(i,s,a),u=(l+o)/2;if(l===o)r=0,n=0;else{let e=o-l;switch(n=u<=.5?e/(o+l):e/(2-o-l),o){case i:r=(s-a)/e+6*(s<a);break;case s:r=(a-i)/e+2;break;case a:r=(i-s)/e+4}r/=6}return e.h=r,e.s=n,e.l=u,e}getRGB(e,t=L.workingColorSpace){return L.workingToColorSpace(ti.copy(this),t),e.r=ti.r,e.g=ti.g,e.b=ti.b,e}getStyle(e=f){L.workingToColorSpace(ti.copy(this),e);let t=ti.r,r=ti.g,n=ti.b;return e!==f?`color(${e} ${t.toFixed(3)} ${r.toFixed(3)} ${n.toFixed(3)})`:`rgb(${Math.round(255*t)},${Math.round(255*r)},${Math.round(255*n)})`}offsetHSL(e,t,r){return this.getHSL(te),this.setHSL(te.h+e,te.s+t,te.l+r)}add(e){return this.r+=e.r,this.g+=e.g,this.b+=e.b,this}addColors(e,t){return this.r=e.r+t.r,this.g=e.g+t.g,this.b=e.b+t.b,this}addScalar(e){return this.r+=e,this.g+=e,this.b+=e,this}sub(e){return this.r=Math.max(0,this.r-e.r),this.g=Math.max(0,this.g-e.g),this.b=Math.max(0,this.b-e.b),this}multiply(e){return this.r*=e.r,this.g*=e.g,this.b*=e.b,this}multiplyScalar(e){return this.r*=e,this.g*=e,this.b*=e,this}lerp(e,t){return this.r+=(e.r-this.r)*t,this.g+=(e.g-this.g)*t,this.b+=(e.b-this.b)*t,this}lerpColors(e,t,r){return this.r=e.r+(t.r-e.r)*r,this.g=e.g+(t.g-e.g)*r,this.b=e.b+(t.b-e.b)*r,this}lerpHSL(e,t){var r,n,i;this.getHSL(te),e.getHSL(tt);let s=(r=te.h,(1-t)*r+t*tt.h),a=(n=te.s,(1-t)*n+t*tt.s),o=(i=te.l,(1-t)*i+t*tt.l);return this.setHSL(s,a,o),this}setFromVector3(e){return this.r=e.x,this.g=e.y,this.b=e.z,this}applyMatrix3(e){let t=this.r,r=this.g,n=this.b,i=e.elements;return this.r=i[0]*t+i[3]*r+i[6]*n,this.g=i[1]*t+i[4]*r+i[7]*n,this.b=i[2]*t+i[5]*r+i[8]*n,this}equals(e){return e.r===this.r&&e.g===this.g&&e.b===this.b}fromArray(e,t=0){return this.r=e[t],this.g=e[t+1],this.b=e[t+2],this}toArray(e=[],t=0){return e[t]=this.r,e[t+1]=this.g,e[t+2]=this.b,e}fromBufferAttribute(e,t){return this.r=e.getX(t),this.g=e.getY(t),this.b=e.getZ(t),this}toJSON(){return this.getHex()}*[Symbol.iterator](){yield this.r,yield this.g,yield this.b}}let ti=new tn;tn.NAMES=e9;let ts=0;class ta extends g{constructor(){super(),this.isMaterial=!0,Object.defineProperty(this,"id",{value:ts++}),this.uuid=w(),this.name="",this.type="Material",this.blending=1,this.side=0,this.vertexColors=!1,this.opacity=1,this.transparent=!1,this.alphaHash=!1,this.blendSrc=204,this.blendDst=205,this.blendEquation=100,this.blendSrcAlpha=null,this.blendDstAlpha=null,this.blendEquationAlpha=null,this.blendColor=new tn(0,0,0),this.blendAlpha=0,this.depthFunc=3,this.depthTest=!0,this.depthWrite=!0,this.stencilWriteMask=255,this.stencilFunc=519,this.stencilRef=0,this.stencilFuncMask=255,this.stencilFail=7680,this.stencilZFail=7680,this.stencilZPass=7680,this.stencilWrite=!1,this.clippingPlanes=null,this.clipIntersection=!1,this.clipShadows=!1,this.shadowSide=null,this.colorWrite=!0,this.precision=null,this.polygonOffset=!1,this.polygonOffsetFactor=0,this.polygonOffsetUnits=0,this.dithering=!1,this.alphaToCoverage=!1,this.premultipliedAlpha=!1,this.forceSinglePass=!1,this.allowOverride=!0,this.visible=!0,this.toneMapped=!0,this.userData={},this.version=0,this._alphaTest=0}get alphaTest(){return this._alphaTest}set alphaTest(e){this._alphaTest>0!=e>0&&this.version++,this._alphaTest=e}onBeforeRender(){}onBeforeCompile(){}customProgramCacheKey(){return this.onBeforeCompile.toString()}setValues(e){if(void 0!==e)for(let t in e){let r=e[t];if(void 0===r){console.warn(`THREE.Material: parameter '${t}' has value of undefined.`);continue}let n=this[t];if(void 0===n){console.warn(`THREE.Material: '${t}' is not a property of THREE.${this.type}.`);continue}n&&n.isColor?n.set(r):n&&n.isVector3&&r&&r.isVector3?n.copy(r):this[t]=r}}toJSON(e){let t=void 0===e||"string"==typeof e;t&&(e={textures:{},images:{}});let r={metadata:{version:4.7,type:"Material",generator:"Material.toJSON"}};function n(e){let t=[];for(let r in e){let n=e[r];delete n.metadata,t.push(n)}return t}if(r.uuid=this.uuid,r.type=this.type,""!==this.name&&(r.name=this.name),this.color&&this.color.isColor&&(r.color=this.color.getHex()),void 0!==this.roughness&&(r.roughness=this.roughness),void 0!==this.metalness&&(r.metalness=this.metalness),void 0!==this.sheen&&(r.sheen=this.sheen),this.sheenColor&&this.sheenColor.isColor&&(r.sheenColor=this.sheenColor.getHex()),void 0!==this.sheenRoughness&&(r.sheenRoughness=this.sheenRoughness),this.emissive&&this.emissive.isColor&&(r.emissive=this.emissive.getHex()),void 0!==this.emissiveIntensity&&1!==this.emissiveIntensity&&(r.emissiveIntensity=this.emissiveIntensity),this.specular&&this.specular.isColor&&(r.specular=this.specular.getHex()),void 0!==this.specularIntensity&&(r.specularIntensity=this.specularIntensity),this.specularColor&&this.specularColor.isColor&&(r.specularColor=this.specularColor.getHex()),void 0!==this.shininess&&(r.shininess=this.shininess),void 0!==this.clearcoat&&(r.clearcoat=this.clearcoat),void 0!==this.clearcoatRoughness&&(r.clearcoatRoughness=this.clearcoatRoughness),this.clearcoatMap&&this.clearcoatMap.isTexture&&(r.clearcoatMap=this.clearcoatMap.toJSON(e).uuid),this.clearcoatRoughnessMap&&this.clearcoatRoughnessMap.isTexture&&(r.clearcoatRoughnessMap=this.clearcoatRoughnessMap.toJSON(e).uuid),this.clearcoatNormalMap&&this.clearcoatNormalMap.isTexture&&(r.clearcoatNormalMap=this.clearcoatNormalMap.toJSON(e).uuid,r.clearcoatNormalScale=this.clearcoatNormalScale.toArray()),void 0!==this.dispersion&&(r.dispersion=this.dispersion),void 0!==this.iridescence&&(r.iridescence=this.iridescence),void 0!==this.iridescenceIOR&&(r.iridescenceIOR=this.iridescenceIOR),void 0!==this.iridescenceThicknessRange&&(r.iridescenceThicknessRange=this.iridescenceThicknessRange),this.iridescenceMap&&this.iridescenceMap.isTexture&&(r.iridescenceMap=this.iridescenceMap.toJSON(e).uuid),this.iridescenceThicknessMap&&this.iridescenceThicknessMap.isTexture&&(r.iridescenceThicknessMap=this.iridescenceThicknessMap.toJSON(e).uuid),void 0!==this.anisotropy&&(r.anisotropy=this.anisotropy),void 0!==this.anisotropyRotation&&(r.anisotropyRotation=this.anisotropyRotation),this.anisotropyMap&&this.anisotropyMap.isTexture&&(r.anisotropyMap=this.anisotropyMap.toJSON(e).uuid),this.map&&this.map.isTexture&&(r.map=this.map.toJSON(e).uuid),this.matcap&&this.matcap.isTexture&&(r.matcap=this.matcap.toJSON(e).uuid),this.alphaMap&&this.alphaMap.isTexture&&(r.alphaMap=this.alphaMap.toJSON(e).uuid),this.lightMap&&this.lightMap.isTexture&&(r.lightMap=this.lightMap.toJSON(e).uuid,r.lightMapIntensity=this.lightMapIntensity),this.aoMap&&this.aoMap.isTexture&&(r.aoMap=this.aoMap.toJSON(e).uuid,r.aoMapIntensity=this.aoMapIntensity),this.bumpMap&&this.bumpMap.isTexture&&(r.bumpMap=this.bumpMap.toJSON(e).uuid,r.bumpScale=this.bumpScale),this.normalMap&&this.normalMap.isTexture&&(r.normalMap=this.normalMap.toJSON(e).uuid,r.normalMapType=this.normalMapType,r.normalScale=this.normalScale.toArray()),this.displacementMap&&this.displacementMap.isTexture&&(r.displacementMap=this.displacementMap.toJSON(e).uuid,r.displacementScale=this.displacementScale,r.displacementBias=this.displacementBias),this.roughnessMap&&this.roughnessMap.isTexture&&(r.roughnessMap=this.roughnessMap.toJSON(e).uuid),this.metalnessMap&&this.metalnessMap.isTexture&&(r.metalnessMap=this.metalnessMap.toJSON(e).uuid),this.emissiveMap&&this.emissiveMap.isTexture&&(r.emissiveMap=this.emissiveMap.toJSON(e).uuid),this.specularMap&&this.specularMap.isTexture&&(r.specularMap=this.specularMap.toJSON(e).uuid),this.specularIntensityMap&&this.specularIntensityMap.isTexture&&(r.specularIntensityMap=this.specularIntensityMap.toJSON(e).uuid),this.specularColorMap&&this.specularColorMap.isTexture&&(r.specularColorMap=this.specularColorMap.toJSON(e).uuid),this.envMap&&this.envMap.isTexture&&(r.envMap=this.envMap.toJSON(e).uuid,void 0!==this.combine&&(r.combine=this.combine)),void 0!==this.envMapRotation&&(r.envMapRotation=this.envMapRotation.toArray()),void 0!==this.envMapIntensity&&(r.envMapIntensity=this.envMapIntensity),void 0!==this.reflectivity&&(r.reflectivity=this.reflectivity),void 0!==this.refractionRatio&&(r.refractionRatio=this.refractionRatio),this.gradientMap&&this.gradientMap.isTexture&&(r.gradientMap=this.gradientMap.toJSON(e).uuid),void 0!==this.transmission&&(r.transmission=this.transmission),this.transmissionMap&&this.transmissionMap.isTexture&&(r.transmissionMap=this.transmissionMap.toJSON(e).uuid),void 0!==this.thickness&&(r.thickness=this.thickness),this.thicknessMap&&this.thicknessMap.isTexture&&(r.thicknessMap=this.thicknessMap.toJSON(e).uuid),void 0!==this.attenuationDistance&&this.attenuationDistance!==1/0&&(r.attenuationDistance=this.attenuationDistance),void 0!==this.attenuationColor&&(r.attenuationColor=this.attenuationColor.getHex()),void 0!==this.size&&(r.size=this.size),null!==this.shadowSide&&(r.shadowSide=this.shadowSide),void 0!==this.sizeAttenuation&&(r.sizeAttenuation=this.sizeAttenuation),1!==this.blending&&(r.blending=this.blending),0!==this.side&&(r.side=this.side),!0===this.vertexColors&&(r.vertexColors=!0),this.opacity<1&&(r.opacity=this.opacity),!0===this.transparent&&(r.transparent=!0),204!==this.blendSrc&&(r.blendSrc=this.blendSrc),205!==this.blendDst&&(r.blendDst=this.blendDst),100!==this.blendEquation&&(r.blendEquation=this.blendEquation),null!==this.blendSrcAlpha&&(r.blendSrcAlpha=this.blendSrcAlpha),null!==this.blendDstAlpha&&(r.blendDstAlpha=this.blendDstAlpha),null!==this.blendEquationAlpha&&(r.blendEquationAlpha=this.blendEquationAlpha),this.blendColor&&this.blendColor.isColor&&(r.blendColor=this.blendColor.getHex()),0!==this.blendAlpha&&(r.blendAlpha=this.blendAlpha),3!==this.depthFunc&&(r.depthFunc=this.depthFunc),!1===this.depthTest&&(r.depthTest=this.depthTest),!1===this.depthWrite&&(r.depthWrite=this.depthWrite),!1===this.colorWrite&&(r.colorWrite=this.colorWrite),255!==this.stencilWriteMask&&(r.stencilWriteMask=this.stencilWriteMask),519!==this.stencilFunc&&(r.stencilFunc=this.stencilFunc),0!==this.stencilRef&&(r.stencilRef=this.stencilRef),255!==this.stencilFuncMask&&(r.stencilFuncMask=this.stencilFuncMask),7680!==this.stencilFail&&(r.stencilFail=this.stencilFail),7680!==this.stencilZFail&&(r.stencilZFail=this.stencilZFail),7680!==this.stencilZPass&&(r.stencilZPass=this.stencilZPass),!0===this.stencilWrite&&(r.stencilWrite=this.stencilWrite),void 0!==this.rotation&&0!==this.rotation&&(r.rotation=this.rotation),!0===this.polygonOffset&&(r.polygonOffset=!0),0!==this.polygonOffsetFactor&&(r.polygonOffsetFactor=this.polygonOffsetFactor),0!==this.polygonOffsetUnits&&(r.polygonOffsetUnits=this.polygonOffsetUnits),void 0!==this.linewidth&&1!==this.linewidth&&(r.linewidth=this.linewidth),void 0!==this.dashSize&&(r.dashSize=this.dashSize),void 0!==this.gapSize&&(r.gapSize=this.gapSize),void 0!==this.scale&&(r.scale=this.scale),!0===this.dithering&&(r.dithering=!0),this.alphaTest>0&&(r.alphaTest=this.alphaTest),!0===this.alphaHash&&(r.alphaHash=!0),!0===this.alphaToCoverage&&(r.alphaToCoverage=!0),!0===this.premultipliedAlpha&&(r.premultipliedAlpha=!0),!0===this.forceSinglePass&&(r.forceSinglePass=!0),!0===this.wireframe&&(r.wireframe=!0),this.wireframeLinewidth>1&&(r.wireframeLinewidth=this.wireframeLinewidth),"round"!==this.wireframeLinecap&&(r.wireframeLinecap=this.wireframeLinecap),"round"!==this.wireframeLinejoin&&(r.wireframeLinejoin=this.wireframeLinejoin),!0===this.flatShading&&(r.flatShading=!0),!1===this.visible&&(r.visible=!1),!1===this.toneMapped&&(r.toneMapped=!1),!1===this.fog&&(r.fog=!1),Object.keys(this.userData).length>0&&(r.userData=this.userData),t){let t=n(e.textures),i=n(e.images);t.length>0&&(r.textures=t),i.length>0&&(r.images=i)}return r}clone(){return new this.constructor().copy(this)}copy(e){this.name=e.name,this.blending=e.blending,this.side=e.side,this.vertexColors=e.vertexColors,this.opacity=e.opacity,this.transparent=e.transparent,this.blendSrc=e.blendSrc,this.blendDst=e.blendDst,this.blendEquation=e.blendEquation,this.blendSrcAlpha=e.blendSrcAlpha,this.blendDstAlpha=e.blendDstAlpha,this.blendEquationAlpha=e.blendEquationAlpha,this.blendColor.copy(e.blendColor),this.blendAlpha=e.blendAlpha,this.depthFunc=e.depthFunc,this.depthTest=e.depthTest,this.depthWrite=e.depthWrite,this.stencilWriteMask=e.stencilWriteMask,this.stencilFunc=e.stencilFunc,this.stencilRef=e.stencilRef,this.stencilFuncMask=e.stencilFuncMask,this.stencilFail=e.stencilFail,this.stencilZFail=e.stencilZFail,this.stencilZPass=e.stencilZPass,this.stencilWrite=e.stencilWrite;let t=e.clippingPlanes,r=null;if(null!==t){let e=t.length;r=Array(e);for(let n=0;n!==e;++n)r[n]=t[n].clone()}return this.clippingPlanes=r,this.clipIntersection=e.clipIntersection,this.clipShadows=e.clipShadows,this.shadowSide=e.shadowSide,this.colorWrite=e.colorWrite,this.precision=e.precision,this.polygonOffset=e.polygonOffset,this.polygonOffsetFactor=e.polygonOffsetFactor,this.polygonOffsetUnits=e.polygonOffsetUnits,this.dithering=e.dithering,this.alphaTest=e.alphaTest,this.alphaHash=e.alphaHash,this.alphaToCoverage=e.alphaToCoverage,this.premultipliedAlpha=e.premultipliedAlpha,this.forceSinglePass=e.forceSinglePass,this.visible=e.visible,this.toneMapped=e.toneMapped,this.userData=JSON.parse(JSON.stringify(e.userData)),this}dispose(){this.dispatchEvent({type:"dispose"})}set needsUpdate(e){!0===e&&this.version++}}let to=new C,tl=new z,tu=0;class th{constructor(e,t,r=!1){if(Array.isArray(e))throw TypeError("THREE.BufferAttribute: array should be a Typed Array.");this.isBufferAttribute=!0,Object.defineProperty(this,"id",{value:tu++}),this.name="",this.array=e,this.itemSize=t,this.count=void 0!==e?e.length/t:0,this.normalized=r,this.usage=35044,this.updateRanges=[],this.gpuType=1015,this.version=0}onUploadCallback(){}set needsUpdate(e){!0===e&&this.version++}setUsage(e){return this.usage=e,this}addUpdateRange(e,t){this.updateRanges.push({start:e,count:t})}clearUpdateRanges(){this.updateRanges.length=0}copy(e){return this.name=e.name,this.array=new e.array.constructor(e.array),this.itemSize=e.itemSize,this.count=e.count,this.normalized=e.normalized,this.usage=e.usage,this.gpuType=e.gpuType,this}copyAt(e,t,r){e*=this.itemSize,r*=t.itemSize;for(let n=0,i=this.itemSize;n<i;n++)this.array[e+n]=t.array[r+n];return this}copyArray(e){return this.array.set(e),this}applyMatrix3(e){if(2===this.itemSize)for(let t=0,r=this.count;t<r;t++)tl.fromBufferAttribute(this,t),tl.applyMatrix3(e),this.setXY(t,tl.x,tl.y);else if(3===this.itemSize)for(let t=0,r=this.count;t<r;t++)to.fromBufferAttribute(this,t),to.applyMatrix3(e),this.setXYZ(t,to.x,to.y,to.z);return this}applyMatrix4(e){for(let t=0,r=this.count;t<r;t++)to.fromBufferAttribute(this,t),to.applyMatrix4(e),this.setXYZ(t,to.x,to.y,to.z);return this}applyNormalMatrix(e){for(let t=0,r=this.count;t<r;t++)to.fromBufferAttribute(this,t),to.applyNormalMatrix(e),this.setXYZ(t,to.x,to.y,to.z);return this}transformDirection(e){for(let t=0,r=this.count;t<r;t++)to.fromBufferAttribute(this,t),to.transformDirection(e),this.setXYZ(t,to.x,to.y,to.z);return this}set(e,t=0){return this.array.set(e,t),this}getComponent(e,t){let r=this.array[e*this.itemSize+t];return this.normalized&&(r=M(r,this.array)),r}setComponent(e,t,r){return this.normalized&&(r=_(r,this.array)),this.array[e*this.itemSize+t]=r,this}getX(e){let t=this.array[e*this.itemSize];return this.normalized&&(t=M(t,this.array)),t}setX(e,t){return this.normalized&&(t=_(t,this.array)),this.array[e*this.itemSize]=t,this}getY(e){let t=this.array[e*this.itemSize+1];return this.normalized&&(t=M(t,this.array)),t}setY(e,t){return this.normalized&&(t=_(t,this.array)),this.array[e*this.itemSize+1]=t,this}getZ(e){let t=this.array[e*this.itemSize+2];return this.normalized&&(t=M(t,this.array)),t}setZ(e,t){return this.normalized&&(t=_(t,this.array)),this.array[e*this.itemSize+2]=t,this}getW(e){let t=this.array[e*this.itemSize+3];return this.normalized&&(t=M(t,this.array)),t}setW(e,t){return this.normalized&&(t=_(t,this.array)),this.array[e*this.itemSize+3]=t,this}setXY(e,t,r){return e*=this.itemSize,this.normalized&&(t=_(t,this.array),r=_(r,this.array)),this.array[e+0]=t,this.array[e+1]=r,this}setXYZ(e,t,r,n){return e*=this.itemSize,this.normalized&&(t=_(t,this.array),r=_(r,this.array),n=_(n,this.array)),this.array[e+0]=t,this.array[e+1]=r,this.array[e+2]=n,this}setXYZW(e,t,r,n,i){return e*=this.itemSize,this.normalized&&(t=_(t,this.array),r=_(r,this.array),n=_(n,this.array),i=_(i,this.array)),this.array[e+0]=t,this.array[e+1]=r,this.array[e+2]=n,this.array[e+3]=i,this}onUpload(e){return this.onUploadCallback=e,this}clone(){return new this.constructor(this.array,this.itemSize).copy(this)}toJSON(){let e={itemSize:this.itemSize,type:this.array.constructor.name,array:Array.from(this.array),normalized:this.normalized};return""!==this.name&&(e.name=this.name),35044!==this.usage&&(e.usage=this.usage),e}}class tc extends th{constructor(e,t,r){super(new Uint16Array(e),t,r)}}class td extends th{constructor(e,t,r){super(new Uint32Array(e),t,r)}}class tf extends th{constructor(e,t,r){super(new Float32Array(e),t,r)}}let tp=0,tm=new ew,ty=new eY,tg=new C,tx=new Y,tb=new Y,tv=new C;class tw extends g{constructor(){super(),this.isBufferGeometry=!0,Object.defineProperty(this,"id",{value:tp++}),this.uuid=w(),this.name="",this.type="BufferGeometry",this.index=null,this.indirect=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null,this.drawRange={start:0,count:1/0},this.userData={}}getIndex(){return this.index}setIndex(e){return Array.isArray(e)?this.index=new(!function(e){for(let t=e.length-1;t>=0;--t)if(e[t]>=65535)return!0;return!1}(e)?tc:td)(e,1):this.index=e,this}setIndirect(e){return this.indirect=e,this}getIndirect(){return this.indirect}getAttribute(e){return this.attributes[e]}setAttribute(e,t){return this.attributes[e]=t,this}deleteAttribute(e){return delete this.attributes[e],this}hasAttribute(e){return void 0!==this.attributes[e]}addGroup(e,t,r=0){this.groups.push({start:e,count:t,materialIndex:r})}clearGroups(){this.groups=[]}setDrawRange(e,t){this.drawRange.start=e,this.drawRange.count=t}applyMatrix4(e){let t=this.attributes.position;void 0!==t&&(t.applyMatrix4(e),t.needsUpdate=!0);let r=this.attributes.normal;if(void 0!==r){let t=new A().getNormalMatrix(e);r.applyNormalMatrix(t),r.needsUpdate=!0}let n=this.attributes.tangent;return void 0!==n&&(n.transformDirection(e),n.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this}applyQuaternion(e){return tm.makeRotationFromQuaternion(e),this.applyMatrix4(tm),this}rotateX(e){return tm.makeRotationX(e),this.applyMatrix4(tm),this}rotateY(e){return tm.makeRotationY(e),this.applyMatrix4(tm),this}rotateZ(e){return tm.makeRotationZ(e),this.applyMatrix4(tm),this}translate(e,t,r){return tm.makeTranslation(e,t,r),this.applyMatrix4(tm),this}scale(e,t,r){return tm.makeScale(e,t,r),this.applyMatrix4(tm),this}lookAt(e){return ty.lookAt(e),ty.updateMatrix(),this.applyMatrix4(ty.matrix),this}center(){return this.computeBoundingBox(),this.boundingBox.getCenter(tg).negate(),this.translate(tg.x,tg.y,tg.z),this}setFromPoints(e){let t=this.getAttribute("position");if(void 0===t){let t=[];for(let r=0,n=e.length;r<n;r++){let n=e[r];t.push(n.x,n.y,n.z||0)}this.setAttribute("position",new tf(t,3))}else{let r=Math.min(e.length,t.count);for(let n=0;n<r;n++){let r=e[n];t.setXYZ(n,r.x,r.y,r.z||0)}e.length>t.count&&console.warn("THREE.BufferGeometry: Buffer size too small for points data. Use .dispose() and create a new geometry."),t.needsUpdate=!0}return this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new Y);let e=this.attributes.position,t=this.morphAttributes.position;if(e&&e.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box.",this),this.boundingBox.set(new C(-1/0,-1/0,-1/0),new C(Infinity,Infinity,Infinity));return}if(void 0!==e){if(this.boundingBox.setFromBufferAttribute(e),t)for(let e=0,r=t.length;e<r;e++){let r=t[e];tx.setFromBufferAttribute(r),this.morphTargetsRelative?(tv.addVectors(this.boundingBox.min,tx.min),this.boundingBox.expandByPoint(tv),tv.addVectors(this.boundingBox.max,tx.max),this.boundingBox.expandByPoint(tv)):(this.boundingBox.expandByPoint(tx.min),this.boundingBox.expandByPoint(tx.max))}}else this.boundingBox.makeEmpty();(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&console.error('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.',this)}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new ed);let e=this.attributes.position,t=this.morphAttributes.position;if(e&&e.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere.",this),this.boundingSphere.set(new C,1/0);return}if(e){let r=this.boundingSphere.center;if(tx.setFromBufferAttribute(e),t)for(let e=0,r=t.length;e<r;e++){let r=t[e];tb.setFromBufferAttribute(r),this.morphTargetsRelative?(tv.addVectors(tx.min,tb.min),tx.expandByPoint(tv),tv.addVectors(tx.max,tb.max),tx.expandByPoint(tv)):(tx.expandByPoint(tb.min),tx.expandByPoint(tb.max))}tx.getCenter(r);let n=0;for(let t=0,i=e.count;t<i;t++)tv.fromBufferAttribute(e,t),n=Math.max(n,r.distanceToSquared(tv));if(t)for(let i=0,s=t.length;i<s;i++){let s=t[i],a=this.morphTargetsRelative;for(let t=0,i=s.count;t<i;t++)tv.fromBufferAttribute(s,t),a&&(tg.fromBufferAttribute(e,t),tv.add(tg)),n=Math.max(n,r.distanceToSquared(tv))}this.boundingSphere.radius=Math.sqrt(n),isNaN(this.boundingSphere.radius)&&console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.',this)}}computeTangents(){let e=this.index,t=this.attributes;if(null===e||void 0===t.position||void 0===t.normal||void 0===t.uv)return void console.error("THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)");let r=t.position,n=t.normal,i=t.uv;!1===this.hasAttribute("tangent")&&this.setAttribute("tangent",new th(new Float32Array(4*r.count),4));let s=this.getAttribute("tangent"),a=[],o=[];for(let e=0;e<r.count;e++)a[e]=new C,o[e]=new C;let l=new C,u=new C,h=new C,c=new z,d=new z,f=new z,p=new C,m=new C,y=this.groups;0===y.length&&(y=[{start:0,count:e.count}]);for(let t=0,n=y.length;t<n;++t){let n=y[t],s=n.start,g=n.count;for(let t=s,n=s+g;t<n;t+=3)!function(e,t,n){l.fromBufferAttribute(r,e),u.fromBufferAttribute(r,t),h.fromBufferAttribute(r,n),c.fromBufferAttribute(i,e),d.fromBufferAttribute(i,t),f.fromBufferAttribute(i,n),u.sub(l),h.sub(l),d.sub(c),f.sub(c);let s=1/(d.x*f.y-f.x*d.y);isFinite(s)&&(p.copy(u).multiplyScalar(f.y).addScaledVector(h,-d.y).multiplyScalar(s),m.copy(h).multiplyScalar(d.x).addScaledVector(u,-f.x).multiplyScalar(s),a[e].add(p),a[t].add(p),a[n].add(p),o[e].add(m),o[t].add(m),o[n].add(m))}(e.getX(t+0),e.getX(t+1),e.getX(t+2))}let g=new C,x=new C,b=new C,v=new C;function w(e){b.fromBufferAttribute(n,e),v.copy(b);let t=a[e];g.copy(t),g.sub(b.multiplyScalar(b.dot(t))).normalize(),x.crossVectors(v,t);let r=x.dot(o[e]);s.setXYZW(e,g.x,g.y,g.z,r<0?-1:1)}for(let t=0,r=y.length;t<r;++t){let r=y[t],n=r.start,i=r.count;for(let t=n,r=n+i;t<r;t+=3)w(e.getX(t+0)),w(e.getX(t+1)),w(e.getX(t+2))}}computeVertexNormals(){let e=this.index,t=this.getAttribute("position");if(void 0!==t){let r=this.getAttribute("normal");if(void 0===r)r=new th(new Float32Array(3*t.count),3),this.setAttribute("normal",r);else for(let e=0,t=r.count;e<t;e++)r.setXYZ(e,0,0,0);let n=new C,i=new C,s=new C,a=new C,o=new C,l=new C,u=new C,h=new C;if(e)for(let c=0,d=e.count;c<d;c+=3){let d=e.getX(c+0),f=e.getX(c+1),p=e.getX(c+2);n.fromBufferAttribute(t,d),i.fromBufferAttribute(t,f),s.fromBufferAttribute(t,p),u.subVectors(s,i),h.subVectors(n,i),u.cross(h),a.fromBufferAttribute(r,d),o.fromBufferAttribute(r,f),l.fromBufferAttribute(r,p),a.add(u),o.add(u),l.add(u),r.setXYZ(d,a.x,a.y,a.z),r.setXYZ(f,o.x,o.y,o.z),r.setXYZ(p,l.x,l.y,l.z)}else for(let e=0,a=t.count;e<a;e+=3)n.fromBufferAttribute(t,e+0),i.fromBufferAttribute(t,e+1),s.fromBufferAttribute(t,e+2),u.subVectors(s,i),h.subVectors(n,i),u.cross(h),r.setXYZ(e+0,u.x,u.y,u.z),r.setXYZ(e+1,u.x,u.y,u.z),r.setXYZ(e+2,u.x,u.y,u.z);this.normalizeNormals(),r.needsUpdate=!0}}normalizeNormals(){let e=this.attributes.normal;for(let t=0,r=e.count;t<r;t++)tv.fromBufferAttribute(e,t),tv.normalize(),e.setXYZ(t,tv.x,tv.y,tv.z)}toNonIndexed(){function e(e,t){let r=e.array,n=e.itemSize,i=e.normalized,s=new r.constructor(t.length*n),a=0,o=0;for(let i=0,l=t.length;i<l;i++){a=e.isInterleavedBufferAttribute?t[i]*e.data.stride+e.offset:t[i]*n;for(let e=0;e<n;e++)s[o++]=r[a++]}return new th(s,n,i)}if(null===this.index)return console.warn("THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."),this;let t=new tw,r=this.index.array,n=this.attributes;for(let i in n){let s=e(n[i],r);t.setAttribute(i,s)}let i=this.morphAttributes;for(let n in i){let s=[],a=i[n];for(let t=0,n=a.length;t<n;t++){let n=e(a[t],r);s.push(n)}t.morphAttributes[n]=s}t.morphTargetsRelative=this.morphTargetsRelative;let s=this.groups;for(let e=0,r=s.length;e<r;e++){let r=s[e];t.addGroup(r.start,r.count,r.materialIndex)}return t}toJSON(){let e={metadata:{version:4.7,type:"BufferGeometry",generator:"BufferGeometry.toJSON"}};if(e.uuid=this.uuid,e.type=this.type,""!==this.name&&(e.name=this.name),Object.keys(this.userData).length>0&&(e.userData=this.userData),void 0!==this.parameters){let t=this.parameters;for(let r in t)void 0!==t[r]&&(e[r]=t[r]);return e}e.data={attributes:{}};let t=this.index;null!==t&&(e.data.index={type:t.array.constructor.name,array:Array.prototype.slice.call(t.array)});let r=this.attributes;for(let t in r){let n=r[t];e.data.attributes[t]=n.toJSON(e.data)}let n={},i=!1;for(let t in this.morphAttributes){let r=this.morphAttributes[t],s=[];for(let t=0,n=r.length;t<n;t++){let n=r[t];s.push(n.toJSON(e.data))}s.length>0&&(n[t]=s,i=!0)}i&&(e.data.morphAttributes=n,e.data.morphTargetsRelative=this.morphTargetsRelative);let s=this.groups;s.length>0&&(e.data.groups=JSON.parse(JSON.stringify(s)));let a=this.boundingSphere;return null!==a&&(e.data.boundingSphere=a.toJSON()),e}clone(){return new this.constructor().copy(this)}copy(e){this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingBox=null,this.boundingSphere=null;let t={};this.name=e.name;let r=e.index;null!==r&&this.setIndex(r.clone());let n=e.attributes;for(let e in n){let r=n[e];this.setAttribute(e,r.clone(t))}let i=e.morphAttributes;for(let e in i){let r=[],n=i[e];for(let e=0,i=n.length;e<i;e++)r.push(n[e].clone(t));this.morphAttributes[e]=r}this.morphTargetsRelative=e.morphTargetsRelative;let s=e.groups;for(let e=0,t=s.length;e<t;e++){let t=s[e];this.addGroup(t.start,t.count,t.materialIndex)}let a=e.boundingBox;null!==a&&(this.boundingBox=a.clone());let o=e.boundingSphere;return null!==o&&(this.boundingSphere=o.clone()),this.drawRange.start=e.drawRange.start,this.drawRange.count=e.drawRange.count,this.userData=e.userData,this}dispose(){this.dispatchEvent({type:"dispose"})}}let tS=new C,tM=new C,t_=new C,tz=new C,tk=new C;class tC extends tw{constructor(e=1,t=1,r=1,n=1,i=1,s=1){super(),this.type="BoxGeometry",this.parameters={width:e,height:t,depth:r,widthSegments:n,heightSegments:i,depthSegments:s};let a=this;n=Math.floor(n),i=Math.floor(i);let o=[],l=[],u=[],h=[],c=0,d=0;function f(e,t,r,n,i,s,f,p,m,y,g){let x=s/m,b=f/y,v=s/2,w=f/2,S=p/2,M=m+1,_=y+1,z=0,k=0,P=new C;for(let s=0;s<_;s++){let a=s*b-w;for(let o=0;o<M;o++){let c=o*x-v;P[e]=c*n,P[t]=a*i,P[r]=S,l.push(P.x,P.y,P.z),P[e]=0,P[t]=0,P[r]=p>0?1:-1,u.push(P.x,P.y,P.z),h.push(o/m),h.push(1-s/y),z+=1}}for(let e=0;e<y;e++)for(let t=0;t<m;t++){let r=c+t+M*e,n=c+t+M*(e+1),i=c+(t+1)+M*(e+1),s=c+(t+1)+M*e;o.push(r,n,s),o.push(n,i,s),k+=6}a.addGroup(d,k,g),d+=k,c+=z}f("z","y","x",-1,-1,r,t,e,s=Math.floor(s),i,0),f("z","y","x",1,-1,r,t,-e,s,i,1),f("x","z","y",1,1,e,r,t,n,s,2),f("x","z","y",1,-1,e,r,-t,n,s,3),f("x","y","z",1,-1,e,t,r,n,i,4),f("x","y","z",-1,-1,e,t,-r,n,i,5),this.setIndex(o),this.setAttribute("position",new tf(l,3)),this.setAttribute("normal",new tf(u,3)),this.setAttribute("uv",new tf(h,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new tC(e.width,e.height,e.depth,e.widthSegments,e.heightSegments,e.depthSegments)}}class tP extends eY{constructor(){super(),this.isCamera=!0,this.type="Camera",this.matrixWorldInverse=new ew,this.projectionMatrix=new ew,this.projectionMatrixInverse=new ew,this.coordinateSystem=2e3}copy(e,t){return super.copy(e,t),this.matrixWorldInverse.copy(e.matrixWorldInverse),this.projectionMatrix.copy(e.projectionMatrix),this.projectionMatrixInverse.copy(e.projectionMatrixInverse),this.coordinateSystem=e.coordinateSystem,this}getWorldDirection(e){return super.getWorldDirection(e).negate()}updateMatrixWorld(e){super.updateMatrixWorld(e),this.matrixWorldInverse.copy(this.matrixWorld).invert()}updateWorldMatrix(e,t){super.updateWorldMatrix(e,t),this.matrixWorldInverse.copy(this.matrixWorld).invert()}clone(){return new this.constructor().copy(this)}}let tE=new C,tA=new z,tN=new z;class tT extends tP{constructor(e=50,t=1,r=.1,n=2e3){super(),this.isPerspectiveCamera=!0,this.type="PerspectiveCamera",this.fov=e,this.zoom=1,this.near=r,this.far=n,this.focus=10,this.aspect=t,this.view=null,this.filmGauge=35,this.filmOffset=0,this.updateProjectionMatrix()}copy(e,t){return super.copy(e,t),this.fov=e.fov,this.zoom=e.zoom,this.near=e.near,this.far=e.far,this.focus=e.focus,this.aspect=e.aspect,this.view=null===e.view?null:Object.assign({},e.view),this.filmGauge=e.filmGauge,this.filmOffset=e.filmOffset,this}setFocalLength(e){let t=.5*this.getFilmHeight()/e;this.fov=2*v*Math.atan(t),this.updateProjectionMatrix()}getFocalLength(){let e=Math.tan(.5*b*this.fov);return .5*this.getFilmHeight()/e}getEffectiveFOV(){return 2*v*Math.atan(Math.tan(.5*b*this.fov)/this.zoom)}getFilmWidth(){return this.filmGauge*Math.min(this.aspect,1)}getFilmHeight(){return this.filmGauge/Math.max(this.aspect,1)}getViewBounds(e,t,r){tE.set(-1,-1,.5).applyMatrix4(this.projectionMatrixInverse),t.set(tE.x,tE.y).multiplyScalar(-e/tE.z),tE.set(1,1,.5).applyMatrix4(this.projectionMatrixInverse),r.set(tE.x,tE.y).multiplyScalar(-e/tE.z)}getViewSize(e,t){return this.getViewBounds(e,tA,tN),t.subVectors(tN,tA)}setViewOffset(e,t,r,n,i,s){this.aspect=e/t,null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=e,this.view.fullHeight=t,this.view.offsetX=r,this.view.offsetY=n,this.view.width=i,this.view.height=s,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let e=this.near,t=e*Math.tan(.5*b*this.fov)/this.zoom,r=2*t,n=this.aspect*r,i=-.5*n,s=this.view;if(null!==this.view&&this.view.enabled){let e=s.fullWidth,a=s.fullHeight;i+=s.offsetX*n/e,t-=s.offsetY*r/a,n*=s.width/e,r*=s.height/a}let a=this.filmOffset;0!==a&&(i+=e*a/this.getFilmWidth()),this.projectionMatrix.makePerspective(i,i+n,t,t-r,e,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(e){let t=super.toJSON(e);return t.object.fov=this.fov,t.object.zoom=this.zoom,t.object.near=this.near,t.object.far=this.far,t.object.focus=this.focus,t.object.aspect=this.aspect,null!==this.view&&(t.object.view=Object.assign({},this.view)),t.object.filmGauge=this.filmGauge,t.object.filmOffset=this.filmOffset,t}}let tj=new C,tO=new C,tR=new A;class tI{constructor(e=new C(1,0,0),t=0){this.isPlane=!0,this.normal=e,this.constant=t}set(e,t){return this.normal.copy(e),this.constant=t,this}setComponents(e,t,r,n){return this.normal.set(e,t,r),this.constant=n,this}setFromNormalAndCoplanarPoint(e,t){return this.normal.copy(e),this.constant=-t.dot(this.normal),this}setFromCoplanarPoints(e,t,r){let n=tj.subVectors(r,t).cross(tO.subVectors(e,t)).normalize();return this.setFromNormalAndCoplanarPoint(n,e),this}copy(e){return this.normal.copy(e.normal),this.constant=e.constant,this}normalize(){let e=1/this.normal.length();return this.normal.multiplyScalar(e),this.constant*=e,this}negate(){return this.constant*=-1,this.normal.negate(),this}distanceToPoint(e){return this.normal.dot(e)+this.constant}distanceToSphere(e){return this.distanceToPoint(e.center)-e.radius}projectPoint(e,t){return t.copy(e).addScaledVector(this.normal,-this.distanceToPoint(e))}intersectLine(e,t){let r=e.delta(tj),n=this.normal.dot(r);if(0===n)return 0===this.distanceToPoint(e.start)?t.copy(e.start):null;let i=-(e.start.dot(this.normal)+this.constant)/n;return i<0||i>1?null:t.copy(e.start).addScaledVector(r,i)}intersectsLine(e){let t=this.distanceToPoint(e.start),r=this.distanceToPoint(e.end);return t<0&&r>0||r<0&&t>0}intersectsBox(e){return e.intersectsPlane(this)}intersectsSphere(e){return e.intersectsPlane(this)}coplanarPoint(e){return e.copy(this.normal).multiplyScalar(-this.constant)}applyMatrix4(e,t){let r=t||tR.getNormalMatrix(e),n=this.coplanarPoint(tj).applyMatrix4(e),i=this.normal.applyMatrix3(r).normalize();return this.constant=-n.dot(i),this}translate(e){return this.constant-=e.dot(this.normal),this}equals(e){return e.normal.equals(this.normal)&&e.constant===this.constant}clone(){return new this.constructor().copy(this)}}let tL=new ed,tF=new C;class tV{constructor(e=new tI,t=new tI,r=new tI,n=new tI,i=new tI,s=new tI){this.planes=[e,t,r,n,i,s]}set(e,t,r,n,i,s){let a=this.planes;return a[0].copy(e),a[1].copy(t),a[2].copy(r),a[3].copy(n),a[4].copy(i),a[5].copy(s),this}copy(e){let t=this.planes;for(let r=0;r<6;r++)t[r].copy(e.planes[r]);return this}setFromProjectionMatrix(e,t=2e3){let r=this.planes,n=e.elements,i=n[0],s=n[1],a=n[2],o=n[3],l=n[4],u=n[5],h=n[6],c=n[7],d=n[8],f=n[9],p=n[10],m=n[11],y=n[12],g=n[13],x=n[14],b=n[15];if(r[0].setComponents(o-i,c-l,m-d,b-y).normalize(),r[1].setComponents(o+i,c+l,m+d,b+y).normalize(),r[2].setComponents(o+s,c+u,m+f,b+g).normalize(),r[3].setComponents(o-s,c-u,m-f,b-g).normalize(),r[4].setComponents(o-a,c-h,m-p,b-x).normalize(),2e3===t)r[5].setComponents(o+a,c+h,m+p,b+x).normalize();else if(2001===t)r[5].setComponents(a,h,p,x).normalize();else throw Error("THREE.Frustum.setFromProjectionMatrix(): Invalid coordinate system: "+t);return this}intersectsObject(e){if(void 0!==e.boundingSphere)null===e.boundingSphere&&e.computeBoundingSphere(),tL.copy(e.boundingSphere).applyMatrix4(e.matrixWorld);else{let t=e.geometry;null===t.boundingSphere&&t.computeBoundingSphere(),tL.copy(t.boundingSphere).applyMatrix4(e.matrixWorld)}return this.intersectsSphere(tL)}intersectsSprite(e){return tL.center.set(0,0,0),tL.radius=.*********1865476,tL.applyMatrix4(e.matrixWorld),this.intersectsSphere(tL)}intersectsSphere(e){let t=this.planes,r=e.center,n=-e.radius;for(let e=0;e<6;e++)if(t[e].distanceToPoint(r)<n)return!1;return!0}intersectsBox(e){let t=this.planes;for(let r=0;r<6;r++){let n=t[r];if(tF.x=n.normal.x>0?e.max.x:e.min.x,tF.y=n.normal.y>0?e.max.y:e.min.y,tF.z=n.normal.z>0?e.max.z:e.min.z,0>n.distanceToPoint(tF))return!1}return!0}containsPoint(e){let t=this.planes;for(let r=0;r<6;r++)if(0>t[r].distanceToPoint(e))return!1;return!0}clone(){return new this.constructor().copy(this)}}let tB=new ew,tD=new tV;class tU{constructor(){this.coordinateSystem=2e3}intersectsObject(e,t){if(!t.isArrayCamera||0===t.cameras.length)return!1;for(let r=0;r<t.cameras.length;r++){let n=t.cameras[r];if(tB.multiplyMatrices(n.projectionMatrix,n.matrixWorldInverse),tD.setFromProjectionMatrix(tB,this.coordinateSystem),tD.intersectsObject(e))return!0}return!1}intersectsSprite(e,t){if(!t||!t.cameras||0===t.cameras.length)return!1;for(let r=0;r<t.cameras.length;r++){let n=t.cameras[r];if(tB.multiplyMatrices(n.projectionMatrix,n.matrixWorldInverse),tD.setFromProjectionMatrix(tB,this.coordinateSystem),tD.intersectsSprite(e))return!0}return!1}intersectsSphere(e,t){if(!t||!t.cameras||0===t.cameras.length)return!1;for(let r=0;r<t.cameras.length;r++){let n=t.cameras[r];if(tB.multiplyMatrices(n.projectionMatrix,n.matrixWorldInverse),tD.setFromProjectionMatrix(tB,this.coordinateSystem),tD.intersectsSphere(e))return!0}return!1}intersectsBox(e,t){if(!t||!t.cameras||0===t.cameras.length)return!1;for(let r=0;r<t.cameras.length;r++){let n=t.cameras[r];if(tB.multiplyMatrices(n.projectionMatrix,n.matrixWorldInverse),tD.setFromProjectionMatrix(tB,this.coordinateSystem),tD.intersectsBox(e))return!0}return!1}containsPoint(e,t){if(!t||!t.cameras||0===t.cameras.length)return!1;for(let r=0;r<t.cameras.length;r++){let n=t.cameras[r];if(tB.multiplyMatrices(n.projectionMatrix,n.matrixWorldInverse),tD.setFromProjectionMatrix(tB,this.coordinateSystem),tD.containsPoint(e))return!0}return!1}clone(){return new tU}}class tW extends tw{constructor(e=1,t=1,r=4,n=8,i=1){super(),this.type="CapsuleGeometry",this.parameters={radius:e,height:t,capSegments:r,radialSegments:n,heightSegments:i},t=Math.max(0,t),r=Math.max(1,Math.floor(r)),n=Math.max(3,Math.floor(n));let s=[],a=[],o=[],l=[],u=t/2,h=Math.PI/2*e,c=t,d=2*h+c,f=2*r+(i=Math.max(1,Math.floor(i))),p=n+1,m=new C,y=new C;for(let g=0;g<=f;g++){let x=0,b=0,v=0,w=0;if(g<=r){let t=g/r,n=t*Math.PI/2;b=-u-e*Math.cos(n),v=e*Math.sin(n),w=-e*Math.cos(n),x=t*h}else if(g<=r+i){let n=(g-r)/i;b=-u+n*t,v=e,w=0,x=h+n*c}else{let t=(g-r-i)/r,n=t*Math.PI/2;b=u+e*Math.sin(n),v=e*Math.cos(n),w=e*Math.sin(n),x=h+c+t*h}let S=Math.max(0,Math.min(1,x/d)),M=0;0===g?M=.5/n:g===f&&(M=-.5/n);for(let e=0;e<=n;e++){let t=e/n,r=t*Math.PI*2,i=Math.sin(r),s=Math.cos(r);y.x=-v*s,y.y=b,y.z=v*i,a.push(y.x,y.y,y.z),m.set(-v*s,w,v*i),m.normalize(),o.push(m.x,m.y,m.z),l.push(t+M,S)}if(g>0){let e=(g-1)*p;for(let t=0;t<n;t++){let r=e+t,n=e+t+1,i=g*p+t,a=g*p+t+1;s.push(r,n,i),s.push(n,a,i)}}}this.setIndex(s),this.setAttribute("position",new tf(a,3)),this.setAttribute("normal",new tf(o,3)),this.setAttribute("uv",new tf(l,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new tW(e.radius,e.height,e.capSegments,e.radialSegments,e.heightSegments)}}class tH extends tw{constructor(e=1,t=32,r=0,n=2*Math.PI){super(),this.type="CircleGeometry",this.parameters={radius:e,segments:t,thetaStart:r,thetaLength:n},t=Math.max(3,t);let i=[],s=[],a=[],o=[],l=new C,u=new z;s.push(0,0,0),a.push(0,0,1),o.push(.5,.5);for(let i=0,h=3;i<=t;i++,h+=3){let c=r+i/t*n;l.x=e*Math.cos(c),l.y=e*Math.sin(c),s.push(l.x,l.y,l.z),a.push(0,0,1),u.x=(s[h]/e+1)/2,u.y=(s[h+1]/e+1)/2,o.push(u.x,u.y)}for(let e=1;e<=t;e++)i.push(e,e+1,0);this.setIndex(i),this.setAttribute("position",new tf(s,3)),this.setAttribute("normal",new tf(a,3)),this.setAttribute("uv",new tf(o,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new tH(e.radius,e.segments,e.thetaStart,e.thetaLength)}}class tq extends tw{constructor(e=1,t=1,r=1,n=32,i=1,s=!1,a=0,o=2*Math.PI){super(),this.type="CylinderGeometry",this.parameters={radiusTop:e,radiusBottom:t,height:r,radialSegments:n,heightSegments:i,openEnded:s,thetaStart:a,thetaLength:o};let l=this;n=Math.floor(n),i=Math.floor(i);let u=[],h=[],c=[],d=[],f=0,p=[],m=r/2,y=0;function g(r){let i=f,s=new z,p=new C,g=0,x=!0===r?e:t,b=!0===r?1:-1;for(let e=1;e<=n;e++)h.push(0,m*b,0),c.push(0,b,0),d.push(.5,.5),f++;let v=f;for(let e=0;e<=n;e++){let t=e/n*o+a,r=Math.cos(t),i=Math.sin(t);p.x=x*i,p.y=m*b,p.z=x*r,h.push(p.x,p.y,p.z),c.push(0,b,0),s.x=.5*r+.5,s.y=.5*i*b+.5,d.push(s.x,s.y),f++}for(let e=0;e<n;e++){let t=i+e,n=v+e;!0===r?u.push(n,n+1,t):u.push(n+1,n,t),g+=3}l.addGroup(y,g,!0===r?1:2),y+=g}(function(){let s=new C,g=new C,x=0,b=(t-e)/r;for(let l=0;l<=i;l++){let u=[],y=l/i,x=y*(t-e)+e;for(let e=0;e<=n;e++){let t=e/n,i=t*o+a,l=Math.sin(i),p=Math.cos(i);g.x=x*l,g.y=-y*r+m,g.z=x*p,h.push(g.x,g.y,g.z),s.set(l,b,p).normalize(),c.push(s.x,s.y,s.z),d.push(t,1-y),u.push(f++)}p.push(u)}for(let r=0;r<n;r++)for(let n=0;n<i;n++){let s=p[n][r],a=p[n+1][r],o=p[n+1][r+1],l=p[n][r+1];(e>0||0!==n)&&(u.push(s,a,l),x+=3),(t>0||n!==i-1)&&(u.push(a,o,l),x+=3)}l.addGroup(y,x,0),y+=x})(),!1===s&&(e>0&&g(!0),t>0&&g(!1)),this.setIndex(u),this.setAttribute("position",new tf(h,3)),this.setAttribute("normal",new tf(c,3)),this.setAttribute("uv",new tf(d,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new tq(e.radiusTop,e.radiusBottom,e.height,e.radialSegments,e.heightSegments,e.openEnded,e.thetaStart,e.thetaLength)}}class tJ extends tq{constructor(e=1,t=1,r=32,n=1,i=!1,s=0,a=2*Math.PI){super(0,e,t,r,n,i,s,a),this.type="ConeGeometry",this.parameters={radius:e,height:t,radialSegments:r,heightSegments:n,openEnded:i,thetaStart:s,thetaLength:a}}static fromJSON(e){return new tJ(e.radius,e.height,e.radialSegments,e.heightSegments,e.openEnded,e.thetaStart,e.thetaLength)}}class tZ extends tw{constructor(e=[],t=[],r=1,n=0){super(),this.type="PolyhedronGeometry",this.parameters={vertices:e,indices:t,radius:r,detail:n};let i=[],s=[];function a(e){i.push(e.x,e.y,e.z)}function o(t,r){let n=3*t;r.x=e[n+0],r.y=e[n+1],r.z=e[n+2]}function l(e,t,r,n){n<0&&1===e.x&&(s[t]=e.x-1),0===r.x&&0===r.z&&(s[t]=n/2/Math.PI+.5)}function u(e){return Math.atan2(e.z,-e.x)}(function(e){let r=new C,n=new C,i=new C;for(let s=0;s<t.length;s+=3)o(t[s+0],r),o(t[s+1],n),o(t[s+2],i),function(e,t,r,n){let i=n+1,s=[];for(let n=0;n<=i;n++){s[n]=[];let a=e.clone().lerp(r,n/i),o=t.clone().lerp(r,n/i),l=i-n;for(let e=0;e<=l;e++)0===e&&n===i?s[n][e]=a:s[n][e]=a.clone().lerp(o,e/l)}for(let e=0;e<i;e++)for(let t=0;t<2*(i-e)-1;t++){let r=Math.floor(t/2);t%2==0?(a(s[e][r+1]),a(s[e+1][r]),a(s[e][r])):(a(s[e][r+1]),a(s[e+1][r+1]),a(s[e+1][r]))}}(r,n,i,e)})(n),function(e){let t=new C;for(let r=0;r<i.length;r+=3)t.x=i[r+0],t.y=i[r+1],t.z=i[r+2],t.normalize().multiplyScalar(e),i[r+0]=t.x,i[r+1]=t.y,i[r+2]=t.z}(r),function(){let e=new C;for(let r=0;r<i.length;r+=3){var t;e.x=i[r+0],e.y=i[r+1],e.z=i[r+2];let n=u(e)/2/Math.PI+.5,a=Math.atan2(-(t=e).y,Math.sqrt(t.x*t.x+t.z*t.z))/Math.PI+.5;s.push(n,1-a)}(function(){let e=new C,t=new C,r=new C,n=new C,a=new z,o=new z,h=new z;for(let c=0,d=0;c<i.length;c+=9,d+=6){e.set(i[c+0],i[c+1],i[c+2]),t.set(i[c+3],i[c+4],i[c+5]),r.set(i[c+6],i[c+7],i[c+8]),a.set(s[d+0],s[d+1]),o.set(s[d+2],s[d+3]),h.set(s[d+4],s[d+5]),n.copy(e).add(t).add(r).divideScalar(3);let f=u(n);l(a,d+0,e,f),l(o,d+2,t,f),l(h,d+4,r,f)}})(),function(){for(let e=0;e<s.length;e+=6){let t=s[e+0],r=s[e+2],n=s[e+4],i=Math.max(t,r,n),a=Math.min(t,r,n);i>.9&&a<.1&&(t<.2&&(s[e+0]+=1),r<.2&&(s[e+2]+=1),n<.2&&(s[e+4]+=1))}}()}(),this.setAttribute("position",new tf(i,3)),this.setAttribute("normal",new tf(i.slice(),3)),this.setAttribute("uv",new tf(s,2)),0===n?this.computeVertexNormals():this.normalizeNormals()}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new tZ(e.vertices,e.indices,e.radius,e.details)}}class tY extends tZ{constructor(e=1,t=0){let r=(1+Math.sqrt(5))/2,n=1/r;super([-1,-1,-1,-1,-1,1,-1,1,-1,-1,1,1,1,-1,-1,1,-1,1,1,1,-1,1,1,1,0,-n,-r,0,-n,r,0,n,-r,0,n,r,-n,-r,0,-n,r,0,n,-r,0,n,r,0,-r,0,-n,r,0,-n,-r,0,n,r,0,n],[3,11,7,3,7,15,3,15,13,7,19,17,7,17,6,7,6,15,17,4,8,17,8,10,17,10,6,8,0,16,8,16,2,8,2,10,0,12,1,0,1,18,0,18,16,6,10,2,6,2,13,6,13,15,2,16,18,2,18,3,2,3,13,18,1,9,18,9,11,18,11,3,4,14,12,4,12,0,4,0,8,11,9,5,11,5,19,11,19,7,19,5,14,19,14,4,19,4,17,1,12,14,1,14,5,1,5,9],e,t),this.type="DodecahedronGeometry",this.parameters={radius:e,detail:t}}static fromJSON(e){return new tY(e.radius,e.detail)}}let tX=new C,tG=new C,tQ=new C,t$=new e7;class tK{constructor(){this.type="Curve",this.arcLengthDivisions=200,this.needsUpdate=!1,this.cacheArcLengths=null}getPoint(){console.warn("THREE.Curve: .getPoint() not implemented.")}getPointAt(e,t){let r=this.getUtoTmapping(e);return this.getPoint(r,t)}getPoints(e=5){let t=[];for(let r=0;r<=e;r++)t.push(this.getPoint(r/e));return t}getSpacedPoints(e=5){let t=[];for(let r=0;r<=e;r++)t.push(this.getPointAt(r/e));return t}getLength(){let e=this.getLengths();return e[e.length-1]}getLengths(e=this.arcLengthDivisions){if(this.cacheArcLengths&&this.cacheArcLengths.length===e+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;let t=[],r,n=this.getPoint(0),i=0;t.push(0);for(let s=1;s<=e;s++)t.push(i+=(r=this.getPoint(s/e)).distanceTo(n)),n=r;return this.cacheArcLengths=t,t}updateArcLengths(){this.needsUpdate=!0,this.getLengths()}getUtoTmapping(e,t=null){let r,n=this.getLengths(),i=0,s=n.length;r=t||e*n[s-1];let a=0,o=s-1,l;for(;a<=o;)if((l=n[i=Math.floor(a+(o-a)/2)]-r)<0)a=i+1;else if(l>0)o=i-1;else{o=i;break}if(n[i=o]===r)return i/(s-1);let u=n[i],h=n[i+1];return(i+(r-u)/(h-u))/(s-1)}getTangent(e,t){let r=e-1e-4,n=e+1e-4;r<0&&(r=0),n>1&&(n=1);let i=this.getPoint(r),s=this.getPoint(n),a=t||(i.isVector2?new z:new C);return a.copy(s).sub(i).normalize(),a}getTangentAt(e,t){let r=this.getUtoTmapping(e);return this.getTangent(r,t)}computeFrenetFrames(e,t=!1){let r=new C,n=[],i=[],s=[],a=new C,o=new ew;for(let t=0;t<=e;t++){let r=t/e;n[t]=this.getTangentAt(r,new C)}i[0]=new C,s[0]=new C;let l=Number.MAX_VALUE,u=Math.abs(n[0].x),h=Math.abs(n[0].y),c=Math.abs(n[0].z);u<=l&&(l=u,r.set(1,0,0)),h<=l&&(l=h,r.set(0,1,0)),c<=l&&r.set(0,0,1),a.crossVectors(n[0],r).normalize(),i[0].crossVectors(n[0],a),s[0].crossVectors(n[0],i[0]);for(let t=1;t<=e;t++){if(i[t]=i[t-1].clone(),s[t]=s[t-1].clone(),a.crossVectors(n[t-1],n[t]),a.length()>Number.EPSILON){a.normalize();let e=Math.acos(S(n[t-1].dot(n[t]),-1,1));i[t].applyMatrix4(o.makeRotationAxis(a,e))}s[t].crossVectors(n[t],i[t])}if(!0===t){let t=Math.acos(S(i[0].dot(i[e]),-1,1));t/=e,n[0].dot(a.crossVectors(i[0],i[e]))>0&&(t=-t);for(let r=1;r<=e;r++)i[r].applyMatrix4(o.makeRotationAxis(n[r],t*r)),s[r].crossVectors(n[r],i[r])}return{tangents:n,normals:i,binormals:s}}clone(){return new this.constructor().copy(this)}copy(e){return this.arcLengthDivisions=e.arcLengthDivisions,this}toJSON(){let e={metadata:{version:4.7,type:"Curve",generator:"Curve.toJSON"}};return e.arcLengthDivisions=this.arcLengthDivisions,e.type=this.type,e}fromJSON(e){return this.arcLengthDivisions=e.arcLengthDivisions,this}}class t0 extends tK{constructor(e=0,t=0,r=1,n=1,i=0,s=2*Math.PI,a=!1,o=0){super(),this.isEllipseCurve=!0,this.type="EllipseCurve",this.aX=e,this.aY=t,this.xRadius=r,this.yRadius=n,this.aStartAngle=i,this.aEndAngle=s,this.aClockwise=a,this.aRotation=o}getPoint(e,t=new z){let r=2*Math.PI,n=this.aEndAngle-this.aStartAngle,i=Math.abs(n)<Number.EPSILON;for(;n<0;)n+=r;for(;n>r;)n-=r;n<Number.EPSILON&&(n=i?0:r),!0!==this.aClockwise||i||(n===r?n=-r:n-=r);let s=this.aStartAngle+e*n,a=this.aX+this.xRadius*Math.cos(s),o=this.aY+this.yRadius*Math.sin(s);if(0!==this.aRotation){let e=Math.cos(this.aRotation),t=Math.sin(this.aRotation),r=a-this.aX,n=o-this.aY;a=r*e-n*t+this.aX,o=r*t+n*e+this.aY}return t.set(a,o)}copy(e){return super.copy(e),this.aX=e.aX,this.aY=e.aY,this.xRadius=e.xRadius,this.yRadius=e.yRadius,this.aStartAngle=e.aStartAngle,this.aEndAngle=e.aEndAngle,this.aClockwise=e.aClockwise,this.aRotation=e.aRotation,this}toJSON(){let e=super.toJSON();return e.aX=this.aX,e.aY=this.aY,e.xRadius=this.xRadius,e.yRadius=this.yRadius,e.aStartAngle=this.aStartAngle,e.aEndAngle=this.aEndAngle,e.aClockwise=this.aClockwise,e.aRotation=this.aRotation,e}fromJSON(e){return super.fromJSON(e),this.aX=e.aX,this.aY=e.aY,this.xRadius=e.xRadius,this.yRadius=e.yRadius,this.aStartAngle=e.aStartAngle,this.aEndAngle=e.aEndAngle,this.aClockwise=e.aClockwise,this.aRotation=e.aRotation,this}}class t1 extends t0{constructor(e,t,r,n,i,s){super(e,t,r,r,n,i,s),this.isArcCurve=!0,this.type="ArcCurve"}}function t2(){let e=0,t=0,r=0,n=0;function i(i,s,a,o){e=i,t=a,r=-3*i+3*s-2*a-o,n=2*i-2*s+a+o}return{initCatmullRom:function(e,t,r,n,s){i(t,r,s*(r-e),s*(n-t))},initNonuniformCatmullRom:function(e,t,r,n,s,a,o){let l=(t-e)/s-(r-e)/(s+a)+(r-t)/a,u=(r-t)/a-(n-t)/(a+o)+(n-r)/o;i(t,r,l*=a,u*=a)},calc:function(i){let s=i*i;return e+t*i+r*s+s*i*n}}}let t3=new C,t4=new t2,t5=new t2,t6=new t2;class t8 extends tK{constructor(e=[],t=!1,r="centripetal",n=.5){super(),this.isCatmullRomCurve3=!0,this.type="CatmullRomCurve3",this.points=e,this.closed=t,this.curveType=r,this.tension=n}getPoint(e,t=new C){let r,n,i=this.points,s=i.length,a=(s-!this.closed)*e,o=Math.floor(a),l=a-o;this.closed?o+=o>0?0:(Math.floor(Math.abs(o)/s)+1)*s:0===l&&o===s-1&&(o=s-2,l=1),this.closed||o>0?r=i[(o-1)%s]:(t3.subVectors(i[0],i[1]).add(i[0]),r=t3);let u=i[o%s],h=i[(o+1)%s];if(this.closed||o+2<s?n=i[(o+2)%s]:(t3.subVectors(i[s-1],i[s-2]).add(i[s-1]),n=t3),"centripetal"===this.curveType||"chordal"===this.curveType){let e="chordal"===this.curveType?.5:.25,t=Math.pow(r.distanceToSquared(u),e),i=Math.pow(u.distanceToSquared(h),e),s=Math.pow(h.distanceToSquared(n),e);i<1e-4&&(i=1),t<1e-4&&(t=i),s<1e-4&&(s=i),t4.initNonuniformCatmullRom(r.x,u.x,h.x,n.x,t,i,s),t5.initNonuniformCatmullRom(r.y,u.y,h.y,n.y,t,i,s),t6.initNonuniformCatmullRom(r.z,u.z,h.z,n.z,t,i,s)}else"catmullrom"===this.curveType&&(t4.initCatmullRom(r.x,u.x,h.x,n.x,this.tension),t5.initCatmullRom(r.y,u.y,h.y,n.y,this.tension),t6.initCatmullRom(r.z,u.z,h.z,n.z,this.tension));return t.set(t4.calc(l),t5.calc(l),t6.calc(l)),t}copy(e){super.copy(e),this.points=[];for(let t=0,r=e.points.length;t<r;t++){let r=e.points[t];this.points.push(r.clone())}return this.closed=e.closed,this.curveType=e.curveType,this.tension=e.tension,this}toJSON(){let e=super.toJSON();e.points=[];for(let t=0,r=this.points.length;t<r;t++){let r=this.points[t];e.points.push(r.toArray())}return e.closed=this.closed,e.curveType=this.curveType,e.tension=this.tension,e}fromJSON(e){super.fromJSON(e),this.points=[];for(let t=0,r=e.points.length;t<r;t++){let r=e.points[t];this.points.push(new C().fromArray(r))}return this.closed=e.closed,this.curveType=e.curveType,this.tension=e.tension,this}}function t7(e,t,r,n,i){let s=(n-t)*.5,a=(i-r)*.5,o=e*e;return e*o*(2*r-2*n+s+a)+(-3*r+3*n-2*s-a)*o+s*e+r}function t9(e,t,r,n){return function(e,t){let r=1-e;return r*r*t}(e,t)+2*(1-e)*e*r+e*e*n}function re(e,t,r,n,i){return function(e,t){let r=1-e;return r*r*r*t}(e,t)+function(e,t){let r=1-e;return 3*r*r*e*t}(e,r)+3*(1-e)*e*e*n+e*e*e*i}class rt extends tK{constructor(e=new z,t=new z,r=new z,n=new z){super(),this.isCubicBezierCurve=!0,this.type="CubicBezierCurve",this.v0=e,this.v1=t,this.v2=r,this.v3=n}getPoint(e,t=new z){let r=this.v0,n=this.v1,i=this.v2,s=this.v3;return t.set(re(e,r.x,n.x,i.x,s.x),re(e,r.y,n.y,i.y,s.y)),t}copy(e){return super.copy(e),this.v0.copy(e.v0),this.v1.copy(e.v1),this.v2.copy(e.v2),this.v3.copy(e.v3),this}toJSON(){let e=super.toJSON();return e.v0=this.v0.toArray(),e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e.v3=this.v3.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v0.fromArray(e.v0),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this.v3.fromArray(e.v3),this}}class rr extends tK{constructor(e=new C,t=new C,r=new C,n=new C){super(),this.isCubicBezierCurve3=!0,this.type="CubicBezierCurve3",this.v0=e,this.v1=t,this.v2=r,this.v3=n}getPoint(e,t=new C){let r=this.v0,n=this.v1,i=this.v2,s=this.v3;return t.set(re(e,r.x,n.x,i.x,s.x),re(e,r.y,n.y,i.y,s.y),re(e,r.z,n.z,i.z,s.z)),t}copy(e){return super.copy(e),this.v0.copy(e.v0),this.v1.copy(e.v1),this.v2.copy(e.v2),this.v3.copy(e.v3),this}toJSON(){let e=super.toJSON();return e.v0=this.v0.toArray(),e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e.v3=this.v3.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v0.fromArray(e.v0),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this.v3.fromArray(e.v3),this}}class rn extends tK{constructor(e=new z,t=new z){super(),this.isLineCurve=!0,this.type="LineCurve",this.v1=e,this.v2=t}getPoint(e,t=new z){return 1===e?t.copy(this.v2):(t.copy(this.v2).sub(this.v1),t.multiplyScalar(e).add(this.v1)),t}getPointAt(e,t){return this.getPoint(e,t)}getTangent(e,t=new z){return t.subVectors(this.v2,this.v1).normalize()}getTangentAt(e,t){return this.getTangent(e,t)}copy(e){return super.copy(e),this.v1.copy(e.v1),this.v2.copy(e.v2),this}toJSON(){let e=super.toJSON();return e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this}}class ri extends tK{constructor(e=new C,t=new C){super(),this.isLineCurve3=!0,this.type="LineCurve3",this.v1=e,this.v2=t}getPoint(e,t=new C){return 1===e?t.copy(this.v2):(t.copy(this.v2).sub(this.v1),t.multiplyScalar(e).add(this.v1)),t}getPointAt(e,t){return this.getPoint(e,t)}getTangent(e,t=new C){return t.subVectors(this.v2,this.v1).normalize()}getTangentAt(e,t){return this.getTangent(e,t)}copy(e){return super.copy(e),this.v1.copy(e.v1),this.v2.copy(e.v2),this}toJSON(){let e=super.toJSON();return e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this}}class rs extends tK{constructor(e=new z,t=new z,r=new z){super(),this.isQuadraticBezierCurve=!0,this.type="QuadraticBezierCurve",this.v0=e,this.v1=t,this.v2=r}getPoint(e,t=new z){let r=this.v0,n=this.v1,i=this.v2;return t.set(t9(e,r.x,n.x,i.x),t9(e,r.y,n.y,i.y)),t}copy(e){return super.copy(e),this.v0.copy(e.v0),this.v1.copy(e.v1),this.v2.copy(e.v2),this}toJSON(){let e=super.toJSON();return e.v0=this.v0.toArray(),e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v0.fromArray(e.v0),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this}}class ra extends tK{constructor(e=new C,t=new C,r=new C){super(),this.isQuadraticBezierCurve3=!0,this.type="QuadraticBezierCurve3",this.v0=e,this.v1=t,this.v2=r}getPoint(e,t=new C){let r=this.v0,n=this.v1,i=this.v2;return t.set(t9(e,r.x,n.x,i.x),t9(e,r.y,n.y,i.y),t9(e,r.z,n.z,i.z)),t}copy(e){return super.copy(e),this.v0.copy(e.v0),this.v1.copy(e.v1),this.v2.copy(e.v2),this}toJSON(){let e=super.toJSON();return e.v0=this.v0.toArray(),e.v1=this.v1.toArray(),e.v2=this.v2.toArray(),e}fromJSON(e){return super.fromJSON(e),this.v0.fromArray(e.v0),this.v1.fromArray(e.v1),this.v2.fromArray(e.v2),this}}class ro extends tK{constructor(e=[]){super(),this.isSplineCurve=!0,this.type="SplineCurve",this.points=e}getPoint(e,t=new z){let r=this.points,n=(r.length-1)*e,i=Math.floor(n),s=n-i,a=r[0===i?i:i-1],o=r[i],l=r[i>r.length-2?r.length-1:i+1],u=r[i>r.length-3?r.length-1:i+2];return t.set(t7(s,a.x,o.x,l.x,u.x),t7(s,a.y,o.y,l.y,u.y)),t}copy(e){super.copy(e),this.points=[];for(let t=0,r=e.points.length;t<r;t++){let r=e.points[t];this.points.push(r.clone())}return this}toJSON(){let e=super.toJSON();e.points=[];for(let t=0,r=this.points.length;t<r;t++){let r=this.points[t];e.points.push(r.toArray())}return e}fromJSON(e){super.fromJSON(e),this.points=[];for(let t=0,r=e.points.length;t<r;t++){let r=e.points[t];this.points.push(new z().fromArray(r))}return this}}var rl=Object.freeze({__proto__:null,ArcCurve:t1,CatmullRomCurve3:t8,CubicBezierCurve:rt,CubicBezierCurve3:rr,EllipseCurve:t0,LineCurve:rn,LineCurve3:ri,QuadraticBezierCurve:rs,QuadraticBezierCurve3:ra,SplineCurve:ro});class ru extends tK{constructor(){super(),this.type="CurvePath",this.curves=[],this.autoClose=!1}add(e){this.curves.push(e)}closePath(){let e=this.curves[0].getPoint(0),t=this.curves[this.curves.length-1].getPoint(1);if(!e.equals(t)){let r=!0===e.isVector2?"LineCurve":"LineCurve3";this.curves.push(new rl[r](t,e))}return this}getPoint(e,t){let r=e*this.getLength(),n=this.getCurveLengths(),i=0;for(;i<n.length;){if(n[i]>=r){let e=n[i]-r,s=this.curves[i],a=s.getLength(),o=0===a?0:1-e/a;return s.getPointAt(o,t)}i++}return null}getLength(){let e=this.getCurveLengths();return e[e.length-1]}updateArcLengths(){this.needsUpdate=!0,this.cacheLengths=null,this.getCurveLengths()}getCurveLengths(){if(this.cacheLengths&&this.cacheLengths.length===this.curves.length)return this.cacheLengths;let e=[],t=0;for(let r=0,n=this.curves.length;r<n;r++)e.push(t+=this.curves[r].getLength());return this.cacheLengths=e,e}getSpacedPoints(e=40){let t=[];for(let r=0;r<=e;r++)t.push(this.getPoint(r/e));return this.autoClose&&t.push(t[0]),t}getPoints(e=12){let t,r=[];for(let n=0,i=this.curves;n<i.length;n++){let s=i[n],a=s.isEllipseCurve?2*e:s.isLineCurve||s.isLineCurve3?1:s.isSplineCurve?e*s.points.length:e,o=s.getPoints(a);for(let e=0;e<o.length;e++){let n=o[e];t&&t.equals(n)||(r.push(n),t=n)}}return this.autoClose&&r.length>1&&!r[r.length-1].equals(r[0])&&r.push(r[0]),r}copy(e){super.copy(e),this.curves=[];for(let t=0,r=e.curves.length;t<r;t++){let r=e.curves[t];this.curves.push(r.clone())}return this.autoClose=e.autoClose,this}toJSON(){let e=super.toJSON();e.autoClose=this.autoClose,e.curves=[];for(let t=0,r=this.curves.length;t<r;t++){let r=this.curves[t];e.curves.push(r.toJSON())}return e}fromJSON(e){super.fromJSON(e),this.autoClose=e.autoClose,this.curves=[];for(let t=0,r=e.curves.length;t<r;t++){let r=e.curves[t];this.curves.push(new rl[r.type]().fromJSON(r))}return this}}class rh extends ru{constructor(e){super(),this.type="Path",this.currentPoint=new z,e&&this.setFromPoints(e)}setFromPoints(e){this.moveTo(e[0].x,e[0].y);for(let t=1,r=e.length;t<r;t++)this.lineTo(e[t].x,e[t].y);return this}moveTo(e,t){return this.currentPoint.set(e,t),this}lineTo(e,t){let r=new rn(this.currentPoint.clone(),new z(e,t));return this.curves.push(r),this.currentPoint.set(e,t),this}quadraticCurveTo(e,t,r,n){let i=new rs(this.currentPoint.clone(),new z(e,t),new z(r,n));return this.curves.push(i),this.currentPoint.set(r,n),this}bezierCurveTo(e,t,r,n,i,s){let a=new rt(this.currentPoint.clone(),new z(e,t),new z(r,n),new z(i,s));return this.curves.push(a),this.currentPoint.set(i,s),this}splineThru(e){let t=new ro([this.currentPoint.clone()].concat(e));return this.curves.push(t),this.currentPoint.copy(e[e.length-1]),this}arc(e,t,r,n,i,s){let a=this.currentPoint.x,o=this.currentPoint.y;return this.absarc(e+a,t+o,r,n,i,s),this}absarc(e,t,r,n,i,s){return this.absellipse(e,t,r,r,n,i,s),this}ellipse(e,t,r,n,i,s,a,o){let l=this.currentPoint.x,u=this.currentPoint.y;return this.absellipse(e+l,t+u,r,n,i,s,a,o),this}absellipse(e,t,r,n,i,s,a,o){let l=new t0(e,t,r,n,i,s,a,o);if(this.curves.length>0){let e=l.getPoint(0);e.equals(this.currentPoint)||this.lineTo(e.x,e.y)}this.curves.push(l);let u=l.getPoint(1);return this.currentPoint.copy(u),this}copy(e){return super.copy(e),this.currentPoint.copy(e.currentPoint),this}toJSON(){let e=super.toJSON();return e.currentPoint=this.currentPoint.toArray(),e}fromJSON(e){return super.fromJSON(e),this.currentPoint.fromArray(e.currentPoint),this}}class rc extends rh{constructor(e){super(e),this.uuid=w(),this.type="Shape",this.holes=[]}getPointsHoles(e){let t=[];for(let r=0,n=this.holes.length;r<n;r++)t[r]=this.holes[r].getPoints(e);return t}extractPoints(e){return{shape:this.getPoints(e),holes:this.getPointsHoles(e)}}copy(e){super.copy(e),this.holes=[];for(let t=0,r=e.holes.length;t<r;t++){let r=e.holes[t];this.holes.push(r.clone())}return this}toJSON(){let e=super.toJSON();e.uuid=this.uuid,e.holes=[];for(let t=0,r=this.holes.length;t<r;t++){let r=this.holes[t];e.holes.push(r.toJSON())}return e}fromJSON(e){super.fromJSON(e),this.uuid=e.uuid,this.holes=[];for(let t=0,r=e.holes.length;t<r;t++){let r=e.holes[t];this.holes.push(new rh().fromJSON(r))}return this}}function rd(e,t,r,n,i){let s;if(i===function(e,t,r,n){let i=0;for(let s=t,a=r-n;s<r;s+=n)i+=(e[a]-e[s])*(e[s+1]+e[a+1]),a=s;return i}(e,t,r,n)>0)for(let i=t;i<r;i+=n)s=rz(i/n|0,e[i],e[i+1],s);else for(let i=r-n;i>=t;i-=n)s=rz(i/n|0,e[i],e[i+1],s);return s&&rb(s,s.next)&&(rk(s),s=s.next),s}function rf(e,t){if(!e)return e;t||(t=e);let r=e,n;do if(n=!1,!r.steiner&&(rb(r,r.next)||0===rx(r.prev,r,r.next))){if(rk(r),(r=t=r.prev)===r.next)break;n=!0}else r=r.next;while(n||r!==t);return t}function rp(e,t){let r=e.x-t.x;return 0===r&&0==(r=e.y-t.y)&&(r=(e.next.y-e.y)/(e.next.x-e.x)-(t.next.y-t.y)/(t.next.x-t.x)),r}function rm(e,t,r,n,i){return(e=((e=((e=((e=((e=(e-r)*i|0)|e<<8)&0xff00ff)|e<<4)&0xf0f0f0f)|e<<2)&0x33333333)|e<<1)&0x55555555)|(t=((t=((t=((t=((t=(t-n)*i|0)|t<<8)&0xff00ff)|t<<4)&0xf0f0f0f)|t<<2)&0x33333333)|t<<1)&0x55555555)<<1}function ry(e,t,r,n,i,s,a,o){return(i-a)*(t-o)>=(e-a)*(s-o)&&(e-a)*(n-o)>=(r-a)*(t-o)&&(r-a)*(s-o)>=(i-a)*(n-o)}function rg(e,t,r,n,i,s,a,o){return(e!==a||t!==o)&&ry(e,t,r,n,i,s,a,o)}function rx(e,t,r){return(t.y-e.y)*(r.x-t.x)-(t.x-e.x)*(r.y-t.y)}function rb(e,t){return e.x===t.x&&e.y===t.y}function rv(e,t,r,n){let i=rS(rx(e,t,r)),s=rS(rx(e,t,n)),a=rS(rx(r,n,e)),o=rS(rx(r,n,t));return!!(i!==s&&a!==o||0===i&&rw(e,r,t)||0===s&&rw(e,n,t)||0===a&&rw(r,e,n)||0===o&&rw(r,t,n))}function rw(e,t,r){return t.x<=Math.max(e.x,r.x)&&t.x>=Math.min(e.x,r.x)&&t.y<=Math.max(e.y,r.y)&&t.y>=Math.min(e.y,r.y)}function rS(e){return e>0?1:e<0?-1:0}function rM(e,t){return 0>rx(e.prev,e,e.next)?rx(e,t,e.next)>=0&&rx(e,e.prev,t)>=0:0>rx(e,t,e.prev)||0>rx(e,e.next,t)}function r_(e,t){let r=rC(e.i,e.x,e.y),n=rC(t.i,t.x,t.y),i=e.next,s=t.prev;return e.next=t,t.prev=e,r.next=i,i.prev=r,n.next=r,r.prev=n,s.next=n,n.prev=s,n}function rz(e,t,r,n){let i=rC(e,t,r);return n?(i.next=n.next,i.prev=n,n.next.prev=i,n.next=i):(i.prev=i,i.next=i),i}function rk(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function rC(e,t,r){return{i:e,x:t,y:r,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}class rP{static triangulate(e,t,r=2){return function(e,t,r=2){let n,i,s,a=t&&t.length,o=a?t[0]*r:e.length,l=rd(e,0,o,r,!0),u=[];if(!l||l.next===l.prev)return u;if(a&&(l=function(e,t,r,n){let i=[];for(let r=0,s=t.length;r<s;r++){let a=t[r]*n,o=r<s-1?t[r+1]*n:e.length,l=rd(e,a,o,n,!1);l===l.next&&(l.steiner=!0),i.push(function(e){let t=e,r=e;do(t.x<r.x||t.x===r.x&&t.y<r.y)&&(r=t),t=t.next;while(t!==e);return r}(l))}i.sort(rp);for(let e=0;e<i.length;e++)r=function(e,t){let r=function(e,t){let r,n=t,i=e.x,s=e.y,a=-1/0;if(rb(e,n))return n;do{if(rb(e,n.next))return n.next;if(s<=n.y&&s>=n.next.y&&n.next.y!==n.y){let e=n.x+(s-n.y)*(n.next.x-n.x)/(n.next.y-n.y);if(e<=i&&e>a&&(a=e,r=n.x<n.next.x?n:n.next,e===i))return r}n=n.next}while(n!==t);if(!r)return null;let o=r,l=r.x,u=r.y,h=1/0;n=r;do{if(i>=n.x&&n.x>=l&&i!==n.x&&ry(s<u?i:a,s,l,u,s<u?a:i,s,n.x,n.y)){var c,d;let t=Math.abs(s-n.y)/(i-n.x);rM(n,e)&&(t<h||t===h&&(n.x>r.x||n.x===r.x&&(c=r,d=n,0>rx(c.prev,c,d.prev)&&0>rx(d.next,c,c.next))))&&(r=n,h=t)}n=n.next}while(n!==o);return r}(e,t);if(!r)return t;let n=r_(r,e);return rf(n,n.next),rf(r,r.next)}(i[e],r);return r}(e,t,l,r)),e.length>80*r){n=1/0,i=1/0;let t=-1/0,a=-1/0;for(let s=r;s<o;s+=r){let r=e[s],o=e[s+1];r<n&&(n=r),o<i&&(i=o),r>t&&(t=r),o>a&&(a=o)}s=0!==(s=Math.max(t-n,a-i))?32767/s:0}return function e(t,r,n,i,s,a,o){if(!t)return;!o&&a&&function(e,t,r,n){let i=e;do 0===i.z&&(i.z=rm(i.x,i.y,t,r,n)),i.prevZ=i.prev,i.nextZ=i.next,i=i.next;while(i!==e);i.prevZ.nextZ=null,i.prevZ=null,function(e){let t,r=1;do{let n,i=e;e=null;let s=null;for(t=0;i;){t++;let a=i,o=0;for(let e=0;e<r&&(o++,a=a.nextZ);e++);let l=r;for(;o>0||l>0&&a;)0!==o&&(0===l||!a||i.z<=a.z)?(n=i,i=i.nextZ,o--):(n=a,a=a.nextZ,l--),s?s.nextZ=n:e=n,n.prevZ=s,s=n;i=a}s.nextZ=null,r*=2}while(t>1)}(i)}(t,i,s,a);let l=t;for(;t.prev!==t.next;){let u=t.prev,h=t.next;if(a?function(e,t,r,n){let i=e.prev,s=e.next;if(rx(i,e,s)>=0)return!1;let a=i.x,o=e.x,l=s.x,u=i.y,h=e.y,c=s.y,d=Math.min(a,o,l),f=Math.min(u,h,c),p=Math.max(a,o,l),m=Math.max(u,h,c),y=rm(d,f,t,r,n),g=rm(p,m,t,r,n),x=e.prevZ,b=e.nextZ;for(;x&&x.z>=y&&b&&b.z<=g;){if(x.x>=d&&x.x<=p&&x.y>=f&&x.y<=m&&x!==i&&x!==s&&rg(a,u,o,h,l,c,x.x,x.y)&&rx(x.prev,x,x.next)>=0||(x=x.prevZ,b.x>=d&&b.x<=p&&b.y>=f&&b.y<=m&&b!==i&&b!==s&&rg(a,u,o,h,l,c,b.x,b.y)&&rx(b.prev,b,b.next)>=0))return!1;b=b.nextZ}for(;x&&x.z>=y;){if(x.x>=d&&x.x<=p&&x.y>=f&&x.y<=m&&x!==i&&x!==s&&rg(a,u,o,h,l,c,x.x,x.y)&&rx(x.prev,x,x.next)>=0)return!1;x=x.prevZ}for(;b&&b.z<=g;){if(b.x>=d&&b.x<=p&&b.y>=f&&b.y<=m&&b!==i&&b!==s&&rg(a,u,o,h,l,c,b.x,b.y)&&rx(b.prev,b,b.next)>=0)return!1;b=b.nextZ}return!0}(t,i,s,a):function(e){let t=e.prev,r=e.next;if(rx(t,e,r)>=0)return!1;let n=t.x,i=e.x,s=r.x,a=t.y,o=e.y,l=r.y,u=Math.min(n,i,s),h=Math.min(a,o,l),c=Math.max(n,i,s),d=Math.max(a,o,l),f=r.next;for(;f!==t;){if(f.x>=u&&f.x<=c&&f.y>=h&&f.y<=d&&rg(n,a,i,o,s,l,f.x,f.y)&&rx(f.prev,f,f.next)>=0)return!1;f=f.next}return!0}(t)){r.push(u.i,t.i,h.i),rk(t),t=h.next,l=h.next;continue}if((t=h)===l){o?1===o?e(t=function(e,t){let r=e;do{let n=r.prev,i=r.next.next;!rb(n,i)&&rv(n,r,r.next,i)&&rM(n,i)&&rM(i,n)&&(t.push(n.i,r.i,i.i),rk(r),rk(r.next),r=e=i),r=r.next}while(r!==e);return rf(r)}(rf(t),r),r,n,i,s,a,2):2===o&&function(t,r,n,i,s,a){let o=t;do{let t=o.next.next;for(;t!==o.prev;){var l,u;if(o.i!==t.i&&(l=o,u=t,l.next.i!==u.i&&l.prev.i!==u.i&&!function(e,t){let r=e;do{if(r.i!==e.i&&r.next.i!==e.i&&r.i!==t.i&&r.next.i!==t.i&&rv(r,r.next,e,t))return!0;r=r.next}while(r!==e);return!1}(l,u)&&(rM(l,u)&&rM(u,l)&&function(e,t){let r=e,n=!1,i=(e.x+t.x)/2,s=(e.y+t.y)/2;do r.y>s!=r.next.y>s&&r.next.y!==r.y&&i<(r.next.x-r.x)*(s-r.y)/(r.next.y-r.y)+r.x&&(n=!n),r=r.next;while(r!==e);return n}(l,u)&&(rx(l.prev,l,u.prev)||rx(l,u.prev,u))||rb(l,u)&&rx(l.prev,l,l.next)>0&&rx(u.prev,u,u.next)>0))){let l=r_(o,t);o=rf(o,o.next),l=rf(l,l.next),e(o,r,n,i,s,a,0),e(l,r,n,i,s,a,0);return}t=t.next}o=o.next}while(o!==t)}(t,r,n,i,s,a):e(rf(t),r,n,i,s,a,1);break}}}(l,u,r,n,i,s,0),u}(e,t,r)}}class rE{static area(e){let t=e.length,r=0;for(let n=t-1,i=0;i<t;n=i++)r+=e[n].x*e[i].y-e[i].x*e[n].y;return .5*r}static isClockWise(e){return 0>rE.area(e)}static triangulateShape(e,t){let r=[],n=[],i=[];rA(e),rN(r,e);let s=e.length;t.forEach(rA);for(let e=0;e<t.length;e++)n.push(s),s+=t[e].length,rN(r,t[e]);let a=rP.triangulate(r,n);for(let e=0;e<a.length;e+=3)i.push(a.slice(e,e+3));return i}}function rA(e){let t=e.length;t>2&&e[t-1].equals(e[0])&&e.pop()}function rN(e,t){for(let r=0;r<t.length;r++)e.push(t[r].x),e.push(t[r].y)}class rT extends tw{constructor(e=new rc([new z(.5,.5),new z(-.5,.5),new z(-.5,-.5),new z(.5,-.5)]),t={}){super(),this.type="ExtrudeGeometry",this.parameters={shapes:e,options:t},e=Array.isArray(e)?e:[e];let r=this,n=[],i=[];for(let s=0,a=e.length;s<a;s++)!function(e){let s,a,o,l,u,h=[],c=void 0!==t.curveSegments?t.curveSegments:12,d=void 0!==t.steps?t.steps:1,f=void 0!==t.depth?t.depth:1,p=void 0===t.bevelEnabled||t.bevelEnabled,m=void 0!==t.bevelThickness?t.bevelThickness:.2,y=void 0!==t.bevelSize?t.bevelSize:m-.1,g=void 0!==t.bevelOffset?t.bevelOffset:0,x=void 0!==t.bevelSegments?t.bevelSegments:3,b=t.extrudePath,v=void 0!==t.UVGenerator?t.UVGenerator:rj,w,S=!1;b&&(w=b.getSpacedPoints(d),S=!0,p=!1,s=b.computeFrenetFrames(d,!1),a=new C,o=new C,l=new C),p||(x=0,m=0,y=0,g=0);let M=e.extractPoints(c),_=M.shape,k=M.holes;if(!rE.isClockWise(_)){_=_.reverse();for(let e=0,t=k.length;e<t;e++){let t=k[e];rE.isClockWise(t)&&(k[e]=t.reverse())}}function P(e){let t=1e-10*1e-10,r=e[0];for(let n=1;n<=e.length;n++){let i=n%e.length,s=e[i],a=s.x-r.x,o=s.y-r.y,l=a*a+o*o,u=Math.max(Math.abs(s.x),Math.abs(s.y),Math.abs(r.x),Math.abs(r.y));if(l<=t*u*u){e.splice(i,1),n--;continue}r=s}}P(_),k.forEach(P);let E=k.length,A=_;for(let e=0;e<E;e++){let t=k[e];_=_.concat(t)}function N(e,t,r){return t||console.error("THREE.ExtrudeGeometry: vec does not exist"),e.clone().addScaledVector(t,r)}let T=_.length;function j(e,t,r){let n,i,s,a=e.x-t.x,o=e.y-t.y,l=r.x-e.x,u=r.y-e.y,h=a*a+o*o;if(Math.abs(a*u-o*l)>Number.EPSILON){let c=Math.sqrt(h),d=Math.sqrt(l*l+u*u),f=t.x-o/c,p=t.y+a/c,m=((r.x-u/d-f)*u-(r.y+l/d-p)*l)/(a*u-o*l),y=(n=f+a*m-e.x)*n+(i=p+o*m-e.y)*i;if(y<=2)return new z(n,i);s=Math.sqrt(y/2)}else{let e=!1;a>Number.EPSILON?l>Number.EPSILON&&(e=!0):a<-Number.EPSILON?l<-Number.EPSILON&&(e=!0):Math.sign(o)===Math.sign(u)&&(e=!0),e?(n=-o,i=a,s=Math.sqrt(h)):(n=a,i=o,s=Math.sqrt(h/2))}return new z(n/s,i/s)}let O=[];for(let e=0,t=A.length,r=t-1,n=e+1;e<t;e++,r++,n++)r===t&&(r=0),n===t&&(n=0),O[e]=j(A[e],A[r],A[n]);let R=[],I,L=O.concat();for(let e=0;e<E;e++){let t=k[e];I=[];for(let e=0,r=t.length,n=r-1,i=e+1;e<r;e++,n++,i++)n===r&&(n=0),i===r&&(i=0),I[e]=j(t[e],t[n],t[i]);R.push(I),L=L.concat(I)}if(0===x)u=rE.triangulateShape(A,k);else{let e=[],t=[];for(let r=0;r<x;r++){let n=r/x,i=m*Math.cos(n*Math.PI/2),s=y*Math.sin(n*Math.PI/2)+g;for(let t=0,r=A.length;t<r;t++){let r=N(A[t],O[t],s);D(r.x,r.y,-i),0===n&&e.push(r)}for(let e=0;e<E;e++){let r=k[e];I=R[e];let a=[];for(let e=0,t=r.length;e<t;e++){let t=N(r[e],I[e],s);D(t.x,t.y,-i),0===n&&a.push(t)}0===n&&t.push(a)}}u=rE.triangulateShape(e,t)}let F=u.length,V=y+g;for(let e=0;e<T;e++){let t=p?N(_[e],L[e],V):_[e];S?(o.copy(s.normals[0]).multiplyScalar(t.x),a.copy(s.binormals[0]).multiplyScalar(t.y),l.copy(w[0]).add(o).add(a),D(l.x,l.y,l.z)):D(t.x,t.y,0)}for(let e=1;e<=d;e++)for(let t=0;t<T;t++){let r=p?N(_[t],L[t],V):_[t];S?(o.copy(s.normals[e]).multiplyScalar(r.x),a.copy(s.binormals[e]).multiplyScalar(r.y),l.copy(w[e]).add(o).add(a),D(l.x,l.y,l.z)):D(r.x,r.y,f/d*e)}for(let e=x-1;e>=0;e--){let t=e/x,r=m*Math.cos(t*Math.PI/2),n=y*Math.sin(t*Math.PI/2)+g;for(let e=0,t=A.length;e<t;e++){let t=N(A[e],O[e],n);D(t.x,t.y,f+r)}for(let e=0,t=k.length;e<t;e++){let t=k[e];I=R[e];for(let e=0,i=t.length;e<i;e++){let i=N(t[e],I[e],n);S?D(i.x,i.y+w[d-1].y,w[d-1].x+r):D(i.x,i.y,f+r)}}}function B(e,t){let i=e.length;for(;--i>=0;){let s=i,a=i-1;a<0&&(a=e.length-1);for(let e=0,i=d+2*x;e<i;e++){let i=T*e,o=T*(e+1);!function(e,t,i,s){W(e),W(t),W(s),W(t),W(i),W(s);let a=n.length/3,o=v.generateSideWallUV(r,n,a-6,a-3,a-2,a-1);H(o[0]),H(o[1]),H(o[3]),H(o[1]),H(o[2]),H(o[3])}(t+s+i,t+a+i,t+a+o,t+s+o)}}}function D(e,t,r){h.push(e),h.push(t),h.push(r)}function U(e,t,i){W(e),W(t),W(i);let s=n.length/3,a=v.generateTopUV(r,n,s-3,s-2,s-1);H(a[0]),H(a[1]),H(a[2])}function W(e){n.push(h[3*e+0]),n.push(h[3*e+1]),n.push(h[3*e+2])}function H(e){i.push(e.x),i.push(e.y)}(function(){let e=n.length/3;if(p){let e=0,t=0*T;for(let e=0;e<F;e++){let r=u[e];U(r[2]+t,r[1]+t,r[0]+t)}t=T*(d+2*x);for(let e=0;e<F;e++){let r=u[e];U(r[0]+t,r[1]+t,r[2]+t)}}else{for(let e=0;e<F;e++){let t=u[e];U(t[2],t[1],t[0])}for(let e=0;e<F;e++){let t=u[e];U(t[0]+T*d,t[1]+T*d,t[2]+T*d)}}r.addGroup(e,n.length/3-e,0)})(),function(){let e=n.length/3,t=0;B(A,0),t+=A.length;for(let e=0,r=k.length;e<r;e++){let r=k[e];B(r,t),t+=r.length}r.addGroup(e,n.length/3-e,1)}()}(e[s]);this.setAttribute("position",new tf(n,3)),this.setAttribute("uv",new tf(i,2)),this.computeVertexNormals()}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}toJSON(){let e=super.toJSON();return function(e,t,r){if(r.shapes=[],Array.isArray(e))for(let t=0,n=e.length;t<n;t++){let n=e[t];r.shapes.push(n.uuid)}else r.shapes.push(e.uuid);return r.options=Object.assign({},t),void 0!==t.extrudePath&&(r.options.extrudePath=t.extrudePath.toJSON()),r}(this.parameters.shapes,this.parameters.options,e)}static fromJSON(e,t){let r=[];for(let n=0,i=e.shapes.length;n<i;n++){let i=t[e.shapes[n]];r.push(i)}let n=e.options.extrudePath;return void 0!==n&&(e.options.extrudePath=new rl[n.type]().fromJSON(n)),new rT(r,e.options)}}let rj={generateTopUV:function(e,t,r,n,i){let s=t[3*r],a=t[3*r+1],o=t[3*n],l=t[3*n+1],u=t[3*i],h=t[3*i+1];return[new z(s,a),new z(o,l),new z(u,h)]},generateSideWallUV:function(e,t,r,n,i,s){let a=t[3*r],o=t[3*r+1],l=t[3*r+2],u=t[3*n],h=t[3*n+1],c=t[3*n+2],d=t[3*i],f=t[3*i+1],p=t[3*i+2],m=t[3*s],y=t[3*s+1],g=t[3*s+2];return Math.abs(o-h)<Math.abs(a-u)?[new z(a,1-l),new z(u,1-c),new z(d,1-p),new z(m,1-g)]:[new z(o,1-l),new z(h,1-c),new z(f,1-p),new z(y,1-g)]}};class rO extends tZ{constructor(e=1,t=0){let r=(1+Math.sqrt(5))/2;super([-1,r,0,1,r,0,-1,-r,0,1,-r,0,0,-1,r,0,1,r,0,-1,-r,0,1,-r,r,0,-1,r,0,1,-r,0,-1,-r,0,1],[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1],e,t),this.type="IcosahedronGeometry",this.parameters={radius:e,detail:t}}static fromJSON(e){return new rO(e.radius,e.detail)}}class rR extends tw{constructor(e=[new z(0,-.5),new z(.5,0),new z(0,.5)],t=12,r=0,n=2*Math.PI){super(),this.type="LatheGeometry",this.parameters={points:e,segments:t,phiStart:r,phiLength:n},t=Math.floor(t),n=S(n,0,2*Math.PI);let i=[],s=[],a=[],o=[],l=[],u=1/t,h=new C,c=new z,d=new C,f=new C,p=new C,m=0,y=0;for(let t=0;t<=e.length-1;t++)switch(t){case 0:m=e[t+1].x-e[t].x,d.x=+(y=e[t+1].y-e[t].y),d.y=-m,d.z=0*y,p.copy(d),d.normalize(),o.push(d.x,d.y,d.z);break;case e.length-1:o.push(p.x,p.y,p.z);break;default:m=e[t+1].x-e[t].x,d.x=+(y=e[t+1].y-e[t].y),d.y=-m,d.z=0*y,f.copy(d),d.x+=p.x,d.y+=p.y,d.z+=p.z,d.normalize(),o.push(d.x,d.y,d.z),p.copy(f)}for(let i=0;i<=t;i++){let d=r+i*u*n,f=Math.sin(d),p=Math.cos(d);for(let r=0;r<=e.length-1;r++){h.x=e[r].x*f,h.y=e[r].y,h.z=e[r].x*p,s.push(h.x,h.y,h.z),c.x=i/t,c.y=r/(e.length-1),a.push(c.x,c.y);let n=o[3*r+0]*f,u=o[3*r+1],d=o[3*r+0]*p;l.push(n,u,d)}}for(let r=0;r<t;r++)for(let t=0;t<e.length-1;t++){let n=t+r*e.length,s=n+e.length,a=n+e.length+1,o=n+1;i.push(n,s,o),i.push(a,o,s)}this.setIndex(i),this.setAttribute("position",new tf(s,3)),this.setAttribute("uv",new tf(a,2)),this.setAttribute("normal",new tf(l,3))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rR(e.points,e.segments,e.phiStart,e.phiLength)}}class rI extends tZ{constructor(e=1,t=0){super([1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1],[0,2,4,0,4,3,0,3,5,0,5,2,1,2,5,1,5,3,1,3,4,1,4,2],e,t),this.type="OctahedronGeometry",this.parameters={radius:e,detail:t}}static fromJSON(e){return new rI(e.radius,e.detail)}}class rL extends tw{constructor(e=1,t=1,r=1,n=1){super(),this.type="PlaneGeometry",this.parameters={width:e,height:t,widthSegments:r,heightSegments:n};let i=e/2,s=t/2,a=Math.floor(r),o=Math.floor(n),l=a+1,u=o+1,h=e/a,c=t/o,d=[],f=[],p=[],m=[];for(let e=0;e<u;e++){let t=e*c-s;for(let r=0;r<l;r++){let n=r*h-i;f.push(n,-t,0),p.push(0,0,1),m.push(r/a),m.push(1-e/o)}}for(let e=0;e<o;e++)for(let t=0;t<a;t++){let r=t+l*e,n=t+l*(e+1),i=t+1+l*(e+1),s=t+1+l*e;d.push(r,n,s),d.push(n,i,s)}this.setIndex(d),this.setAttribute("position",new tf(f,3)),this.setAttribute("normal",new tf(p,3)),this.setAttribute("uv",new tf(m,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rL(e.width,e.height,e.widthSegments,e.heightSegments)}}class rF extends tw{constructor(e=.5,t=1,r=32,n=1,i=0,s=2*Math.PI){super(),this.type="RingGeometry",this.parameters={innerRadius:e,outerRadius:t,thetaSegments:r,phiSegments:n,thetaStart:i,thetaLength:s},r=Math.max(3,r);let a=[],o=[],l=[],u=[],h=e,c=(t-e)/(n=Math.max(1,n)),d=new C,f=new z;for(let e=0;e<=n;e++){for(let e=0;e<=r;e++){let n=i+e/r*s;d.x=h*Math.cos(n),d.y=h*Math.sin(n),o.push(d.x,d.y,d.z),l.push(0,0,1),f.x=(d.x/t+1)/2,f.y=(d.y/t+1)/2,u.push(f.x,f.y)}h+=c}for(let e=0;e<n;e++){let t=e*(r+1);for(let e=0;e<r;e++){let n=e+t,i=n+r+1,s=n+r+2,o=n+1;a.push(n,i,o),a.push(i,s,o)}}this.setIndex(a),this.setAttribute("position",new tf(o,3)),this.setAttribute("normal",new tf(l,3)),this.setAttribute("uv",new tf(u,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rF(e.innerRadius,e.outerRadius,e.thetaSegments,e.phiSegments,e.thetaStart,e.thetaLength)}}class rV extends tw{constructor(e=new rc([new z(0,.5),new z(-.5,-.5),new z(.5,-.5)]),t=12){super(),this.type="ShapeGeometry",this.parameters={shapes:e,curveSegments:t};let r=[],n=[],i=[],s=[],a=0,o=0;if(!1===Array.isArray(e))l(e);else for(let t=0;t<e.length;t++)l(e[t]),this.addGroup(a,o,t),a+=o,o=0;function l(e){let a=n.length/3,l=e.extractPoints(t),u=l.shape,h=l.holes;!1===rE.isClockWise(u)&&(u=u.reverse());for(let e=0,t=h.length;e<t;e++){let t=h[e];!0===rE.isClockWise(t)&&(h[e]=t.reverse())}let c=rE.triangulateShape(u,h);for(let e=0,t=h.length;e<t;e++){let t=h[e];u=u.concat(t)}for(let e=0,t=u.length;e<t;e++){let t=u[e];n.push(t.x,t.y,0),i.push(0,0,1),s.push(t.x,t.y)}for(let e=0,t=c.length;e<t;e++){let t=c[e],n=t[0]+a,i=t[1]+a,s=t[2]+a;r.push(n,i,s),o+=3}}this.setIndex(r),this.setAttribute("position",new tf(n,3)),this.setAttribute("normal",new tf(i,3)),this.setAttribute("uv",new tf(s,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}toJSON(){let e=super.toJSON();return function(e,t){if(t.shapes=[],Array.isArray(e))for(let r=0,n=e.length;r<n;r++){let n=e[r];t.shapes.push(n.uuid)}else t.shapes.push(e.uuid);return t}(this.parameters.shapes,e)}static fromJSON(e,t){let r=[];for(let n=0,i=e.shapes.length;n<i;n++){let i=t[e.shapes[n]];r.push(i)}return new rV(r,e.curveSegments)}}class rB extends tw{constructor(e=1,t=32,r=16,n=0,i=2*Math.PI,s=0,a=Math.PI){super(),this.type="SphereGeometry",this.parameters={radius:e,widthSegments:t,heightSegments:r,phiStart:n,phiLength:i,thetaStart:s,thetaLength:a},t=Math.max(3,Math.floor(t)),r=Math.max(2,Math.floor(r));let o=Math.min(s+a,Math.PI),l=0,u=[],h=new C,c=new C,d=[],f=[],p=[],m=[];for(let d=0;d<=r;d++){let y=[],g=d/r,x=0;0===d&&0===s?x=.5/t:d===r&&o===Math.PI&&(x=-.5/t);for(let r=0;r<=t;r++){let o=r/t;h.x=-e*Math.cos(n+o*i)*Math.sin(s+g*a),h.y=e*Math.cos(s+g*a),h.z=e*Math.sin(n+o*i)*Math.sin(s+g*a),f.push(h.x,h.y,h.z),c.copy(h).normalize(),p.push(c.x,c.y,c.z),m.push(o+x,1-g),y.push(l++)}u.push(y)}for(let e=0;e<r;e++)for(let n=0;n<t;n++){let t=u[e][n+1],i=u[e][n],a=u[e+1][n],l=u[e+1][n+1];(0!==e||s>0)&&d.push(t,i,l),(e!==r-1||o<Math.PI)&&d.push(i,a,l)}this.setIndex(d),this.setAttribute("position",new tf(f,3)),this.setAttribute("normal",new tf(p,3)),this.setAttribute("uv",new tf(m,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rB(e.radius,e.widthSegments,e.heightSegments,e.phiStart,e.phiLength,e.thetaStart,e.thetaLength)}}class rD extends tZ{constructor(e=1,t=0){super([1,1,1,-1,-1,1,-1,1,-1,1,-1,-1],[2,1,0,0,3,2,1,3,0,2,3,1],e,t),this.type="TetrahedronGeometry",this.parameters={radius:e,detail:t}}static fromJSON(e){return new rD(e.radius,e.detail)}}class rU extends tw{constructor(e=1,t=.4,r=12,n=48,i=2*Math.PI){super(),this.type="TorusGeometry",this.parameters={radius:e,tube:t,radialSegments:r,tubularSegments:n,arc:i},r=Math.floor(r),n=Math.floor(n);let s=[],a=[],o=[],l=[],u=new C,h=new C,c=new C;for(let s=0;s<=r;s++)for(let d=0;d<=n;d++){let f=d/n*i,p=s/r*Math.PI*2;h.x=(e+t*Math.cos(p))*Math.cos(f),h.y=(e+t*Math.cos(p))*Math.sin(f),h.z=t*Math.sin(p),a.push(h.x,h.y,h.z),u.x=e*Math.cos(f),u.y=e*Math.sin(f),c.subVectors(h,u).normalize(),o.push(c.x,c.y,c.z),l.push(d/n),l.push(s/r)}for(let e=1;e<=r;e++)for(let t=1;t<=n;t++){let r=(n+1)*e+t-1,i=(n+1)*(e-1)+t-1,a=(n+1)*(e-1)+t,o=(n+1)*e+t;s.push(r,i,o),s.push(i,a,o)}this.setIndex(s),this.setAttribute("position",new tf(a,3)),this.setAttribute("normal",new tf(o,3)),this.setAttribute("uv",new tf(l,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rU(e.radius,e.tube,e.radialSegments,e.tubularSegments,e.arc)}}class rW extends tw{constructor(e=1,t=.4,r=64,n=8,i=2,s=3){super(),this.type="TorusKnotGeometry",this.parameters={radius:e,tube:t,tubularSegments:r,radialSegments:n,p:i,q:s},r=Math.floor(r),n=Math.floor(n);let a=[],o=[],l=[],u=[],h=new C,c=new C,d=new C,f=new C,p=new C,m=new C,y=new C;for(let a=0;a<=r;++a){let x=a/r*i*Math.PI*2;g(x,i,s,e,d),g(x+.01,i,s,e,f),m.subVectors(f,d),y.addVectors(f,d),p.crossVectors(m,y),y.crossVectors(p,m),p.normalize(),y.normalize();for(let e=0;e<=n;++e){let i=e/n*Math.PI*2,s=-t*Math.cos(i),f=t*Math.sin(i);h.x=d.x+(s*y.x+f*p.x),h.y=d.y+(s*y.y+f*p.y),h.z=d.z+(s*y.z+f*p.z),o.push(h.x,h.y,h.z),c.subVectors(h,d).normalize(),l.push(c.x,c.y,c.z),u.push(a/r),u.push(e/n)}}for(let e=1;e<=r;e++)for(let t=1;t<=n;t++){let r=(n+1)*(e-1)+(t-1),i=(n+1)*e+(t-1),s=(n+1)*e+t,o=(n+1)*(e-1)+t;a.push(r,i,o),a.push(i,s,o)}function g(e,t,r,n,i){let s=Math.cos(e),a=Math.sin(e),o=r/t*e,l=Math.cos(o);i.x=n*(2+l)*.5*s,i.y=n*(2+l)*a*.5,i.z=n*Math.sin(o)*.5}this.setIndex(a),this.setAttribute("position",new tf(o,3)),this.setAttribute("normal",new tf(l,3)),this.setAttribute("uv",new tf(u,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}static fromJSON(e){return new rW(e.radius,e.tube,e.tubularSegments,e.radialSegments,e.p,e.q)}}class rH extends tw{constructor(e=new ra(new C(-1,-1,0),new C(-1,1,0),new C(1,1,0)),t=64,r=1,n=8,i=!1){super(),this.type="TubeGeometry",this.parameters={path:e,tubularSegments:t,radius:r,radialSegments:n,closed:i};let s=e.computeFrenetFrames(t,i);this.tangents=s.tangents,this.normals=s.normals,this.binormals=s.binormals;let a=new C,o=new C,l=new z,u=new C,h=[],c=[],d=[],f=[];function p(i){u=e.getPointAt(i/t,u);let l=s.normals[i],d=s.binormals[i];for(let e=0;e<=n;e++){let t=e/n*Math.PI*2,i=Math.sin(t),s=-Math.cos(t);o.x=s*l.x+i*d.x,o.y=s*l.y+i*d.y,o.z=s*l.z+i*d.z,o.normalize(),c.push(o.x,o.y,o.z),a.x=u.x+r*o.x,a.y=u.y+r*o.y,a.z=u.z+r*o.z,h.push(a.x,a.y,a.z)}}(function(){for(let e=0;e<t;e++)p(e);p(!1===i?t:0),function(){for(let e=0;e<=t;e++)for(let r=0;r<=n;r++)l.x=e/t,l.y=r/n,d.push(l.x,l.y)}(),function(){for(let e=1;e<=t;e++)for(let t=1;t<=n;t++){let r=(n+1)*(e-1)+(t-1),i=(n+1)*e+(t-1),s=(n+1)*e+t,a=(n+1)*(e-1)+t;f.push(r,i,a),f.push(i,s,a)}}()})(),this.setIndex(f),this.setAttribute("position",new tf(h,3)),this.setAttribute("normal",new tf(c,3)),this.setAttribute("uv",new tf(d,2))}copy(e){return super.copy(e),this.parameters=Object.assign({},e.parameters),this}toJSON(){let e=super.toJSON();return e.path=this.parameters.path.toJSON(),e}static fromJSON(e){return new rH(new rl[e.path.type]().fromJSON(e.path),e.tubularSegments,e.radius,e.radialSegments,e.closed)}}function rq(e,t,r){let n=`${e.x},${e.y},${e.z}-${t.x},${t.y},${t.z}`,i=`${t.x},${t.y},${t.z}-${e.x},${e.y},${e.z}`;return!0!==r.has(n)&&!0!==r.has(i)&&(r.add(n),r.add(i),!0)}function rJ(e,t){return e&&e.constructor!==t?"number"==typeof t.BYTES_PER_ELEMENT?new t(e):Array.prototype.slice.call(e):e}class rZ{constructor(e,t,r,n){this.parameterPositions=e,this._cachedIndex=0,this.resultBuffer=void 0!==n?n:new t.constructor(r),this.sampleValues=t,this.valueSize=r,this.settings=null,this.DefaultSettings_={}}evaluate(e){let t=this.parameterPositions,r=this._cachedIndex,n=t[r],i=t[r-1];r:{n:{let s;i:{s:if(!(e<n)){for(let s=r+2;;){if(void 0===n){if(e<i)break s;return r=t.length,this._cachedIndex=r,this.copySampleValue_(r-1)}if(r===s)break;if(i=n,e<(n=t[++r]))break n}s=t.length;break i}if(!(e>=i)){let a=t[1];e<a&&(r=2,i=a);for(let s=r-2;;){if(void 0===i)return this._cachedIndex=0,this.copySampleValue_(0);if(r===s)break;if(n=i,e>=(i=t[--r-1]))break n}s=r,r=0;break i}break r}for(;r<s;){let n=r+s>>>1;e<t[n]?s=n:r=n+1}if(n=t[r],void 0===(i=t[r-1]))return this._cachedIndex=0,this.copySampleValue_(0);if(void 0===n)return r=t.length,this._cachedIndex=r,this.copySampleValue_(r-1)}this._cachedIndex=r,this.intervalChanged_(r,i,n)}return this.interpolate_(r,i,e,n)}getSettings_(){return this.settings||this.DefaultSettings_}copySampleValue_(e){let t=this.resultBuffer,r=this.sampleValues,n=this.valueSize,i=e*n;for(let e=0;e!==n;++e)t[e]=r[i+e];return t}interpolate_(){throw Error("call to abstract method")}intervalChanged_(){}}class rY extends rZ{constructor(e,t,r,n){super(e,t,r,n),this._weightPrev=-0,this._offsetPrev=-0,this._weightNext=-0,this._offsetNext=-0,this.DefaultSettings_={endingStart:2400,endingEnd:2400}}intervalChanged_(e,t,r){let n=this.parameterPositions,i=e-2,s=e+1,a=n[i],o=n[s];if(void 0===a)switch(this.getSettings_().endingStart){case 2401:i=e,a=2*t-r;break;case 2402:i=n.length-2,a=t+n[i]-n[i+1];break;default:i=e,a=r}if(void 0===o)switch(this.getSettings_().endingEnd){case 2401:s=e,o=2*r-t;break;case 2402:s=1,o=r+n[1]-n[0];break;default:s=e-1,o=t}let l=(r-t)*.5,u=this.valueSize;this._weightPrev=l/(t-a),this._weightNext=l/(o-r),this._offsetPrev=i*u,this._offsetNext=s*u}interpolate_(e,t,r,n){let i=this.resultBuffer,s=this.sampleValues,a=this.valueSize,o=e*a,l=o-a,u=this._offsetPrev,h=this._offsetNext,c=this._weightPrev,d=this._weightNext,f=(r-t)/(n-t),p=f*f,m=p*f,y=-c*m+2*c*p-c*f,g=(1+c)*m+(-1.5-2*c)*p+(-.5+c)*f+1,x=(-1-d)*m+(1.5+d)*p+.5*f,b=d*m-d*p;for(let e=0;e!==a;++e)i[e]=y*s[u+e]+g*s[l+e]+x*s[o+e]+b*s[h+e];return i}}class rX extends rZ{constructor(e,t,r,n){super(e,t,r,n)}interpolate_(e,t,r,n){let i=this.resultBuffer,s=this.sampleValues,a=this.valueSize,o=e*a,l=o-a,u=(r-t)/(n-t),h=1-u;for(let e=0;e!==a;++e)i[e]=s[l+e]*h+s[o+e]*u;return i}}class rG extends rZ{constructor(e,t,r,n){super(e,t,r,n)}interpolate_(e){return this.copySampleValue_(e-1)}}class rQ{constructor(e,t,r,n){if(void 0===e)throw Error("THREE.KeyframeTrack: track name is undefined");if(void 0===t||0===t.length)throw Error("THREE.KeyframeTrack: no keyframes in track named "+e);this.name=e,this.times=rJ(t,this.TimeBufferType),this.values=rJ(r,this.ValueBufferType),this.setInterpolation(n||this.DefaultInterpolation)}static toJSON(e){let t,r=e.constructor;if(r.toJSON!==this.toJSON)t=r.toJSON(e);else{t={name:e.name,times:rJ(e.times,Array),values:rJ(e.values,Array)};let r=e.getInterpolation();r!==e.DefaultInterpolation&&(t.interpolation=r)}return t.type=e.ValueTypeName,t}InterpolantFactoryMethodDiscrete(e){return new rG(this.times,this.values,this.getValueSize(),e)}InterpolantFactoryMethodLinear(e){return new rX(this.times,this.values,this.getValueSize(),e)}InterpolantFactoryMethodSmooth(e){return new rY(this.times,this.values,this.getValueSize(),e)}setInterpolation(e){let t;switch(e){case 2300:t=this.InterpolantFactoryMethodDiscrete;break;case 2301:t=this.InterpolantFactoryMethodLinear;break;case 2302:t=this.InterpolantFactoryMethodSmooth}if(void 0===t){let t="unsupported interpolation for "+this.ValueTypeName+" keyframe track named "+this.name;if(void 0===this.createInterpolant)if(e!==this.DefaultInterpolation)this.setInterpolation(this.DefaultInterpolation);else throw Error(t);return console.warn("THREE.KeyframeTrack:",t),this}return this.createInterpolant=t,this}getInterpolation(){switch(this.createInterpolant){case this.InterpolantFactoryMethodDiscrete:return 2300;case this.InterpolantFactoryMethodLinear:return 2301;case this.InterpolantFactoryMethodSmooth:return 2302}}getValueSize(){return this.values.length/this.times.length}shift(e){if(0!==e){let t=this.times;for(let r=0,n=t.length;r!==n;++r)t[r]+=e}return this}scale(e){if(1!==e){let t=this.times;for(let r=0,n=t.length;r!==n;++r)t[r]*=e}return this}trim(e,t){let r=this.times,n=r.length,i=0,s=n-1;for(;i!==n&&r[i]<e;)++i;for(;-1!==s&&r[s]>t;)--s;if(++s,0!==i||s!==n){i>=s&&(i=(s=Math.max(s,1))-1);let e=this.getValueSize();this.times=r.slice(i,s),this.values=this.values.slice(i*e,s*e)}return this}validate(){var e;let t=!0,r=this.getValueSize();r-Math.floor(r)!=0&&(console.error("THREE.KeyframeTrack: Invalid value size in track.",this),t=!1);let n=this.times,i=this.values,s=n.length;0===s&&(console.error("THREE.KeyframeTrack: Track is empty.",this),t=!1);let a=null;for(let e=0;e!==s;e++){let r=n[e];if("number"==typeof r&&isNaN(r)){console.error("THREE.KeyframeTrack: Time is not a valid number.",this,e,r),t=!1;break}if(null!==a&&a>r){console.error("THREE.KeyframeTrack: Out of order keys.",this,e,r,a),t=!1;break}a=r}if(void 0!==i&&ArrayBuffer.isView(e=i)&&!(e instanceof DataView))for(let e=0,r=i.length;e!==r;++e){let r=i[e];if(isNaN(r)){console.error("THREE.KeyframeTrack: Value is not a valid number.",this,e,r),t=!1;break}}return t}optimize(){let e=this.times.slice(),t=this.values.slice(),r=this.getValueSize(),n=2302===this.getInterpolation(),i=e.length-1,s=1;for(let a=1;a<i;++a){let i=!1,o=e[a];if(o!==e[a+1]&&(1!==a||o!==e[0]))if(n)i=!0;else{let e=a*r,n=e-r,s=e+r;for(let a=0;a!==r;++a){let r=t[e+a];if(r!==t[n+a]||r!==t[s+a]){i=!0;break}}}if(i){if(a!==s){e[s]=e[a];let n=a*r,i=s*r;for(let e=0;e!==r;++e)t[i+e]=t[n+e]}++s}}if(i>0){e[s]=e[i];for(let e=i*r,n=s*r,a=0;a!==r;++a)t[n+a]=t[e+a];++s}return s!==e.length?(this.times=e.slice(0,s),this.values=t.slice(0,s*r)):(this.times=e,this.values=t),this}clone(){let e=this.times.slice(),t=this.values.slice(),r=new this.constructor(this.name,e,t);return r.createInterpolant=this.createInterpolant,r}}rQ.prototype.ValueTypeName="",rQ.prototype.TimeBufferType=Float32Array,rQ.prototype.ValueBufferType=Float32Array,rQ.prototype.DefaultInterpolation=2301;class r$ extends rQ{constructor(e,t,r){super(e,t,r)}}r$.prototype.ValueTypeName="bool",r$.prototype.ValueBufferType=Array,r$.prototype.DefaultInterpolation=2300,r$.prototype.InterpolantFactoryMethodLinear=void 0,r$.prototype.InterpolantFactoryMethodSmooth=void 0;class rK extends rQ{constructor(e,t,r,n){super(e,t,r,n)}}rK.prototype.ValueTypeName="color";class r0 extends rQ{constructor(e,t,r,n){super(e,t,r,n)}}r0.prototype.ValueTypeName="number";class r1 extends rZ{constructor(e,t,r,n){super(e,t,r,n)}interpolate_(e,t,r,n){let i=this.resultBuffer,s=this.sampleValues,a=this.valueSize,o=(r-t)/(n-t),l=e*a;for(let e=l+a;l!==e;l+=4)k.slerpFlat(i,0,s,l-a,s,l,o);return i}}class r2 extends rQ{constructor(e,t,r,n){super(e,t,r,n)}InterpolantFactoryMethodLinear(e){return new r1(this.times,this.values,this.getValueSize(),e)}}r2.prototype.ValueTypeName="quaternion",r2.prototype.InterpolantFactoryMethodSmooth=void 0;class r3 extends rQ{constructor(e,t,r){super(e,t,r)}}r3.prototype.ValueTypeName="string",r3.prototype.ValueBufferType=Array,r3.prototype.DefaultInterpolation=2300,r3.prototype.InterpolantFactoryMethodLinear=void 0,r3.prototype.InterpolantFactoryMethodSmooth=void 0;class r4 extends rQ{constructor(e,t,r,n){super(e,t,r,n)}}r4.prototype.ValueTypeName="vector";let r5={enabled:!1,files:{},add:function(e,t){!1!==this.enabled&&(this.files[e]=t)},get:function(e){if(!1!==this.enabled)return this.files[e]},remove:function(e){delete this.files[e]},clear:function(){this.files={}}};class r6{constructor(e,t,r){let n,i=this,s=!1,a=0,o=0,l=[];this.onStart=void 0,this.onLoad=e,this.onProgress=t,this.onError=r,this.itemStart=function(e){o++,!1===s&&void 0!==i.onStart&&i.onStart(e,a,o),s=!0},this.itemEnd=function(e){a++,void 0!==i.onProgress&&i.onProgress(e,a,o),a===o&&(s=!1,void 0!==i.onLoad&&i.onLoad())},this.itemError=function(e){void 0!==i.onError&&i.onError(e)},this.resolveURL=function(e){return n?n(e):e},this.setURLModifier=function(e){return n=e,this},this.addHandler=function(e,t){return l.push(e,t),this},this.removeHandler=function(e){let t=l.indexOf(e);return -1!==t&&l.splice(t,2),this},this.getHandler=function(e){for(let t=0,r=l.length;t<r;t+=2){let r=l[t],n=l[t+1];if(r.global&&(r.lastIndex=0),r.test(e))return n}return null}}}let r8=new r6;class r7{constructor(e){this.manager=void 0!==e?e:r8,this.crossOrigin="anonymous",this.withCredentials=!1,this.path="",this.resourcePath="",this.requestHeader={}}load(){}loadAsync(e,t){let r=this;return new Promise(function(n,i){r.load(e,n,t,i)})}parse(){}setCrossOrigin(e){return this.crossOrigin=e,this}setWithCredentials(e){return this.withCredentials=e,this}setPath(e){return this.path=e,this}setResourcePath(e){return this.resourcePath=e,this}setRequestHeader(e){return this.requestHeader=e,this}}r7.DEFAULT_MATERIAL_NAME="__DEFAULT";class r9 extends r7{constructor(e){super(e)}load(e,t,r,n){void 0!==this.path&&(e=this.path+e),e=this.manager.resolveURL(e);let i=this,s=r5.get(e);if(void 0!==s)return i.manager.itemStart(e),setTimeout(function(){t&&t(s),i.manager.itemEnd(e)},0),s;let a=T("img");function o(){u(),r5.add(e,this),t&&t(this),i.manager.itemEnd(e)}function l(t){u(),n&&n(t),i.manager.itemError(e),i.manager.itemEnd(e)}function u(){a.removeEventListener("load",o,!1),a.removeEventListener("error",l,!1)}return a.addEventListener("load",o,!1),a.addEventListener("error",l,!1),"data:"!==e.slice(0,5)&&void 0!==this.crossOrigin&&(a.crossOrigin=this.crossOrigin),i.manager.itemStart(e),a.src=e,a}}class ne extends r7{constructor(e){super(e)}load(e,t,r,n){let i=new J,s=new r9(this.manager);return s.setCrossOrigin(this.crossOrigin),s.setPath(this.path),s.load(e,function(e){i.image=e,i.needsUpdate=!0,void 0!==t&&t(i)},r,n),i}}class nt extends tP{constructor(e=-1,t=1,r=1,n=-1,i=.1,s=2e3){super(),this.isOrthographicCamera=!0,this.type="OrthographicCamera",this.zoom=1,this.view=null,this.left=e,this.right=t,this.top=r,this.bottom=n,this.near=i,this.far=s,this.updateProjectionMatrix()}copy(e,t){return super.copy(e,t),this.left=e.left,this.right=e.right,this.top=e.top,this.bottom=e.bottom,this.near=e.near,this.far=e.far,this.zoom=e.zoom,this.view=null===e.view?null:Object.assign({},e.view),this}setViewOffset(e,t,r,n,i,s){null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=e,this.view.fullHeight=t,this.view.offsetX=r,this.view.offsetY=n,this.view.width=i,this.view.height=s,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let e=(this.right-this.left)/(2*this.zoom),t=(this.top-this.bottom)/(2*this.zoom),r=(this.right+this.left)/2,n=(this.top+this.bottom)/2,i=r-e,s=r+e,a=n+t,o=n-t;if(null!==this.view&&this.view.enabled){let e=(this.right-this.left)/this.view.fullWidth/this.zoom,t=(this.top-this.bottom)/this.view.fullHeight/this.zoom;i+=e*this.view.offsetX,s=i+e*this.view.width,a-=t*this.view.offsetY,o=a-t*this.view.height}this.projectionMatrix.makeOrthographic(i,s,a,o,this.near,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(e){let t=super.toJSON(e);return t.object.zoom=this.zoom,t.object.left=this.left,t.object.right=this.right,t.object.top=this.top,t.object.bottom=this.bottom,t.object.near=this.near,t.object.far=this.far,null!==this.view&&(t.object.view=Object.assign({},this.view)),t}}new WeakMap;let nr="\\[\\]\\.:\\/",nn=RegExp("["+nr+"]","g"),ni="[^"+nr+"]",ns="[^"+nr.replace("\\.","")+"]",na=/((?:WC+[\/:])*)/.source.replace("WC",ni),no=/(WCOD+)?/.source.replace("WCOD",ns),nl=RegExp("^"+na+no+/(?:\.(WC+)(?:\[(.+)\])?)?/.source.replace("WC",ni)+/\.(WC+)(?:\[(.+)\])?/.source.replace("WC",ni)+"$"),nu=["material","materials","bones","map"];class nh{constructor(e,t,r){let n=r||nc.parseTrackName(t);this._targetGroup=e,this._bindings=e.subscribe_(t,n)}getValue(e,t){this.bind();let r=this._targetGroup.nCachedObjects_,n=this._bindings[r];void 0!==n&&n.getValue(e,t)}setValue(e,t){let r=this._bindings;for(let n=this._targetGroup.nCachedObjects_,i=r.length;n!==i;++n)r[n].setValue(e,t)}bind(){let e=this._bindings;for(let t=this._targetGroup.nCachedObjects_,r=e.length;t!==r;++t)e[t].bind()}unbind(){let e=this._bindings;for(let t=this._targetGroup.nCachedObjects_,r=e.length;t!==r;++t)e[t].unbind()}}class nc{constructor(e,t,r){this.path=t,this.parsedPath=r||nc.parseTrackName(t),this.node=nc.findNode(e,this.parsedPath.nodeName),this.rootNode=e,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}static create(e,t,r){return e&&e.isAnimationObjectGroup?new nc.Composite(e,t,r):new nc(e,t,r)}static sanitizeNodeName(e){return e.replace(/\s/g,"_").replace(nn,"")}static parseTrackName(e){let t=nl.exec(e);if(null===t)throw Error("PropertyBinding: Cannot parse trackName: "+e);let r={nodeName:t[2],objectName:t[3],objectIndex:t[4],propertyName:t[5],propertyIndex:t[6]},n=r.nodeName&&r.nodeName.lastIndexOf(".");if(void 0!==n&&-1!==n){let e=r.nodeName.substring(n+1);-1!==nu.indexOf(e)&&(r.nodeName=r.nodeName.substring(0,n),r.objectName=e)}if(null===r.propertyName||0===r.propertyName.length)throw Error("PropertyBinding: can not parse propertyName from trackName: "+e);return r}static findNode(e,t){if(void 0===t||""===t||"."===t||-1===t||t===e.name||t===e.uuid)return e;if(e.skeleton){let r=e.skeleton.getBoneByName(t);if(void 0!==r)return r}if(e.children){let r=function(e){for(let n=0;n<e.length;n++){let i=e[n];if(i.name===t||i.uuid===t)return i;let s=r(i.children);if(s)return s}return null},n=r(e.children);if(n)return n}return null}_getValue_unavailable(){}_setValue_unavailable(){}_getValue_direct(e,t){e[t]=this.targetObject[this.propertyName]}_getValue_array(e,t){let r=this.resolvedProperty;for(let n=0,i=r.length;n!==i;++n)e[t++]=r[n]}_getValue_arrayElement(e,t){e[t]=this.resolvedProperty[this.propertyIndex]}_getValue_toArray(e,t){this.resolvedProperty.toArray(e,t)}_setValue_direct(e,t){this.targetObject[this.propertyName]=e[t]}_setValue_direct_setNeedsUpdate(e,t){this.targetObject[this.propertyName]=e[t],this.targetObject.needsUpdate=!0}_setValue_direct_setMatrixWorldNeedsUpdate(e,t){this.targetObject[this.propertyName]=e[t],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_array(e,t){let r=this.resolvedProperty;for(let n=0,i=r.length;n!==i;++n)r[n]=e[t++]}_setValue_array_setNeedsUpdate(e,t){let r=this.resolvedProperty;for(let n=0,i=r.length;n!==i;++n)r[n]=e[t++];this.targetObject.needsUpdate=!0}_setValue_array_setMatrixWorldNeedsUpdate(e,t){let r=this.resolvedProperty;for(let n=0,i=r.length;n!==i;++n)r[n]=e[t++];this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_arrayElement(e,t){this.resolvedProperty[this.propertyIndex]=e[t]}_setValue_arrayElement_setNeedsUpdate(e,t){this.resolvedProperty[this.propertyIndex]=e[t],this.targetObject.needsUpdate=!0}_setValue_arrayElement_setMatrixWorldNeedsUpdate(e,t){this.resolvedProperty[this.propertyIndex]=e[t],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_fromArray(e,t){this.resolvedProperty.fromArray(e,t)}_setValue_fromArray_setNeedsUpdate(e,t){this.resolvedProperty.fromArray(e,t),this.targetObject.needsUpdate=!0}_setValue_fromArray_setMatrixWorldNeedsUpdate(e,t){this.resolvedProperty.fromArray(e,t),this.targetObject.matrixWorldNeedsUpdate=!0}_getValue_unbound(e,t){this.bind(),this.getValue(e,t)}_setValue_unbound(e,t){this.bind(),this.setValue(e,t)}bind(){let e=this.node,t=this.parsedPath,r=t.objectName,n=t.propertyName,i=t.propertyIndex;if(e||(e=nc.findNode(this.rootNode,t.nodeName),this.node=e),this.getValue=this._getValue_unavailable,this.setValue=this._setValue_unavailable,!e)return void console.warn("THREE.PropertyBinding: No target node found for track: "+this.path+".");if(r){let n=t.objectIndex;switch(r){case"materials":if(!e.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!e.material.materials)return void console.error("THREE.PropertyBinding: Can not bind to material.materials as node.material does not have a materials array.",this);e=e.material.materials;break;case"bones":if(!e.skeleton)return void console.error("THREE.PropertyBinding: Can not bind to bones as node does not have a skeleton.",this);e=e.skeleton.bones;for(let t=0;t<e.length;t++)if(e[t].name===n){n=t;break}break;case"map":if("map"in e){e=e.map;break}if(!e.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!e.material.map)return void console.error("THREE.PropertyBinding: Can not bind to material.map as node.material does not have a map.",this);e=e.material.map;break;default:if(void 0===e[r])return void console.error("THREE.PropertyBinding: Can not bind to objectName of node undefined.",this);e=e[r]}if(void 0!==n){if(void 0===e[n])return void console.error("THREE.PropertyBinding: Trying to bind to objectIndex of objectName, but is undefined.",this,e);e=e[n]}}let s=e[n];if(void 0===s)return void console.error("THREE.PropertyBinding: Trying to update property for track: "+t.nodeName+"."+n+" but it wasn't found.",e);let a=this.Versioning.None;this.targetObject=e,!0===e.isMaterial?a=this.Versioning.NeedsUpdate:!0===e.isObject3D&&(a=this.Versioning.MatrixWorldNeedsUpdate);let o=this.BindingType.Direct;if(void 0!==i){if("morphTargetInfluences"===n){if(!e.geometry)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.",this);if(!e.geometry.morphAttributes)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.morphAttributes.",this);void 0!==e.morphTargetDictionary[i]&&(i=e.morphTargetDictionary[i])}o=this.BindingType.ArrayElement,this.resolvedProperty=s,this.propertyIndex=i}else void 0!==s.fromArray&&void 0!==s.toArray?(o=this.BindingType.HasFromToArray,this.resolvedProperty=s):Array.isArray(s)?(o=this.BindingType.EntireArray,this.resolvedProperty=s):this.propertyName=n;this.getValue=this.GetterByBindingType[o],this.setValue=this.SetterByBindingTypeAndVersioning[o][a]}unbind(){this.node=null,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}}nc.Composite=nh,nc.prototype.BindingType={Direct:0,EntireArray:1,ArrayElement:2,HasFromToArray:3},nc.prototype.Versioning={None:0,NeedsUpdate:1,MatrixWorldNeedsUpdate:2},nc.prototype.GetterByBindingType=[nc.prototype._getValue_direct,nc.prototype._getValue_array,nc.prototype._getValue_arrayElement,nc.prototype._getValue_toArray],nc.prototype.SetterByBindingTypeAndVersioning=[[nc.prototype._setValue_direct,nc.prototype._setValue_direct_setNeedsUpdate,nc.prototype._setValue_direct_setMatrixWorldNeedsUpdate],[nc.prototype._setValue_array,nc.prototype._setValue_array_setNeedsUpdate,nc.prototype._setValue_array_setMatrixWorldNeedsUpdate],[nc.prototype._setValue_arrayElement,nc.prototype._setValue_arrayElement_setNeedsUpdate,nc.prototype._setValue_arrayElement_setMatrixWorldNeedsUpdate],[nc.prototype._setValue_fromArray,nc.prototype._setValue_fromArray_setNeedsUpdate,nc.prototype._setValue_fromArray_setMatrixWorldNeedsUpdate]],new Float32Array(1);class nd{constructor(e=1,t=0,r=0){this.radius=e,this.phi=t,this.theta=r}set(e,t,r){return this.radius=e,this.phi=t,this.theta=r,this}copy(e){return this.radius=e.radius,this.phi=e.phi,this.theta=e.theta,this}makeSafe(){return this.phi=S(this.phi,1e-6,Math.PI-1e-6),this}setFromVector3(e){return this.setFromCartesianCoords(e.x,e.y,e.z)}setFromCartesianCoords(e,t,r){return this.radius=Math.sqrt(e*e+t*t+r*r),0===this.radius?(this.theta=0,this.phi=0):(this.theta=Math.atan2(e,r),this.phi=Math.acos(S(t/this.radius,-1,1))),this}clone(){return new this.constructor().copy(this)}}"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("register",{detail:{revision:"177"}})),"undefined"!=typeof window&&(window.__THREE__?console.warn("WARNING: Multiple instances of Three.js being imported."):window.__THREE__="177");var nf=r(499),np=r(29562),nm=r.n(np),ny=r(53762);let ng=e=>"object"==typeof e&&"function"==typeof e.then,nx=[];function nb(e,t,r=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let n=e.length;if(t.length!==n)return!1;for(let i=0;i<n;i++)if(!r(e[i],t[i]))return!1;return!0}function nv(e,t=null,r=!1,n={}){for(let i of(null===t&&(t=[e]),nx))if(nb(t,i.keys,i.equal)){if(r)return;if(Object.prototype.hasOwnProperty.call(i,"error"))throw i.error;if(Object.prototype.hasOwnProperty.call(i,"response"))return n.lifespan&&n.lifespan>0&&(i.timeout&&clearTimeout(i.timeout),i.timeout=setTimeout(i.remove,n.lifespan)),i.response;if(!r)throw i.promise}let i={keys:t,equal:n.equal,remove:()=>{let e=nx.indexOf(i);-1!==e&&nx.splice(e,1)},promise:(ng(e)?e:e(...t)).then(e=>{i.response=e,n.lifespan&&n.lifespan>0&&(i.timeout=setTimeout(i.remove,n.lifespan))}).catch(e=>i.error=e)};if(nx.push(i),!r)throw i.promise}let nw=(e,t,r)=>nv(e,t,!1,r),nS=(e,t,r)=>void nv(e,t,!0,r),nM=e=>{if(void 0===e||0===e.length)nx.splice(0,nx.length);else{let t=nx.find(t=>nb(e,t.keys,t.equal));t&&t.remove()}};function n_(e){let t=e.root;for(;t.getState().previousRoot;)t=t.getState().previousRoot;return t}h.act;let nz=e=>e&&e.isOrthographicCamera,nk=e=>null!=e&&("string"==typeof e||"number"==typeof e||e.isColor),nC=((e,t)=>"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative"))()?h.useLayoutEffect:h.useEffect;function nP(e){let t=h.useRef(e);return nC(()=>void(t.current=e),[e]),t}let nE={obj:e=>e===Object(e)&&!nE.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,nul:e=>null===e,arr:e=>Array.isArray(e),equ(e,t,{arrays:r="shallow",objects:n="reference",strict:i=!0}={}){let s;if(typeof e!=typeof t||!!e!=!!t)return!1;if(nE.str(e)||nE.num(e)||nE.boo(e))return e===t;let a=nE.obj(e);if(a&&"reference"===n)return e===t;let o=nE.arr(e);if(o&&"reference"===r)return e===t;if((o||a)&&e===t)return!0;for(s in e)if(!(s in t))return!1;if(a&&"shallow"===r&&"shallow"===n){for(s in i?t:e)if(!nE.equ(e[s],t[s],{strict:i,objects:"reference"}))return!1}else for(s in i?t:e)if(e[s]!==t[s])return!1;if(nE.und(s)){if(o&&0===e.length&&0===t.length||a&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}},nA=["children","key","ref"];function nN(e,t,r,n){let i=null==e?void 0:e.__r3f;return!i&&(i={root:t,type:r,parent:null,children:[],props:function(e){let t={};for(let r in e)nA.includes(r)||(t[r]=e[r]);return t}(n),object:e,eventCount:0,handlers:{},isHidden:!1},e&&(e.__r3f=i)),i}function nT(e,t){let r=e[t];if(!t.includes("-"))return{root:e,key:t,target:r};for(let i of(r=e,t.split("-"))){var n;t=i,e=r,r=null==(n=r)?void 0:n[t]}return{root:e,key:t,target:r}}let nj=/-\d+$/;function nO(e,t){if(nE.str(t.props.attach)){if(nj.test(t.props.attach)){let r=t.props.attach.replace(nj,""),{root:n,key:i}=nT(e.object,r);Array.isArray(n[i])||(n[i]=[])}let{root:r,key:n}=nT(e.object,t.props.attach);t.previousAttach=r[n],r[n]=t.object}else nE.fun(t.props.attach)&&(t.previousAttach=t.props.attach(e.object,t.object))}function nR(e,t){if(nE.str(t.props.attach)){let{root:r,key:n}=nT(e.object,t.props.attach),i=t.previousAttach;void 0===i?delete r[n]:r[n]=i}else null==t.previousAttach||t.previousAttach(e.object,t.object);delete t.previousAttach}let nI=[...nA,"args","dispose","attach","object","onUpdate","dispose"],nL=new Map,nF=["map","emissiveMap","sheenColorMap","specularColorMap","envMap"],nV=/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;function nB(e,t){var r,n;let i=e.__r3f,s=i&&n_(i).getState(),a=null==i?void 0:i.eventCount;for(let r in t){let a=t[r];if(nI.includes(r))continue;if(i&&nV.test(r)){"function"==typeof a?i.handlers[r]=a:delete i.handlers[r],i.eventCount=Object.keys(i.handlers).length;continue}if(void 0===a)continue;let{root:o,key:l,target:u}=nT(e,r);u instanceof eT&&a instanceof eT?u.mask=a.mask:u instanceof tn&&nk(a)?u.set(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"function"==typeof u.copy&&null!=a&&a.constructor&&u.constructor===a.constructor?u.copy(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&Array.isArray(a)?"function"==typeof u.fromArray?u.fromArray(a):u.set(...a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"number"==typeof a?"function"==typeof u.setScalar?u.setScalar(a):u.set(a):(o[l]=a,s&&!s.linear&&nF.includes(l)&&null!=(n=o[l])&&n.isTexture&&1023===o[l].format&&1009===o[l].type&&(o[l].colorSpace=f))}if(null!=i&&i.parent&&null!=s&&s.internal&&null!=(r=i.object)&&r.isObject3D&&a!==i.eventCount){let e=i.object,t=s.internal.interaction.indexOf(e);t>-1&&s.internal.interaction.splice(t,1),i.eventCount&&null!==e.raycast&&s.internal.interaction.push(e)}return i&&void 0===i.props.attach&&(i.object.isBufferGeometry?i.props.attach="geometry":i.object.isMaterial&&(i.props.attach="material")),i&&nD(i),e}function nD(e){var t;if(!e.parent)return;null==e.props.onUpdate||e.props.onUpdate(e.object);let r=null==(t=e.root)||null==t.getState?void 0:t.getState();r&&0===r.internal.frames&&r.invalidate()}function nU(e,t){e.manual||(nz(e)?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix())}let nW=e=>null==e?void 0:e.isObject3D,nH=h.createContext(null);function nq(){let e=h.useContext(nH);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function nJ(e=e=>e,t){return nq()(e,t)}function nZ(e,t=0){let r=nq(),n=r.getState().internal.subscribe,i=nP(e);return nC(()=>n(i,t,r),[t,n,r]),null}let nY=new WeakMap,nX=e=>{var t;return"function"==typeof e&&(null==e||null==(t=e.prototype)?void 0:t.constructor)===e};function nG(e,t){return function(r,...n){let i;return nX(r)?(i=nY.get(r))||(i=new r,nY.set(r,i)):i=r,e&&e(i),Promise.all(n.map(e=>new Promise((r,n)=>i.load(e,e=>{nW(null==e?void 0:e.scene)&&Object.assign(e,function(e){let t={nodes:{},materials:{},meshes:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material),e.isMesh&&!t.meshes[e.name]&&(t.meshes[e.name]=e)}),t}(e.scene)),r(e)},t,t=>n(Error(`Could not load ${e}: ${null==t?void 0:t.message}`))))))}}function nQ(e,t,r,n){let i=Array.isArray(t)?t:[t],s=nw(nG(r,n),[e,...i],{equal:nE.equ});return Array.isArray(t)?s:s[0]}nQ.preload=function(e,t,r){let n=Array.isArray(t)?t:[t];return nS(nG(r),[e,...n])},nQ.clear=function(e,t){return nM([e,...Array.isArray(t)?t:[t]])};let n$={},nK=/^three(?=[A-Z])/,n0=e=>`${e[0].toUpperCase()}${e.slice(1)}`;function n1(e,t){let r=n0(e),n=n$[r];if("primitive"!==e&&!n)throw Error(`R3F: ${r} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);if("primitive"===e&&!t.object)throw Error("R3F: Primitives without 'object' are invalid!");if(void 0!==t.args&&!Array.isArray(t.args))throw Error("R3F: The args prop must be an array!")}function n2(e){if(e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?nO(e.parent,e):nW(e.object)&&!1!==e.props.visible&&(e.object.visible=!0),e.isHidden=!1,nD(e)}}function n3(e,t,r){let n=t.root.getState();if(e.parent||e.object===n.scene){if(!t.object){var i,s;let e=n$[n0(t.type)];t.object=null!=(i=t.props.object)?i:new e(...null!=(s=t.props.args)?s:[]),t.object.__r3f=t}if(nB(t.object,t.props),t.props.attach)nO(e,t);else if(nW(t.object)&&nW(e.object)){let n=e.object.children.indexOf(null==r?void 0:r.object);if(r&&-1!==n){let r=e.object.children.indexOf(t.object);-1!==r?(e.object.children.splice(r,1),e.object.children.splice(r<n?n-1:n,0,t.object)):(t.object.parent=e.object,e.object.children.splice(n,0,t.object),t.object.dispatchEvent({type:"added"}),e.object.dispatchEvent({type:"childadded",child:t.object}))}else e.object.add(t.object)}for(let e of t.children)n3(t,e);nD(t)}}function n4(e,t){t&&(t.parent=e,e.children.push(t),n3(e,t))}function n5(e,t,r){if(!t||!r)return;t.parent=e;let n=e.children.indexOf(r);-1!==n?e.children.splice(n,0,t):e.children.push(t),n3(e,t,r)}function n6(e){if("function"==typeof e.dispose){let t=()=>{try{e.dispose()}catch{}};"undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?t():(0,ny.unstable_scheduleCallback)(ny.unstable_IdlePriority,t)}}function n8(e,t,r){if(!t)return;t.parent=null;let n=e.children.indexOf(t);-1!==n&&e.children.splice(n,1),t.props.attach?nR(e,t):nW(t.object)&&nW(e.object)&&(e.object.remove(t.object),function(e,t){let{internal:r}=e.getState();r.interaction=r.interaction.filter(e=>e!==t),r.initialHits=r.initialHits.filter(e=>e!==t),r.hovered.forEach((e,n)=>{(e.eventObject===t||e.object===t)&&r.hovered.delete(n)}),r.capturedMap.forEach((e,n)=>{var i=r.capturedMap,s=t,a=e,o=n;let l=a.get(s);l&&(a.delete(s),0===a.size&&(i.delete(o),l.target.releasePointerCapture(o)))})}(n_(t),t.object));let i=null!==t.props.dispose&&!1!==r;for(let e=t.children.length-1;e>=0;e--){let r=t.children[e];n8(t,r,i)}t.children.length=0,delete t.object.__r3f,i&&"primitive"!==t.type&&"Scene"!==t.object.type&&n6(t.object),void 0===r&&nD(t)}let n7=[],n9=()=>{},ie={},it=0,ir=function(e){let t=nm()(e);return t.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:h.version}),t}({isPrimaryRenderer:!1,warnsIfNotActing:!1,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,createInstance:function(e,t,r){var n;return n1(e=n0(e)in n$?e:e.replace(nK,""),t),"primitive"===e&&null!=(n=t.object)&&n.__r3f&&delete t.object.__r3f,nN(t.object,r,e,t)},removeChild:n8,appendChild:n4,appendInitialChild:n4,insertBefore:n5,appendChildToContainer(e,t){let r=e.getState().scene.__r3f;t&&r&&n4(r,t)},removeChildFromContainer(e,t){let r=e.getState().scene.__r3f;t&&r&&n8(r,t)},insertInContainerBefore(e,t,r){let n=e.getState().scene.__r3f;t&&r&&n&&n5(n,t,r)},getRootHostContext:()=>ie,getChildHostContext:()=>ie,commitUpdate(e,t,r,n,i){var s,a,o;n1(t,n);let l=!1;if("primitive"===e.type&&r.object!==n.object||(null==(s=n.args)?void 0:s.length)!==(null==(a=r.args)?void 0:a.length)?l=!0:null!=(o=n.args)&&o.some((e,t)=>{var n;return e!==(null==(n=r.args)?void 0:n[t])})&&(l=!0),l)n7.push([e,{...n},i]);else{let t=function(e,t){let r={};for(let n in t)if(!nI.includes(n)&&!nE.equ(t[n],e.props[n]))for(let e in r[n]=t[n],t)e.startsWith(`${n}-`)&&(r[e]=t[e]);for(let n in e.props){if(nI.includes(n)||t.hasOwnProperty(n))continue;let{root:i,key:s}=nT(e.object,n);if(i.constructor&&0===i.constructor.length){let e=function(e){let t=nL.get(e.constructor);try{t||(t=new e.constructor,nL.set(e.constructor,t))}catch(e){}return t}(i);nE.und(e)||(r[s]=e[s])}else r[s]=0}return r}(e,n);Object.keys(t).length&&(Object.assign(e.props,t),nB(e.object,t))}(null===i.sibling||(4&i.flags)==0)&&function(){for(let[e]of n7){let t=e.parent;if(t)for(let r of(e.props.attach?nR(t,e):nW(e.object)&&nW(t.object)&&t.object.remove(e.object),e.children))r.props.attach?nR(e,r):nW(r.object)&&nW(e.object)&&e.object.remove(r.object);e.isHidden&&n2(e),e.object.__r3f&&delete e.object.__r3f,"primitive"!==e.type&&n6(e.object)}for(let[n,i,s]of n7){n.props=i;let a=n.parent;if(a){let i=n$[n0(n.type)];n.object=null!=(e=n.props.object)?e:new i(...null!=(t=n.props.args)?t:[]),n.object.__r3f=n;var e,t,r=n.object;for(let e of[s,s.alternate])if(null!==e)if("function"==typeof e.ref){null==e.refCleanup||e.refCleanup();let t=e.ref(r);"function"==typeof t&&(e.refCleanup=t)}else e.ref&&(e.ref.current=r);for(let e of(nB(n.object,n.props),n.props.attach?nO(a,n):nW(n.object)&&nW(a.object)&&a.object.add(n.object),n.children))e.props.attach?nO(n,e):nW(e.object)&&nW(n.object)&&n.object.add(e.object);nD(n)}}n7.length=0}()},finalizeInitialChildren:()=>!1,commitMount(){},getPublicInstance:e=>null==e?void 0:e.object,prepareForCommit:()=>null,preparePortalMount:e=>nN(e.getState().scene,e,"",{}),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance:function(e){if(!e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?nR(e.parent,e):nW(e.object)&&(e.object.visible=!1),e.isHidden=!0,nD(e)}},unhideInstance:n2,createTextInstance:n9,hideTextInstance:n9,unhideTextInstance:n9,scheduleTimeout:"function"==typeof setTimeout?setTimeout:void 0,cancelTimeout:"function"==typeof clearTimeout?clearTimeout:void 0,noTimeout:-1,getInstanceFromNode:()=>null,beforeActiveInstanceBlur(){},afterActiveInstanceBlur(){},detachDeletedInstance(){},prepareScopeUpdate(){},getInstanceFromScope:()=>null,shouldAttemptEagerTransition:()=>!1,trackSchedulerEvent:()=>{},resolveEventType:()=>null,resolveEventTimeStamp:()=>-1.1,requestPostPaintCallback(){},maySuspendCommit:()=>!1,preloadInstance:()=>!0,startSuspendingCommit(){},suspendInstance(){},waitForCommitToBeReady:()=>null,NotPendingTransition:null,HostTransitionContext:h.createContext(null),setCurrentUpdatePriority(e){it=e},getCurrentUpdatePriority:()=>it,resolveUpdatePriority(){var e;if(0!==it)return it;switch("undefined"!=typeof window&&(null==(e=window.event)?void 0:e.type)){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return nf.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return nf.ContinuousEventPriority;default:return nf.DefaultEventPriority}},resetFormInstance(){}}),ii=new Map,is=new Set,ia=new Set,io=new Set;function il(e,t){if(e.size)for(let{callback:r}of e.values())r(t)}function iu(e,t){switch(e){case"before":return il(is,t);case"after":return il(ia,t);case"tail":return il(io,t)}}function ih(e,t,r){let n=t.clock.getDelta();"never"===t.frameloop&&"number"==typeof e&&(n=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),i=t.internal.subscribers;for(let e=0;e<i.length;e++)(s=i[e]).ref.current(s.store.getState(),n,r);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}let ic=!1,id=!1;function ip(e){for(let r of(o=requestAnimationFrame(ip),ic=!0,a=0,iu("before",e),id=!0,ii.values())){var t;(l=r.store.getState()).internal.active&&("always"===l.frameloop||l.internal.frames>0)&&!(null!=(t=l.gl.xr)&&t.isPresenting)&&(a+=ih(e,l))}if(id=!1,iu("after",e),0===a)return iu("tail",e),ic=!1,cancelAnimationFrame(o)}function im(){return(im=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var iy=Object.defineProperty,ig=(e,t,r)=>t in e?iy(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ix=(e,t,r)=>(ig(e,"symbol"!=typeof t?t+"":t,r),r);class ib{constructor(){ix(this,"_listeners")}addEventListener(e,t){void 0===this._listeners&&(this._listeners={});let r=this._listeners;void 0===r[e]&&(r[e]=[]),-1===r[e].indexOf(t)&&r[e].push(t)}hasEventListener(e,t){if(void 0===this._listeners)return!1;let r=this._listeners;return void 0!==r[e]&&-1!==r[e].indexOf(t)}removeEventListener(e,t){if(void 0===this._listeners)return;let r=this._listeners[e];if(void 0!==r){let e=r.indexOf(t);-1!==e&&r.splice(e,1)}}dispatchEvent(e){if(void 0===this._listeners)return;let t=this._listeners[e.type];if(void 0!==t){e.target=this;let r=t.slice(0);for(let t=0,n=r.length;t<n;t++)r[t].call(this,e);e.target=null}}}var iv=Object.defineProperty,iw=(e,t,r)=>t in e?iv(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,iS=(e,t,r)=>(iw(e,"symbol"!=typeof t?t+"":t,r),r);let iM=new ev,i_=new tI,iz=Math.cos(Math.PI/180*70),ik=(e,t)=>(e%t+t)%t;class iC extends ib{constructor(e,t){super(),iS(this,"object"),iS(this,"domElement"),iS(this,"enabled",!0),iS(this,"target",new C),iS(this,"minDistance",0),iS(this,"maxDistance",1/0),iS(this,"minZoom",0),iS(this,"maxZoom",1/0),iS(this,"minPolarAngle",0),iS(this,"maxPolarAngle",Math.PI),iS(this,"minAzimuthAngle",-1/0),iS(this,"maxAzimuthAngle",1/0),iS(this,"enableDamping",!1),iS(this,"dampingFactor",.05),iS(this,"enableZoom",!0),iS(this,"zoomSpeed",1),iS(this,"enableRotate",!0),iS(this,"rotateSpeed",1),iS(this,"enablePan",!0),iS(this,"panSpeed",1),iS(this,"screenSpacePanning",!0),iS(this,"keyPanSpeed",7),iS(this,"zoomToCursor",!1),iS(this,"autoRotate",!1),iS(this,"autoRotateSpeed",2),iS(this,"reverseOrbit",!1),iS(this,"reverseHorizontalOrbit",!1),iS(this,"reverseVerticalOrbit",!1),iS(this,"keys",{LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"}),iS(this,"mouseButtons",{LEFT:c.ROTATE,MIDDLE:c.DOLLY,RIGHT:c.PAN}),iS(this,"touches",{ONE:d.ROTATE,TWO:d.DOLLY_PAN}),iS(this,"target0"),iS(this,"position0"),iS(this,"zoom0"),iS(this,"_domElementKeyEvents",null),iS(this,"getPolarAngle"),iS(this,"getAzimuthalAngle"),iS(this,"setPolarAngle"),iS(this,"setAzimuthalAngle"),iS(this,"getDistance"),iS(this,"getZoomScale"),iS(this,"listenToKeyEvents"),iS(this,"stopListenToKeyEvents"),iS(this,"saveState"),iS(this,"reset"),iS(this,"update"),iS(this,"connect"),iS(this,"dispose"),iS(this,"dollyIn"),iS(this,"dollyOut"),iS(this,"getScale"),iS(this,"setScale"),this.object=e,this.domElement=t,this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this.getPolarAngle=()=>u.phi,this.getAzimuthalAngle=()=>u.theta,this.setPolarAngle=e=>{let t=ik(e,2*Math.PI),n=u.phi;n<0&&(n+=2*Math.PI),t<0&&(t+=2*Math.PI);let i=Math.abs(t-n);2*Math.PI-i<i&&(t<n?t+=2*Math.PI:n+=2*Math.PI),h.phi=t-n,r.update()},this.setAzimuthalAngle=e=>{let t=ik(e,2*Math.PI),n=u.theta;n<0&&(n+=2*Math.PI),t<0&&(t+=2*Math.PI);let i=Math.abs(t-n);2*Math.PI-i<i&&(t<n?t+=2*Math.PI:n+=2*Math.PI),h.theta=t-n,r.update()},this.getDistance=()=>r.object.position.distanceTo(r.target),this.listenToKeyEvents=e=>{e.addEventListener("keydown",K),this._domElementKeyEvents=e},this.stopListenToKeyEvents=()=>{this._domElementKeyEvents.removeEventListener("keydown",K),this._domElementKeyEvents=null},this.saveState=()=>{r.target0.copy(r.target),r.position0.copy(r.object.position),r.zoom0=r.object.zoom},this.reset=()=>{r.target.copy(r.target0),r.object.position.copy(r.position0),r.object.zoom=r.zoom0,r.object.updateProjectionMatrix(),r.dispatchEvent(n),r.update(),o=a.NONE},this.update=(()=>{let t=new C,i=new C(0,1,0),s=new k().setFromUnitVectors(e.up,i),c=s.clone().invert(),d=new C,m=new k,y=2*Math.PI;return function(){let g=r.object.position;s.setFromUnitVectors(e.up,i),c.copy(s).invert(),t.copy(g).sub(r.target),t.applyQuaternion(s),u.setFromVector3(t),r.autoRotate&&o===a.NONE&&j(2*Math.PI/60/60*r.autoRotateSpeed),r.enableDamping?(u.theta+=h.theta*r.dampingFactor,u.phi+=h.phi*r.dampingFactor):(u.theta+=h.theta,u.phi+=h.phi);let x=r.minAzimuthAngle,b=r.maxAzimuthAngle;isFinite(x)&&isFinite(b)&&(x<-Math.PI?x+=y:x>Math.PI&&(x-=y),b<-Math.PI?b+=y:b>Math.PI&&(b-=y),x<=b?u.theta=Math.max(x,Math.min(b,u.theta)):u.theta=u.theta>(x+b)/2?Math.max(x,u.theta):Math.min(b,u.theta)),u.phi=Math.max(r.minPolarAngle,Math.min(r.maxPolarAngle,u.phi)),u.makeSafe(),!0===r.enableDamping?r.target.addScaledVector(p,r.dampingFactor):r.target.add(p),r.zoomToCursor&&E||r.object.isOrthographicCamera?u.radius=B(u.radius):u.radius=B(u.radius*f),t.setFromSpherical(u),t.applyQuaternion(c),g.copy(r.target).add(t),r.object.matrixAutoUpdate||r.object.updateMatrix(),r.object.lookAt(r.target),!0===r.enableDamping?(h.theta*=1-r.dampingFactor,h.phi*=1-r.dampingFactor,p.multiplyScalar(1-r.dampingFactor)):(h.set(0,0,0),p.set(0,0,0));let v=!1;if(r.zoomToCursor&&E){let n=null;if(r.object instanceof tT&&r.object.isPerspectiveCamera){let e=t.length();n=B(e*f);let i=e-n;r.object.position.addScaledVector(_,i),r.object.updateMatrixWorld()}else if(r.object.isOrthographicCamera){let e=new C(P.x,P.y,0);e.unproject(r.object),r.object.zoom=Math.max(r.minZoom,Math.min(r.maxZoom,r.object.zoom/f)),r.object.updateProjectionMatrix(),v=!0;let i=new C(P.x,P.y,0);i.unproject(r.object),r.object.position.sub(i).add(e),r.object.updateMatrixWorld(),n=t.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),r.zoomToCursor=!1;null!==n&&(r.screenSpacePanning?r.target.set(0,0,-1).transformDirection(r.object.matrix).multiplyScalar(n).add(r.object.position):(iM.origin.copy(r.object.position),iM.direction.set(0,0,-1).transformDirection(r.object.matrix),Math.abs(r.object.up.dot(iM.direction))<iz?e.lookAt(r.target):(i_.setFromNormalAndCoplanarPoint(r.object.up,r.target),iM.intersectPlane(i_,r.target))))}else r.object instanceof nt&&r.object.isOrthographicCamera&&(v=1!==f)&&(r.object.zoom=Math.max(r.minZoom,Math.min(r.maxZoom,r.object.zoom/f)),r.object.updateProjectionMatrix());return f=1,E=!1,!!(v||d.distanceToSquared(r.object.position)>l||8*(1-m.dot(r.object.quaternion))>l)&&(r.dispatchEvent(n),d.copy(r.object.position),m.copy(r.object.quaternion),v=!1,!0)}})(),this.connect=e=>{r.domElement=e,r.domElement.style.touchAction="none",r.domElement.addEventListener("contextmenu",ee),r.domElement.addEventListener("pointerdown",X),r.domElement.addEventListener("pointercancel",Q),r.domElement.addEventListener("wheel",$)},this.dispose=()=>{var e,t,n,i,s,a;r.domElement&&(r.domElement.style.touchAction="auto"),null==(e=r.domElement)||e.removeEventListener("contextmenu",ee),null==(t=r.domElement)||t.removeEventListener("pointerdown",X),null==(n=r.domElement)||n.removeEventListener("pointercancel",Q),null==(i=r.domElement)||i.removeEventListener("wheel",$),null==(s=r.domElement)||s.ownerDocument.removeEventListener("pointermove",G),null==(a=r.domElement)||a.ownerDocument.removeEventListener("pointerup",Q),null!==r._domElementKeyEvents&&r._domElementKeyEvents.removeEventListener("keydown",K)};let r=this,n={type:"change"},i={type:"start"},s={type:"end"},a={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6},o=a.NONE,l=1e-6,u=new nd,h=new nd,f=1,p=new C,m=new z,y=new z,g=new z,x=new z,b=new z,v=new z,w=new z,S=new z,M=new z,_=new C,P=new z,E=!1,A=[],N={};function T(){return Math.pow(.95,r.zoomSpeed)}function j(e){r.reverseOrbit||r.reverseHorizontalOrbit?h.theta+=e:h.theta-=e}function O(e){r.reverseOrbit||r.reverseVerticalOrbit?h.phi+=e:h.phi-=e}let R=(()=>{let e=new C;return function(t,r){e.setFromMatrixColumn(r,0),e.multiplyScalar(-t),p.add(e)}})(),I=(()=>{let e=new C;return function(t,n){!0===r.screenSpacePanning?e.setFromMatrixColumn(n,1):(e.setFromMatrixColumn(n,0),e.crossVectors(r.object.up,e)),e.multiplyScalar(t),p.add(e)}})(),L=(()=>{let e=new C;return function(t,n){let i=r.domElement;if(i&&r.object instanceof tT&&r.object.isPerspectiveCamera){let s=r.object.position;e.copy(s).sub(r.target);let a=e.length();R(2*t*(a*=Math.tan(r.object.fov/2*Math.PI/180))/i.clientHeight,r.object.matrix),I(2*n*a/i.clientHeight,r.object.matrix)}else i&&r.object instanceof nt&&r.object.isOrthographicCamera?(R(t*(r.object.right-r.object.left)/r.object.zoom/i.clientWidth,r.object.matrix),I(n*(r.object.top-r.object.bottom)/r.object.zoom/i.clientHeight,r.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),r.enablePan=!1)}})();function F(e){r.object instanceof tT&&r.object.isPerspectiveCamera||r.object instanceof nt&&r.object.isOrthographicCamera?f=e:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),r.enableZoom=!1)}function V(e){if(!r.zoomToCursor||!r.domElement)return;E=!0;let t=r.domElement.getBoundingClientRect(),n=e.clientX-t.left,i=e.clientY-t.top,s=t.width,a=t.height;P.x=n/s*2-1,P.y=-(i/a*2)+1,_.set(P.x,P.y,1).unproject(r.object).sub(r.object.position).normalize()}function B(e){return Math.max(r.minDistance,Math.min(r.maxDistance,e))}function D(e){m.set(e.clientX,e.clientY)}function U(e){x.set(e.clientX,e.clientY)}function W(){if(1==A.length)m.set(A[0].pageX,A[0].pageY);else{let e=.5*(A[0].pageX+A[1].pageX),t=.5*(A[0].pageY+A[1].pageY);m.set(e,t)}}function H(){if(1==A.length)x.set(A[0].pageX,A[0].pageY);else{let e=.5*(A[0].pageX+A[1].pageX),t=.5*(A[0].pageY+A[1].pageY);x.set(e,t)}}function q(){let e=A[0].pageX-A[1].pageX,t=A[0].pageY-A[1].pageY,r=Math.sqrt(e*e+t*t);w.set(0,r)}function J(e){if(1==A.length)y.set(e.pageX,e.pageY);else{let t=er(e),r=.5*(e.pageX+t.x),n=.5*(e.pageY+t.y);y.set(r,n)}g.subVectors(y,m).multiplyScalar(r.rotateSpeed);let t=r.domElement;t&&(j(2*Math.PI*g.x/t.clientHeight),O(2*Math.PI*g.y/t.clientHeight)),m.copy(y)}function Z(e){if(1==A.length)b.set(e.pageX,e.pageY);else{let t=er(e),r=.5*(e.pageX+t.x),n=.5*(e.pageY+t.y);b.set(r,n)}v.subVectors(b,x).multiplyScalar(r.panSpeed),L(v.x,v.y),x.copy(b)}function Y(e){var t;let n=er(e),i=e.pageX-n.x,s=e.pageY-n.y,a=Math.sqrt(i*i+s*s);S.set(0,a),M.set(0,Math.pow(S.y/w.y,r.zoomSpeed)),t=M.y,F(f/t),w.copy(S)}function X(e){var t,n,s;!1!==r.enabled&&(0===A.length&&(null==(t=r.domElement)||t.ownerDocument.addEventListener("pointermove",G),null==(n=r.domElement)||n.ownerDocument.addEventListener("pointerup",Q)),s=e,A.push(s),"touch"===e.pointerType?function(e){switch(et(e),A.length){case 1:switch(r.touches.ONE){case d.ROTATE:if(!1===r.enableRotate)return;W(),o=a.TOUCH_ROTATE;break;case d.PAN:if(!1===r.enablePan)return;H(),o=a.TOUCH_PAN;break;default:o=a.NONE}break;case 2:switch(r.touches.TWO){case d.DOLLY_PAN:if(!1===r.enableZoom&&!1===r.enablePan)return;r.enableZoom&&q(),r.enablePan&&H(),o=a.TOUCH_DOLLY_PAN;break;case d.DOLLY_ROTATE:if(!1===r.enableZoom&&!1===r.enableRotate)return;r.enableZoom&&q(),r.enableRotate&&W(),o=a.TOUCH_DOLLY_ROTATE;break;default:o=a.NONE}break;default:o=a.NONE}o!==a.NONE&&r.dispatchEvent(i)}(e):function(e){let t;switch(e.button){case 0:t=r.mouseButtons.LEFT;break;case 1:t=r.mouseButtons.MIDDLE;break;case 2:t=r.mouseButtons.RIGHT;break;default:t=-1}switch(t){case c.DOLLY:if(!1===r.enableZoom)return;V(e),w.set(e.clientX,e.clientY),o=a.DOLLY;break;case c.ROTATE:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===r.enablePan)return;U(e),o=a.PAN}else{if(!1===r.enableRotate)return;D(e),o=a.ROTATE}break;case c.PAN:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===r.enableRotate)return;D(e),o=a.ROTATE}else{if(!1===r.enablePan)return;U(e),o=a.PAN}break;default:o=a.NONE}o!==a.NONE&&r.dispatchEvent(i)}(e))}function G(e){!1!==r.enabled&&("touch"===e.pointerType?function(e){switch(et(e),o){case a.TOUCH_ROTATE:if(!1===r.enableRotate)return;J(e),r.update();break;case a.TOUCH_PAN:if(!1===r.enablePan)return;Z(e),r.update();break;case a.TOUCH_DOLLY_PAN:if(!1===r.enableZoom&&!1===r.enablePan)return;r.enableZoom&&Y(e),r.enablePan&&Z(e),r.update();break;case a.TOUCH_DOLLY_ROTATE:if(!1===r.enableZoom&&!1===r.enableRotate)return;r.enableZoom&&Y(e),r.enableRotate&&J(e),r.update();break;default:o=a.NONE}}(e):function(e){if(!1!==r.enabled)switch(o){case a.ROTATE:if(!1===r.enableRotate)return;y.set(e.clientX,e.clientY),g.subVectors(y,m).multiplyScalar(r.rotateSpeed);let t=r.domElement;t&&(j(2*Math.PI*g.x/t.clientHeight),O(2*Math.PI*g.y/t.clientHeight)),m.copy(y),r.update();break;case a.DOLLY:var n,i;if(!1===r.enableZoom)return;(S.set(e.clientX,e.clientY),M.subVectors(S,w),M.y>0)?(n=T(),F(f/n)):M.y<0&&(i=T(),F(f*i)),w.copy(S),r.update();break;case a.PAN:if(!1===r.enablePan)return;b.set(e.clientX,e.clientY),v.subVectors(b,x).multiplyScalar(r.panSpeed),L(v.x,v.y),x.copy(b),r.update()}}(e))}function Q(e){var t,n,i;(function(e){delete N[e.pointerId];for(let t=0;t<A.length;t++)if(A[t].pointerId==e.pointerId)return void A.splice(t,1)})(e),0===A.length&&(null==(t=r.domElement)||t.releasePointerCapture(e.pointerId),null==(n=r.domElement)||n.ownerDocument.removeEventListener("pointermove",G),null==(i=r.domElement)||i.ownerDocument.removeEventListener("pointerup",Q)),r.dispatchEvent(s),o=a.NONE}function $(e){if(!1!==r.enabled&&!1!==r.enableZoom&&(o===a.NONE||o===a.ROTATE)){var t,n;e.preventDefault(),r.dispatchEvent(i),(V(e),e.deltaY<0)?(t=T(),F(f*t)):e.deltaY>0&&(n=T(),F(f/n)),r.update(),r.dispatchEvent(s)}}function K(e){if(!1!==r.enabled&&!1!==r.enablePan){let t=!1;switch(e.code){case r.keys.UP:L(0,r.keyPanSpeed),t=!0;break;case r.keys.BOTTOM:L(0,-r.keyPanSpeed),t=!0;break;case r.keys.LEFT:L(r.keyPanSpeed,0),t=!0;break;case r.keys.RIGHT:L(-r.keyPanSpeed,0),t=!0}t&&(e.preventDefault(),r.update())}}function ee(e){!1!==r.enabled&&e.preventDefault()}function et(e){let t=N[e.pointerId];void 0===t&&(t=new z,N[e.pointerId]=t),t.set(e.pageX,e.pageY)}function er(e){return N[(e.pointerId===A[0].pointerId?A[1]:A[0]).pointerId]}this.dollyIn=(e=T())=>{F(f*e),r.update()},this.dollyOut=(e=T())=>{F(f/e),r.update()},this.getScale=()=>f,this.setScale=e=>{F(e),r.update()},this.getZoomScale=()=>T(),void 0!==t&&this.connect(t),this.update()}}let iP=h.forwardRef(({makeDefault:e,camera:t,regress:r,domElement:n,enableDamping:i=!0,keyEvents:s=!1,onChange:a,onStart:o,onEnd:l,...u},c)=>{let d=nJ(e=>e.invalidate),f=nJ(e=>e.camera),p=nJ(e=>e.gl),m=nJ(e=>e.events),y=nJ(e=>e.setEvents),g=nJ(e=>e.set),x=nJ(e=>e.get),b=nJ(e=>e.performance),v=t||f,w=n||m.connected||p.domElement,S=h.useMemo(()=>new iC(v),[v]);return nZ(()=>{S.enabled&&S.update()},-1),h.useEffect(()=>(s&&S.connect(!0===s?w:s),S.connect(w),()=>void S.dispose()),[s,w,r,S,d]),h.useEffect(()=>{let e=e=>{d(),r&&b.regress(),a&&a(e)},t=e=>{o&&o(e)},n=e=>{l&&l(e)};return S.addEventListener("change",e),S.addEventListener("start",t),S.addEventListener("end",n),()=>{S.removeEventListener("start",t),S.removeEventListener("end",n),S.removeEventListener("change",e)}},[a,o,l,S,d,y]),h.useEffect(()=>{if(e){let e=x().controls;return g({controls:S}),()=>g({controls:e})}},[e,S]),h.createElement("primitive",im({ref:c,object:S,enableDamping:i},u))});var iE=r(29523),iA=r(44493),iN=r(62688);let iT=(0,iN.A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),ij=(0,iN.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),iO=(0,iN.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),iR=(0,iN.A)("volume-x",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]),iI=(0,iN.A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]),iL=(0,iN.A)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);var iF=r(31261),iV=r.n(iF),iB=r(49587);let iD=r.n(iB)()(async()=>{},{loadableGenerated:{modules:["components\\3d\\tour-viewer.tsx -> @react-three/fiber"]},ssr:!1,loading:()=>(0,u.jsx)("div",{className:"w-full h-full bg-black flex items-center justify-center",children:(0,u.jsxs)("div",{className:"text-white text-center",children:[(0,u.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,u.jsx)("p",{children:"Loading 3D viewer..."})]})})});function iU({imageUrl:e}){let t=(0,h.useRef)(null),r=nQ(ne,e);return nZ(()=>{t.current}),(0,u.jsxs)("mesh",{ref:t,scale:[-1,1,1],children:[(0,u.jsx)("sphereGeometry",{args:[500,60,40]}),(0,u.jsx)("meshBasicMaterial",{map:r,side:1})]})}function iW(){return(0,u.jsxs)("mesh",{children:[(0,u.jsx)("sphereGeometry",{args:[1,32,32]}),(0,u.jsx)("meshBasicMaterial",{color:"#333",wireframe:!0})]})}function iH({imageUrl:e}){return(0,u.jsxs)("div",{className:"relative w-full h-full bg-black rounded-lg overflow-hidden",children:[(0,u.jsx)(iV(),{src:e,alt:"360\xb0 Tour Preview",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,u.jsx)("div",{className:"absolute inset-0 bg-black/20 flex items-center justify-center",children:(0,u.jsxs)("div",{className:"text-white text-center",children:[(0,u.jsx)("div",{className:"w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,u.jsx)("p",{className:"text-lg font-medium",children:"360\xb0 Experience"}),(0,u.jsx)("p",{className:"text-sm opacity-80",children:"Loading immersive view..."})]})})]})}function iq({imageUrl:e,title:t,autoRotate:r=!1,showControls:n=!0}){let[i,s]=(0,h.useState)(r),[a,o]=(0,h.useState)(!0),[l,c]=(0,h.useState)(!0),d=(0,h.useRef)(null),{isTouch:f,isVRCapable:p}=function(){let[e,t]=(0,h.useState)({type:"desktop",os:"unknown",browser:"unknown",isTouch:!1,isVRCapable:!1,screenSize:{width:0,height:0},orientation:"landscape",pixelRatio:1});return e}();return(0,u.jsxs)("div",{ref:d,className:"relative w-full h-[600px] bg-black rounded-lg overflow-hidden",children:[l?(0,u.jsx)(iD,{camera:{position:[0,0,.1],fov:75,near:.1,far:1e3},gl:{antialias:!0,alpha:!1,powerPreference:"high-performance"},onCreated:()=>console.log("3D Canvas created"),onError:()=>{console.error("3D Canvas failed, falling back to 2D"),c(!1)},children:(0,u.jsxs)(h.Suspense,{fallback:(0,u.jsx)(iW,{}),children:[(0,u.jsx)(iU,{imageUrl:e}),(0,u.jsx)(iP,{enableZoom:!0,enablePan:!1,enableDamping:!0,dampingFactor:.1,autoRotate:i,autoRotateSpeed:.5,minDistance:.1,maxDistance:10,enableRotate:!0,rotateSpeed:f?.5:1,zoomSpeed:f?.5:1})]})}):(0,u.jsx)(iH,{imageUrl:e}),t&&(0,u.jsx)("div",{className:"absolute top-4 left-4 z-10",children:(0,u.jsx)(iA.Zp,{className:"bg-black/50 backdrop-blur-sm border-white/20",children:(0,u.jsx)("div",{className:"p-3",children:(0,u.jsx)("h3",{className:"text-white font-medium",children:t})})})}),n&&(0,u.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10",children:(0,u.jsx)(iA.Zp,{className:"bg-black/50 backdrop-blur-sm border-white/20",children:(0,u.jsxs)("div",{className:"flex items-center space-x-2 p-2",children:[(0,u.jsx)(iE.$,{size:"sm",variant:"ghost",onClick:()=>{s(!i)},className:"text-white hover:bg-white/20",children:i?(0,u.jsx)(iT,{className:"h-4 w-4"}):(0,u.jsx)(ij,{className:"h-4 w-4"})}),(0,u.jsx)(iE.$,{size:"sm",variant:"ghost",onClick:()=>{},className:"text-white hover:bg-white/20",children:(0,u.jsx)(iO,{className:"h-4 w-4"})}),(0,u.jsx)(iE.$,{size:"sm",variant:"ghost",onClick:()=>{o(!a)},className:"text-white hover:bg-white/20",children:a?(0,u.jsx)(iR,{className:"h-4 w-4"}):(0,u.jsx)(iI,{className:"h-4 w-4"})}),(0,u.jsx)(iE.$,{size:"sm",variant:"ghost",onClick:()=>{document.fullscreenElement?document.exitFullscreen():d.current?.requestFullscreen()},className:"text-white hover:bg-white/20",children:(0,u.jsx)(iL,{className:"h-4 w-4"})}),p&&(0,u.jsx)(iE.$,{size:"sm",variant:"ghost",className:"text-white hover:bg-white/20",children:"VR"})]})})}),f&&l&&(0,u.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 pointer-events-none",children:(0,u.jsx)(iA.Zp,{className:"bg-black/50 backdrop-blur-sm border-white/20 animate-pulse",children:(0,u.jsx)("div",{className:"p-4 text-center",children:(0,u.jsx)("p",{className:"text-white text-sm",children:"Drag to look around • Pinch to zoom"})})})}),(0,u.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50 z-0",children:(0,u.jsxs)("div",{className:"text-center text-white",children:[(0,u.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,u.jsx)("p",{children:"Loading immersive experience..."})]})})]})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80210:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(26373).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},81630:e=>{"use strict";e.exports=require("http")},85838:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(26373).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return h},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return o}});let n=r(52836),i=r(49026),s=r(19121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let s=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",s}function o(e,t){var r;throw null!=t||(t=(null==s||null==(r=s.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),a(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=i.RedirectType.replace),a(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function h(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89513:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.HeadManagerContext},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97374:(e,t)=>{"use strict";function r(e,t){var r=e.length;for(e.push(t);0<r;){var n=r-1>>>1,i=e[n];if(0<s(i,t))e[n]=t,e[r]=i,r=n;else break}}function n(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],r=e.pop();if(r!==t){e[0]=r;for(var n=0,i=e.length,a=i>>>1;n<a;){var o=2*(n+1)-1,l=e[o],u=o+1,h=e[u];if(0>s(l,r))u<i&&0>s(h,l)?(e[n]=h,e[u]=r,n=u):(e[n]=l,e[o]=r,n=o);else if(u<i&&0>s(h,r))e[n]=h,e[u]=r,n=u;else break}}return t}function s(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,o=performance;t.unstable_now=function(){return o.now()}}else{var l=Date,u=l.now();t.unstable_now=function(){return l.now()-u}}var h=[],c=[],d=1,f=null,p=3,m=!1,y=!1,g=!1,x="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function w(e){for(var t=n(c);null!==t;){if(null===t.callback)i(c);else if(t.startTime<=e)i(c),t.sortIndex=t.expirationTime,r(h,t);else break;t=n(c)}}function S(e){if(g=!1,w(e),!y)if(null!==n(h))y=!0,N();else{var t=n(c);null!==t&&T(S,t.startTime-e)}}var M=!1,_=-1,z=5,k=-1;function C(){return!(t.unstable_now()-k<z)}function P(){if(M){var e=t.unstable_now();k=e;var r=!0;try{e:{y=!1,g&&(g=!1,b(_),_=-1),m=!0;var s=p;try{t:{for(w(e),f=n(h);null!==f&&!(f.expirationTime>e&&C());){var o=f.callback;if("function"==typeof o){f.callback=null,p=f.priorityLevel;var l=o(f.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof l){f.callback=l,w(e),r=!0;break t}f===n(h)&&i(h),w(e)}else i(h);f=n(h)}if(null!==f)r=!0;else{var u=n(c);null!==u&&T(S,u.startTime-e),r=!1}}break e}finally{f=null,p=s,m=!1}}}finally{r?a():M=!1}}}if("function"==typeof v)a=function(){v(P)};else if("undefined"!=typeof MessageChannel){var E=new MessageChannel,A=E.port2;E.port1.onmessage=P,a=function(){A.postMessage(null)}}else a=function(){x(P,0)};function N(){M||(M=!0,a())}function T(e,r){_=x(function(){e(t.unstable_now())},r)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){y||m||(y=!0,N())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return n(h)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var r=p;p=t;try{return e()}finally{p=r}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=p;p=e;try{return t()}finally{p=r}},t.unstable_scheduleCallback=function(e,i,s){var a=t.unstable_now();switch(s="object"==typeof s&&null!==s&&"number"==typeof(s=s.delay)&&0<s?a+s:a,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=0x3fffffff;break;case 4:o=1e4;break;default:o=5e3}return o=s+o,e={id:d++,callback:i,priorityLevel:e,startTime:s,expirationTime:o,sortIndex:-1},s>a?(e.sortIndex=s,r(c,e),null===n(h)&&e===n(c)&&(g?(b(_),_=-1):g=!0,T(S,s-a))):(e.sortIndex=o,r(h,e),y||m||(y=!0,N())),e},t.unstable_shouldYield=C,t.unstable_wrapCallback=function(e){var t=p;return function(){var r=p;p=t;try{return e.apply(this,arguments)}finally{p=r}}}},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return h},RedirectType:function(){return i.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return s.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(86897),i=r(49026),s=r(62765),a=r(48976),o=r(70899),l=r(163);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class h extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,739,72,958,215,322],()=>r(43619));module.exports=n})();