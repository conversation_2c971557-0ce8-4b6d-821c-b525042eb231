(()=>{var e={};e.id=630,e.ids=[630],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return s}});let s=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1577:(e,t,r)=>{Promise.resolve().then(r.bind(r,11096)),Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,10167)),Promise.resolve().then(r.bind(r,22230))},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:i,quality:a}=e,n=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+i+"&q="+n+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10167:(e,t,r)=>{"use strict";r.d(t,{TourViewer:()=>v});var s=r(60687),i=r(43210),a=r(29523),n=r(44493),o=r(62688);let l=(0,o.A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),d=(0,o.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),c=(0,o.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),u=(0,o.A)("volume-x",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]),p=(0,o.A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]),f=(0,o.A)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);var m=r(31261),h=r.n(m);function x({imageUrl:e}){return(0,s.jsxs)("div",{className:"relative w-full h-full bg-black rounded-lg overflow-hidden",children:[(0,s.jsx)(h(),{src:e,alt:"360\xb0 Tour Preview",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-white text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"360\xb0 Experience"}),(0,s.jsx)("p",{className:"text-sm opacity-80",children:"Loading immersive view..."})]})})]})}function v({imageUrl:e,title:t,autoRotate:r=!1,showControls:o=!0}){let[m,h]=(0,i.useState)(r),[v,g]=(0,i.useState)(!0);return(0,s.jsxs)("div",{className:"relative w-full h-[600px] bg-black rounded-lg overflow-hidden",children:[(0,s.jsx)(x,{imageUrl:e}),t&&(0,s.jsx)("div",{className:"absolute top-4 left-4 z-10",children:(0,s.jsx)(n.Zp,{className:"bg-black/50 backdrop-blur-sm border-white/20",children:(0,s.jsx)("div",{className:"p-3",children:(0,s.jsx)("h3",{className:"text-white font-medium",children:t})})})}),o&&(0,s.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10",children:(0,s.jsx)(n.Zp,{className:"bg-black/50 backdrop-blur-sm border-white/20",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 p-2",children:[(0,s.jsx)(a.$,{size:"sm",variant:"ghost",onClick:()=>{h(!m)},className:"text-white hover:bg-white/20",children:m?(0,s.jsx)(l,{className:"h-4 w-4"}):(0,s.jsx)(d,{className:"h-4 w-4"})}),(0,s.jsx)(a.$,{size:"sm",variant:"ghost",onClick:()=>{},className:"text-white hover:bg-white/20",children:(0,s.jsx)(c,{className:"h-4 w-4"})}),(0,s.jsx)(a.$,{size:"sm",variant:"ghost",onClick:()=>{g(!v)},className:"text-white hover:bg-white/20",children:v?(0,s.jsx)(u,{className:"h-4 w-4"}):(0,s.jsx)(p,{className:"h-4 w-4"})}),(0,s.jsx)(a.$,{size:"sm",variant:"ghost",onClick:()=>{console.log("Fullscreen toggle")},className:"text-white hover:bg-white/20",children:(0,s.jsx)(f,{className:"h-4 w-4"})}),isVRCapable&&(0,s.jsx)(a.$,{size:"sm",variant:"ghost",className:"text-white hover:bg-white/20",children:"VR"})]})})}),(0,s.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 pointer-events-none",children:(0,s.jsx)(n.Zp,{className:"bg-black/50 backdrop-blur-sm border-white/20 animate-pulse",children:(0,s.jsx)("div",{className:"p-4 text-center",children:(0,s.jsx)("p",{className:"text-white text-sm",children:"Drag to look around • Pinch to zoom"})})})}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50 z-0",children:(0,s.jsxs)("div",{className:"text-center text-white",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,s.jsx)("p",{children:"Loading immersive experience..."})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},14959:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.AmpContext},17903:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.ImageConfigContext},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24464:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k,generateMetadata:()=>E});var s=r(37413),i=r(82473),a=r(78963),n=r(30084),o=r(61120),l=r(75321),d=r(10974);let c=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(l.Root,{ref:r,className:(0,d.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));c.displayName=l.Root.displayName;let u=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(l.Image,{ref:r,className:(0,d.cn)("aspect-square h-full w-full",e),...t}));u.displayName=l.Image.displayName;let p=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(l.Fallback,{ref:r,className:(0,d.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));p.displayName=l.Fallback.displayName;var f=r(97680),m=r(53263),h=r(49046),x=r(1215),v=r(85838),g=r(80210),b=r(26373);let y=(0,b.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),j=(0,b.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var w=r(90230);let _=(0,b.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),R=(0,b.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var N=r(97576);let C={id:"1",title:"Luxury 3-Bedroom Apartment in Victoria Island",description:"Experience this stunning luxury apartment featuring modern amenities, breathtaking views of Lagos lagoon, and premium finishes throughout. Perfect for executives and families looking for upscale living in the heart of Lagos.",slug:"luxury-apartment-victoria-island",category:"Real Estate",location:"Victoria Island, Lagos",address:"123 Ahmadu Bello Way, Victoria Island, Lagos State",price:25e5,currency:"NGN",views:2847,likes:89,shares:12,createdAt:"2024-01-15",user:{id:"user1",name:"John Doe",avatar:"/avatars/john.jpg",company:"Premium Properties Lagos"},scenes:[{id:"scene1",title:"Living Room",imageUrl:"/sample-360/living-room.jpg",isStarting:!0},{id:"scene2",title:"Master Bedroom",imageUrl:"/sample-360/bedroom.jpg",isStarting:!1},{id:"scene3",title:"Kitchen",imageUrl:"/sample-360/kitchen.jpg",isStarting:!1}],features:["3 Bedrooms, 2 Bathrooms","Fully Furnished","24/7 Security","Swimming Pool","Gym Access","Parking Space","Generator Backup","High-Speed Internet"]};async function E({params:e}){return C?{title:C.title,description:C.description,openGraph:{title:C.title,description:C.description,type:"website",images:[{url:C.scenes[0]?.imageUrl||"/og-image.jpg",width:1200,height:630,alt:C.title}]}}:{title:"Tour Not Found"}}function k({params:e}){C&&C.slug===e.slug||(0,N.notFound)();let t=C.scenes.find(e=>e.isStarting)||C.scenes[0];return(0,s.jsx)(f.s,{children:(0,s.jsx)("div",{className:"py-8",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(m.TourViewer,{imageUrl:"/sample-360.jpg",title:t.title,autoRotate:!1,showControls:!0})}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(n.E,{variant:"secondary",className:"mb-2",children:C.category}),(0,s.jsx)(a.ZB,{className:"text-2xl",children:C.title}),(0,s.jsxs)(a.BT,{className:"flex items-center mt-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-1"}),C.location]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-primary",children:["₦",C.price.toLocaleString()]}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"per month"})]})]})}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:C.description}),(0,s.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-muted-foreground",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-1"}),C.views.toLocaleString()," views"]}),(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-1"}),C.likes," likes"]}),(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 mr-1"}),C.shares," shares"]}),(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(y,{className:"h-4 w-4 mr-1"}),new Date(C.createdAt).toLocaleDateString()]})]})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{children:(0,s.jsx)(a.ZB,{children:"Features & Amenities"})}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2",children:C.features.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-2"}),e]},t))})})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(a.ZB,{children:"Tour Scenes"}),(0,s.jsx)(a.BT,{children:"Explore different areas of this property"})]}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:C.scenes.map(e=>(0,s.jsxs)("div",{className:`relative aspect-video bg-muted rounded-lg overflow-hidden cursor-pointer border-2 transition-colors ${e.isStarting?"border-primary":"border-transparent hover:border-primary/50"}`,children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)(x.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,s.jsx)("div",{className:"absolute bottom-2 left-2 right-2",children:(0,s.jsx)("div",{className:"bg-black/50 backdrop-blur-sm rounded px-2 py-1",children:(0,s.jsx)("p",{className:"text-white text-sm font-medium",children:e.title})})})]},e.id))})})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{children:(0,s.jsxs)(a.ZB,{className:"flex items-center",children:[(0,s.jsx)(j,{className:"h-5 w-5 mr-2"}),"Created by"]})}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,s.jsxs)(c,{children:[(0,s.jsx)(u,{src:C.user.avatar,alt:C.user.name}),(0,s.jsx)(p,{children:C.user.name.split(" ").map(e=>e[0]).join("")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:C.user.name}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:C.user.company})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(i.$,{className:"w-full",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Contact via WhatsApp"]}),(0,s.jsxs)(i.$,{variant:"outline",className:"w-full",children:[(0,s.jsx)(_,{className:"h-4 w-4 mr-2"}),"View Profile"]})]})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{children:(0,s.jsx)(a.ZB,{children:"Actions"})}),(0,s.jsxs)(a.Wu,{className:"space-y-2",children:[(0,s.jsxs)(i.$,{variant:"outline",className:"w-full",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Save to Favorites"]}),(0,s.jsxs)(i.$,{variant:"outline",className:"w-full",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Share Tour"]}),(0,s.jsxs)(i.$,{variant:"outline",className:"w-full",children:[(0,s.jsx)(R,{className:"h-4 w-4 mr-2"}),"Download VR"]})]})]}),(0,s.jsxs)(a.Zp,{children:[(0,s.jsx)(a.aR,{children:(0,s.jsxs)(a.ZB,{className:"flex items-center",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Location"]})}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:C.address}),(0,s.jsx)("div",{className:"aspect-video bg-muted rounded-lg flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"Map View"})})]})]})]})]})]})})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30084:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(37413);r(61120);var i=r(50662),a=r(10974);let n=(0,i.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,a.cn)(n({variant:t}),e),...r})}},30512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return h},defaultHead:function(){return u}});let s=r(14985),i=r(40740),a=r(60687),n=i._(r(43210)),o=s._(r(47755)),l=r(14959),d=r(89513),c=r(34604);function u(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===n.default.Fragment?e.concat(n.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(50148);let f=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,s={};return i=>{let a=!0,n=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){n=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?a=!1:t.add(i.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(i.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?a=!1:r.add(t);else{let e=i.props[t],r=s[t]||new Set;("name"!==t||!n)&&r.has(e)?a=!1:(r.add(e),s[t]=r)}}}return a}}()).reverse().map((e,t)=>{let s=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,n.default.cloneElement(e,t)}return n.default.cloneElement(e,{key:s})})}let h=function(e){let{children:t}=e,r=(0,n.useContext)(l.AmpStateContext),s=(0,n.useContext)(d.HeadManagerContext);return(0,a.jsx)(o.default,{reduceComponentsToState:m,headManager:s,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let s=r(14985),i=r(44953),a=r(46533),n=s._(r(1933));function o(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=a.Image},33873:e=>{"use strict";e.exports=require("path")},34604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:s=!1}=void 0===e?{}:e;return t||r&&s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},34631:e=>{"use strict";e.exports=require("tls")},41480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:i,blurDataURL:a,objectFit:n}=e,o=s?40*s:t,l=i?40*i:r,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},43619:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["tours",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,24464)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\tours\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\tours\\[slug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/tours/[slug]/page",pathname:"/tours/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},44953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(50148);let s=r(41480),i=r(12756),a=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let d,c,u,{src:p,sizes:f,unoptimized:m=!1,priority:h=!1,loading:x,className:v,quality:g,width:b,height:y,fill:j=!1,style:w,overrideSrc:_,onLoad:R,onLoadingComplete:N,placeholder:C="empty",blurDataURL:E,fetchPriority:k,decoding:O="async",layout:P,objectFit:A,objectPosition:S,lazyBoundary:M,lazyRoot:T,...D}=e,{imgConf:z,showAltText:I,blurComplete:F,defaultLoader:q}=t,L=z||i.imageConfigDefault;if("allSizes"in L)d=L;else{let e=[...L.deviceSizes,...L.imageSizes].sort((e,t)=>e-t),t=L.deviceSizes.sort((e,t)=>e-t),s=null==(r=L.qualities)?void 0:r.sort((e,t)=>e-t);d={...L,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===q)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let V=D.loader||q;delete D.loader,delete D.srcSet;let U="__next_img_default"in V;if(U){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+p+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=V;V=t=>{let{config:r,...s}=t;return e(s)}}if(P){"fill"===P&&(j=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[P];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[P];t&&!f&&(f=t)}let B="",G=o(b),K=o(y);if((l=p)&&"object"==typeof l&&(n(l)||void 0!==l.src)){let e=n(p)?p.default:p;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,E=E||e.blurDataURL,B=e.src,!j)if(G||K){if(G&&!K){let t=G/e.width;K=Math.round(e.height*t)}else if(!G&&K){let t=K/e.height;G=Math.round(e.width*t)}}else G=e.width,K=e.height}let Z=!h&&("lazy"===x||void 0===x);(!(p="string"==typeof p?p:B)||p.startsWith("data:")||p.startsWith("blob:"))&&(m=!0,Z=!1),d.unoptimized&&(m=!0),U&&!d.dangerouslyAllowSVG&&p.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=o(g),W=Object.assign(j?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:A,objectPosition:S}:{},I?{}:{color:"transparent"},w),$=F||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:G,heightInt:K,blurWidth:c,blurHeight:u,blurDataURL:E||"",objectFit:W.objectFit})+'")':'url("'+C+'")',X=a.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,J=$?{backgroundSize:X,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:$}:{},Y=function(e){let{config:t,src:r,unoptimized:s,width:i,quality:a,sizes:n,loader:o}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:s,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,n),c=l.length-1;return{sizes:n||"w"!==d?n:"100vw",srcSet:l.map((e,s)=>o({config:t,src:r,quality:a,width:e})+" "+("w"===d?e:s+1)+d).join(", "),src:o({config:t,src:r,quality:a,width:l[c]})}}({config:d,src:p,unoptimized:m,width:G,quality:H,sizes:f,loader:V});return{props:{...D,loading:Z?"lazy":x,fetchPriority:k,width:G,height:K,decoding:O,className:v,style:{...W,...J},sizes:Y.sizes,srcSet:Y.srcSet,src:_||Y.src},meta:{unoptimized:m,priority:h,placeholder:C,fill:j}}}},46533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let s=r(14985),i=r(40740),a=r(60687),n=i._(r(43210)),o=s._(r(51215)),l=s._(r(30512)),d=r(44953),c=r(12756),u=r(17903);r(50148);let p=r(69148),f=s._(r(1933)),m=r(53038),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function x(e,t,r,s,i,a,n){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let s=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>s,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{s=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==s?void 0:s.current)&&s.current(e)}}))}function v(e){return n.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let g=(0,n.forwardRef)((e,t)=>{let{src:r,srcSet:s,sizes:i,height:o,width:l,decoding:d,className:c,style:u,fetchPriority:p,placeholder:f,loading:h,unoptimized:g,fill:b,onLoadRef:y,onLoadingCompleteRef:j,setBlurComplete:w,setShowAltText:_,sizesInput:R,onLoad:N,onError:C,...E}=e,k=(0,n.useCallback)(e=>{e&&(C&&(e.src=e.src),e.complete&&x(e,f,y,j,w,g,R))},[r,f,y,j,w,C,g,R]),O=(0,m.useMergedRef)(t,k);return(0,a.jsx)("img",{...E,...v(p),loading:h,width:l,height:o,decoding:d,"data-nimg":b?"fill":"1",className:c,style:u,sizes:i,srcSet:s,src:r,ref:O,onLoad:e=>{x(e.currentTarget,f,y,j,w,g,R)},onError:e=>{_(!0),"empty"!==f&&w(!0),C&&C(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,s={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...v(r.fetchPriority)};return t&&o.default.preload?(o.default.preload(r.src,s),null):(0,a.jsx)(l.default,{children:(0,a.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...s},"__nimg-"+r.src+r.srcSet+r.sizes)})}let y=(0,n.forwardRef)((e,t)=>{let r=(0,n.useContext)(p.RouterContext),s=(0,n.useContext)(u.ImageConfigContext),i=(0,n.useMemo)(()=>{var e;let t=h||s||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:i,qualities:a}},[s]),{onLoad:o,onLoadingComplete:l}=e,m=(0,n.useRef)(o);(0,n.useEffect)(()=>{m.current=o},[o]);let x=(0,n.useRef)(l);(0,n.useEffect)(()=>{x.current=l},[l]);let[v,y]=(0,n.useState)(!1),[j,w]=(0,n.useState)(!1),{props:_,meta:R}=(0,d.getImgProps)(e,{defaultLoader:f.default,imgConf:i,blurComplete:v,showAltText:j});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g,{..._,unoptimized:R.unoptimized,placeholder:R.placeholder,fill:R.fill,onLoadRef:m,onLoadingCompleteRef:x,setBlurComplete:y,setShowAltText:w,sizesInput:e.sizes,ref:t}),R.priority?(0,a.jsx)(b,{isAppRouter:!r,imgAttributes:_}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let s=r(43210),i=()=>{},a=()=>{};function n(e){var t;let{headManager:r,reduceComponentsToState:n}=e;function o(){if(r&&r.mountedInstances){let t=s.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(n(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),o(),i(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),i(()=>(r&&(r._pendingUpdate=o),()=>{r&&(r._pendingUpdate=o)})),a(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},48976:(e,t,r)=>{"use strict";function s(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return s}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53263:(e,t,r)=>{"use strict";r.d(t,{TourViewer:()=>i});var s=r(12907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call TourViewer() from the server but TourViewer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\3d\\tour-viewer.tsx","TourViewer");(0,s.registerClientReference)(function(){throw Error("Attempted to call TourPreview() from the server but TourPreview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\3d\\tour-viewer.tsx","TourPreview")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59721:(e,t,r)=>{Promise.resolve().then(r.bind(r,75321)),Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,53263)),Promise.resolve().then(r.bind(r,10590))},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let s=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(s),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=s,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69148:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.RouterContext},70899:(e,t,r)=>{"use strict";function s(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return s}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,n.isNextRouterError)(t)||(0,a.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,s.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let s=r(68388),i=r(52637),a=r(51846),n=r(31162),o=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74075:e=>{"use strict";e.exports=require("zlib")},75321:(e,t,r)=>{"use strict";r.d(t,{Fallback:()=>i,Image:()=>a,Root:()=>n});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","Avatar"),(0,s.registerClientReference)(function(){throw Error("Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","AvatarFallback"),(0,s.registerClientReference)(function(){throw Error("Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","AvatarImage");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call Fallback() from the server but Fallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","Fallback"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call Image() from the server but Image is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","Image"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call Root() from the server but Root is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","Root");(0,s.registerClientReference)(function(){throw Error("Attempted to call createAvatarScope() from the server but createAvatarScope is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-avatar\\dist\\index.mjs","createAvatarScope")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80210:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},81630:e=>{"use strict";e.exports=require("http")},85838:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return n},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return l},redirect:function(){return o}});let s=r(52836),i=r(49026),a=r(19121).actionAsyncStorage;function n(e,t,r){void 0===r&&(r=s.RedirectStatusCode.TemporaryRedirect);let a=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",a}function o(e,t){var r;throw null!=t||(t=(null==a||null==(r=a.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),n(e,t,s.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=i.RedirectType.replace),n(e,t,s.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89513:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.HeadManagerContext},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return i.RedirectType},forbidden:function(){return n.forbidden},notFound:function(){return a.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let s=r(86897),i=r(49026),a=r(62765),n=r(48976),o=r(70899),l=r(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,739,72,958,215,322],()=>r(43619));module.exports=s})();