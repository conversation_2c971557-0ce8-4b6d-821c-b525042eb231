'use client';

import { Suspense, useRef, useState } from 'react';
import { Canvas, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, Html } from '@react-three/drei';
import { TextureLoader, BackSide } from 'three';
import * as THREE from 'three';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Play, Pause, RotateCcw, Maximize, VolumeX, Volume2 } from 'lucide-react';
import { useDeviceDetection } from '@/hooks/use-device-detection';

interface TourViewerProps {
  imageUrl: string;
  title?: string;
  autoRotate?: boolean;
  showControls?: boolean;
  onSceneChange?: (sceneId: string) => void;
}

function Sphere360({ imageUrl }: { imageUrl: string }) {
  const meshRef = useRef<THREE.Mesh>(null);
  const texture = useLoader(TextureLoader, imageUrl);

  useFrame((state, delta) => {
    if (meshRef.current) {
      // Optional: Add subtle rotation
      // meshRef.current.rotation.y += delta * 0.1;
    }
  });

  return (
    <mesh ref={meshRef} scale={[-1, 1, 1]}>
      <sphereGeometry args={[500, 60, 40]} />
      <meshBasicMaterial map={texture} side={BackSide} />
    </mesh>
  );
}

function LoadingFallback() {
  return (
    <Html center>
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
        <p className="text-sm text-muted-foreground">Loading 360° view...</p>
      </div>
    </Html>
  );
}

export function TourViewer({
  imageUrl,
  title,
  autoRotate = false,
  showControls = true,
  onSceneChange
}: TourViewerProps) {
  const [isPlaying, setIsPlaying] = useState(autoRotate);
  const [isMuted, setIsMuted] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isTouch, isVRCapable } = useDeviceDetection();

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const resetView = () => {
    // This would reset the camera position
    // Implementation depends on camera controls
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  return (
    <div
      ref={containerRef}
      className="relative w-full h-[600px] bg-black rounded-lg overflow-hidden"
    >
      {/* 3D Canvas */}
      <Canvas
        camera={{
          position: [0, 0, 0.1],
          fov: 75,
          near: 0.1,
          far: 1000
        }}
        gl={{
          antialias: true,
          alpha: false,
          powerPreference: "high-performance"
        }}
      >
        <Suspense fallback={<LoadingFallback />}>
          <Sphere360 imageUrl={imageUrl} />
          <OrbitControls
            enableZoom={true}
            enablePan={false}
            enableDamping={true}
            dampingFactor={0.1}
            autoRotate={isPlaying}
            autoRotateSpeed={0.5}
            minDistance={0.1}
            maxDistance={10}
            enableRotate={true}
            rotateSpeed={isTouch ? 0.5 : 1}
            zoomSpeed={isTouch ? 0.5 : 1}
          />
        </Suspense>
      </Canvas>

      {/* Title Overlay */}
      {title && (
        <div className="absolute top-4 left-4 z-10">
          <Card className="bg-black/50 backdrop-blur-sm border-white/20">
            <div className="p-3">
              <h3 className="text-white font-medium">{title}</h3>
            </div>
          </Card>
        </div>
      )}

      {/* Controls Overlay */}
      {showControls && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
          <Card className="bg-black/50 backdrop-blur-sm border-white/20">
            <div className="flex items-center space-x-2 p-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={togglePlay}
                className="text-white hover:bg-white/20"
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={resetView}
                className="text-white hover:bg-white/20"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={toggleMute}
                className="text-white hover:bg-white/20"
              >
                {isMuted ? (
                  <VolumeX className="h-4 w-4" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={toggleFullscreen}
                className="text-white hover:bg-white/20"
              >
                <Maximize className="h-4 w-4" />
              </Button>

              {isVRCapable && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                >
                  VR
                </Button>
              )}
            </div>
          </Card>
        </div>
      )}

      {/* Mobile Instructions */}
      {isTouch && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 pointer-events-none">
          <Card className="bg-black/50 backdrop-blur-sm border-white/20 animate-pulse">
            <div className="p-4 text-center">
              <p className="text-white text-sm">
                Drag to look around • Pinch to zoom
              </p>
            </div>
          </Card>
        </div>
      )}

      {/* Loading State */}
      <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-0">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>Loading immersive experience...</p>
        </div>
      </div>
    </div>
  );
}

// Simplified viewer for previews
export function TourPreview({ imageUrl, className }: { imageUrl: string; className?: string }) {
  return (
    <div className={`relative aspect-video bg-black rounded-lg overflow-hidden ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 0.1], fov: 75 }}
        gl={{ antialias: false, alpha: false }}
      >
        <Suspense fallback={null}>
          <Sphere360 imageUrl={imageUrl} />
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            autoRotate={true}
            autoRotateSpeed={1}
            enableDamping={true}
          />
        </Suspense>
      </Canvas>

      <div className="absolute inset-0 flex items-center justify-center">
        <Button className="bg-black/50 hover:bg-black/70 text-white">
          <Play className="h-4 w-4 mr-2" />
          View 360°
        </Button>
      </div>
    </div>
  );
}
