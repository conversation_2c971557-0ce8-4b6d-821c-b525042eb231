'use client';

import { Suspense, useRef, useState } from 'react';
import { use<PERSON>rame, useLoader } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { TextureLoader, BackSide } from 'three';
import * as THREE from 'three';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Play, Pause, RotateCcw, Maximize, VolumeX, Volume2 } from 'lucide-react';
import { useDeviceDetection } from '@/hooks/use-device-detection';
import Image from 'next/image';
import dynamic from 'next/dynamic';

// Dynamically import Canvas to avoid SSR issues
const DynamicCanvas = dynamic(() => import('@react-three/fiber').then(mod => ({ default: mod.Canvas })), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-black flex items-center justify-center">
      <div className="text-white text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
        <p>Loading 3D viewer...</p>
      </div>
    </div>
  )
});

interface TourViewerProps {
  imageUrl: string;
  title?: string;
  autoRotate?: boolean;
  showControls?: boolean;
  onSceneChange?: (sceneId: string) => void;
}

function Sphere360({ imageUrl }: { imageUrl: string }) {
  const meshRef = useRef<THREE.Mesh>(null);
  const texture = useLoader(TextureLoader, imageUrl);

  useFrame(() => {
    if (meshRef.current) {
      // Optional: Add subtle rotation
      // meshRef.current.rotation.y += 0.01;
    }
  });

  return (
    <mesh ref={meshRef} scale={[-1, 1, 1]}>
      <sphereGeometry args={[500, 60, 40]} />
      <meshBasicMaterial map={texture} side={BackSide} />
    </mesh>
  );
}

function LoadingFallback() {
  return (
    <mesh>
      <sphereGeometry args={[1, 32, 32]} />
      <meshBasicMaterial color="#333" wireframe />
    </mesh>
  );
}

// Fallback component for when 3D fails to load
function Sphere360Placeholder({ imageUrl }: { imageUrl: string }) {
  return (
    <div className="relative w-full h-full bg-black rounded-lg overflow-hidden">
      <Image
        src={imageUrl}
        alt="360° Tour Preview"
        fill
        className="object-cover"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
      <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-lg font-medium">360° Experience</p>
          <p className="text-sm opacity-80">Loading immersive view...</p>
        </div>
      </div>
    </div>
  );
}

export function TourViewer({
  imageUrl,
  title,
  autoRotate = false,
  showControls = true
}: TourViewerProps) {
  const [isPlaying, setIsPlaying] = useState(autoRotate);
  const [isMuted, setIsMuted] = useState(true);
  const [use3D, setUse3D] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isTouch, isVRCapable } = useDeviceDetection();

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const resetView = () => {
    // This would reset the camera position
    // Implementation depends on camera controls
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  return (
    <div
      ref={containerRef}
      className="relative w-full h-[600px] bg-black rounded-lg overflow-hidden"
    >
      {/* 3D Canvas */}
      {use3D ? (
        <DynamicCanvas
          camera={{
            position: [0, 0, 0.1],
            fov: 75,
            near: 0.1,
            far: 1000
          }}
          gl={{
            antialias: true,
            alpha: false,
            powerPreference: "high-performance"
          }}
          onCreated={() => console.log('3D Canvas created')}
          onError={() => {
            console.error('3D Canvas failed, falling back to 2D');
            setUse3D(false);
          }}
        >
          <Suspense fallback={<LoadingFallback />}>
            <Sphere360 imageUrl={imageUrl} />
            <OrbitControls
              enableZoom={true}
              enablePan={false}
              enableDamping={true}
              dampingFactor={0.1}
              autoRotate={isPlaying}
              autoRotateSpeed={0.5}
              minDistance={0.1}
              maxDistance={10}
              enableRotate={true}
              rotateSpeed={isTouch ? 0.5 : 1}
              zoomSpeed={isTouch ? 0.5 : 1}
            />
          </Suspense>
        </DynamicCanvas>
      ) : (
        <Sphere360Placeholder imageUrl={imageUrl} />
      )}

      {/* Title Overlay */}
      {title && (
        <div className="absolute top-4 left-4 z-10">
          <Card className="bg-black/50 backdrop-blur-sm border-white/20">
            <div className="p-3">
              <h3 className="text-white font-medium">{title}</h3>
            </div>
          </Card>
        </div>
      )}

      {/* Controls Overlay */}
      {showControls && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
          <Card className="bg-black/50 backdrop-blur-sm border-white/20">
            <div className="flex items-center space-x-2 p-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={togglePlay}
                className="text-white hover:bg-white/20"
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={resetView}
                className="text-white hover:bg-white/20"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={toggleMute}
                className="text-white hover:bg-white/20"
              >
                {isMuted ? (
                  <VolumeX className="h-4 w-4" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={toggleFullscreen}
                className="text-white hover:bg-white/20"
              >
                <Maximize className="h-4 w-4" />
              </Button>

              {isVRCapable && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                >
                  VR
                </Button>
              )}
            </div>
          </Card>
        </div>
      )}

      {/* Mobile Instructions */}
      {isTouch && use3D && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 pointer-events-none">
          <Card className="bg-black/50 backdrop-blur-sm border-white/20 animate-pulse">
            <div className="p-4 text-center">
              <p className="text-white text-sm">
                Drag to look around • Pinch to zoom
              </p>
            </div>
          </Card>
        </div>
      )}

      {/* Loading State */}
      <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-0">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>Loading immersive experience...</p>
        </div>
      </div>
    </div>
  );
}

// Simplified viewer for previews
export function TourPreview({ imageUrl, className }: { imageUrl: string; className?: string }) {
  const [use3D, setUse3D] = useState(true);

  return (
    <div className={`relative aspect-video bg-black rounded-lg overflow-hidden ${className}`}>
      {use3D ? (
        <DynamicCanvas
          camera={{ position: [0, 0, 0.1], fov: 75 }}
          gl={{ antialias: false, alpha: false }}
          onError={() => setUse3D(false)}
        >
          <Suspense fallback={null}>
            <Sphere360 imageUrl={imageUrl} />
            <OrbitControls
              enableZoom={false}
              enablePan={false}
              autoRotate={true}
              autoRotateSpeed={1}
              enableDamping={true}
            />
          </Suspense>
        </DynamicCanvas>
      ) : (
        <Image
          src={imageUrl}
          alt="Tour Preview"
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      )}

      <div className="absolute inset-0 flex items-center justify-center bg-black/20">
        <Button className="bg-black/50 hover:bg-black/70 text-white">
          <Play className="h-4 w-4 mr-2" />
          View 360°
        </Button>
      </div>
    </div>
  );
}
