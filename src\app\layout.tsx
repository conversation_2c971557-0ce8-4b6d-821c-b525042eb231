import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { cn } from "@/lib/utils";
import { Providers } from "@/components/providers";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: {
    default: "VirtualRealTour - Premium 360° Virtual Tours for Nigeria",
    template: "%s | VirtualRealTour",
  },
  description: "Create stunning 360° virtual tours for real estate, education, hospitality, and more. Premium virtual tour platform designed for the Nigerian market.",
  keywords: [
    "virtual tour",
    "360 tour",
    "real estate",
    "Nigeria",
    "virtual reality",
    "property tour",
    "immersive experience",
  ],
  authors: [{ name: "VirtualRealTour Team" }],
  creator: "VirtualRealTour",
  publisher: "VirtualRealTour",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"),
  openGraph: {
    type: "website",
    locale: "en_NG",
    url: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
    siteName: "VirtualRealTour",
    title: "VirtualRealTour - Premium 360° Virtual Tours for Nigeria",
    description: "Create stunning 360° virtual tours for real estate, education, hospitality, and more.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "VirtualRealTour Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "VirtualRealTour - Premium 360° Virtual Tours for Nigeria",
    description: "Create stunning 360° virtual tours for real estate, education, hospitality, and more.",
    images: ["/og-image.jpg"],
    creator: "@virtualrealtour",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          inter.variable
        )}
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
