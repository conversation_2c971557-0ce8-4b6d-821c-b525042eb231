#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Install supabase CLI if not already installed
try {
  execSync('supabase --version', { stdio: 'ignore' });
} catch (error) {
  console.log('Installing Supabase CLI...');
  execSync('npm install -g supabase', { stdio: 'inherit' });
}

// Generate types
console.log('Generating TypeScript types from Supabase...');

try {
  const command = `supabase gen types typescript --project-id ${process.env.SUPABASE_PROJECT_ID || 'your-project-id'} --schema public`;
  const types = execSync(command, { encoding: 'utf8' });
  
  // Write types to file
  const typesPath = path.join(__dirname, '../src/types/supabase.ts');
  fs.writeFileSync(typesPath, types);
  
  console.log('✅ Types generated successfully at src/types/supabase.ts');
} catch (error) {
  console.error('❌ Error generating types:', error.message);
  console.log('Please run this command manually:');
  console.log('supabase gen types typescript --project-id YOUR_PROJECT_ID --schema public > src/types/supabase.ts');
}
