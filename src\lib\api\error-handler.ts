import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { APIError } from './client';

export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}

export function createErrorResponse(
  message: string,
  status: number = 500,
  code?: string,
  details?: any
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: code || getErrorCodeFromStatus(status),
        message,
        details,
      },
    },
    { status }
  );
}

export function handleAPIError(error: unknown): NextResponse<ErrorResponse> {
  console.error('API Error:', error);

  // Zod validation errors
  if (error instanceof ZodError) {
    return createErrorResponse(
      'Validation failed',
      400,
      'VALIDATION_ERROR',
      error.errors
    );
  }

  // Custom API errors
  if (error instanceof APIError) {
    return createErrorResponse(
      error.message,
      error.status,
      error.code,
      error.details
    );
  }

  // Database errors
  if (error instanceof Error) {
    if (error.message.includes('duplicate key')) {
      return createErrorResponse(
        'Resource already exists',
        409,
        'DUPLICATE_RESOURCE'
      );
    }

    if (error.message.includes('foreign key')) {
      return createErrorResponse(
        'Referenced resource not found',
        400,
        'INVALID_REFERENCE'
      );
    }

    if (error.message.includes('not found')) {
      return createErrorResponse(
        'Resource not found',
        404,
        'NOT_FOUND'
      );
    }

    // Development mode: show actual error
    if (process.env.NODE_ENV === 'development') {
      return createErrorResponse(
        error.message,
        500,
        'INTERNAL_ERROR',
        { stack: error.stack }
      );
    }
  }

  // Generic server error
  return createErrorResponse(
    'Internal server error',
    500,
    'INTERNAL_ERROR'
  );
}

function getErrorCodeFromStatus(status: number): string {
  switch (status) {
    case 400:
      return 'BAD_REQUEST';
    case 401:
      return 'UNAUTHORIZED';
    case 403:
      return 'FORBIDDEN';
    case 404:
      return 'NOT_FOUND';
    case 409:
      return 'CONFLICT';
    case 422:
      return 'VALIDATION_ERROR';
    case 429:
      return 'RATE_LIMIT_EXCEEDED';
    case 500:
      return 'INTERNAL_ERROR';
    case 502:
      return 'BAD_GATEWAY';
    case 503:
      return 'SERVICE_UNAVAILABLE';
    case 504:
      return 'GATEWAY_TIMEOUT';
    default:
      return 'UNKNOWN_ERROR';
  }
}

// Error logging utility
export function logError(error: unknown, context?: Record<string, any>) {
  const errorInfo = {
    timestamp: new Date().toISOString(),
    error: error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
    } : error,
    context,
  };

  console.error('Error logged:', errorInfo);

  // In production, you might want to send this to an error tracking service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to Sentry, LogRocket, etc.
    // sentry.captureException(error, { extra: context });
  }
}

// User-friendly error messages
export const USER_FRIENDLY_ERRORS: Record<string, string> = {
  VALIDATION_ERROR: 'Please check your input and try again.',
  UNAUTHORIZED: 'You need to sign in to access this resource.',
  FORBIDDEN: 'You don\'t have permission to access this resource.',
  NOT_FOUND: 'The requested resource was not found.',
  DUPLICATE_RESOURCE: 'This resource already exists.',
  RATE_LIMIT_EXCEEDED: 'Too many requests. Please try again later.',
  INTERNAL_ERROR: 'Something went wrong on our end. Please try again.',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit.',
  INVALID_FILE_TYPE: 'Invalid file type.',
  UPLOAD_FAILED: 'File upload failed. Please try again.',
  PROCESSING_FAILED: 'Media processing failed.',
  SUBSCRIPTION_REQUIRED: 'This feature requires a subscription.',
  LIMIT_EXCEEDED: 'You have reached your plan limit.',
};

export function getUserFriendlyError(code: string): string {
  return USER_FRIENDLY_ERRORS[code] || 'An unexpected error occurred.';
}
