[{"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\api\\tours\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\api\\upload\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\auth\\signin\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\contact\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\dashboard\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\layout.tsx": "7", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\not-found.tsx": "8", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\pricing\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\privacy\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\terms\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\test-supabase\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\tours\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\tours\\[slug]\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\3d\\tour-viewer.tsx": "16", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\error-boundary.tsx": "17", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\dashboard-layout.tsx": "18", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\footer.tsx": "19", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\header.tsx": "20", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\public-layout.tsx": "21", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\sidebar.tsx": "22", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\providers.tsx": "23", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\app-loading.tsx": "24", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\avatar.tsx": "25", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\badge.tsx": "26", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\button.tsx": "27", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\card.tsx": "28", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\dialog.tsx": "29", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\dropdown-menu.tsx": "30", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\input.tsx": "31", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\label.tsx": "32", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\loading.tsx": "33", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\skeleton.tsx": "34", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\tabs.tsx": "35", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\textarea.tsx": "36", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\toast.tsx": "37", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\toaster.tsx": "38", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\index.ts": "39", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-click-outside.ts": "40", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-debounce.ts": "41", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-device-detection.ts": "42", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-file-upload.ts": "43", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-local-storage.ts": "44", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-media-query.ts": "45", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-supabase.ts": "46", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-toast.ts": "47", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\api\\client.ts": "48", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\api\\error-handler.ts": "49", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\constants\\index.ts": "50", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\env.ts": "51", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\supabase\\client.ts": "52", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\supabase\\operations.ts": "53", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\supabase\\server.ts": "54", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\cn.ts": "55", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\date.ts": "56", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\file.ts": "57", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\format.ts": "58", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\index.ts": "59", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\math.ts": "60", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\storage.ts": "61", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\url.ts": "62", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\validation.ts": "63", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils.ts": "64", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\auth.ts": "65", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\index.ts": "66", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\media.ts": "67", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\subscription.ts": "68", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\tour.ts": "69", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\user.ts": "70", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\middleware.ts": "71", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\types\\index.ts": "72", "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\types\\supabase.ts": "73"}, {"size": 9431, "mtime": 1749170643701, "results": "74", "hashOfConfig": "75"}, {"size": 5204, "mtime": 1749174729665, "results": "76", "hashOfConfig": "75"}, {"size": 8288, "mtime": 1749174797157, "results": "77", "hashOfConfig": "75"}, {"size": 2622, "mtime": 1749167572917, "results": "78", "hashOfConfig": "75"}, {"size": 10308, "mtime": 1749170673817, "results": "79", "hashOfConfig": "75"}, {"size": 4473, "mtime": 1749168730627, "results": "80", "hashOfConfig": "75"}, {"size": 2505, "mtime": 1749166807383, "results": "81", "hashOfConfig": "75"}, {"size": 2169, "mtime": 1749170567948, "results": "82", "hashOfConfig": "75"}, {"size": 7729, "mtime": 1749170693731, "results": "83", "hashOfConfig": "75"}, {"size": 7630, "mtime": 1749170611924, "results": "84", "hashOfConfig": "75"}, {"size": 13048, "mtime": 1749171152217, "results": "85", "hashOfConfig": "75"}, {"size": 14395, "mtime": 1749171192083, "results": "86", "hashOfConfig": "75"}, {"size": 10847, "mtime": 1749174833190, "results": "87", "hashOfConfig": "75"}, {"size": 5825, "mtime": 1749167612200, "results": "88", "hashOfConfig": "75"}, {"size": 10930, "mtime": 1749170783372, "results": "89", "hashOfConfig": "75"}, {"size": 7375, "mtime": 1749208623252, "results": "90", "hashOfConfig": "75"}, {"size": 4412, "mtime": 1749208768952, "results": "91", "hashOfConfig": "75"}, {"size": 549, "mtime": 1749168679628, "results": "92", "hashOfConfig": "75"}, {"size": 4787, "mtime": 1749168706823, "results": "93", "hashOfConfig": "75"}, {"size": 5903, "mtime": 1749168651583, "results": "94", "hashOfConfig": "75"}, {"size": 391, "mtime": 1749168688360, "results": "95", "hashOfConfig": "75"}, {"size": 4246, "mtime": 1749168670390, "results": "96", "hashOfConfig": "75"}, {"size": 4421, "mtime": 1749173589871, "results": "97", "hashOfConfig": "75"}, {"size": 3659, "mtime": 1749170586438, "results": "98", "hashOfConfig": "75"}, {"size": 1405, "mtime": 1749168498236, "results": "99", "hashOfConfig": "75"}, {"size": 1140, "mtime": 1749168487178, "results": "100", "hashOfConfig": "75"}, {"size": 1902, "mtime": 1749166754950, "results": "101", "hashOfConfig": "75"}, {"size": 1814, "mtime": 1749166764444, "results": "102", "hashOfConfig": "75"}, {"size": 3835, "mtime": 1749168441461, "results": "103", "hashOfConfig": "75"}, {"size": 7295, "mtime": 1749168464436, "results": "104", "hashOfConfig": "75"}, {"size": 768, "mtime": 1749166771232, "results": "105", "hashOfConfig": "75"}, {"size": 710, "mtime": 1749166777953, "results": "106", "hashOfConfig": "75"}, {"size": 3036, "mtime": 1749169060298, "results": "107", "hashOfConfig": "75"}, {"size": 261, "mtime": 1749168507017, "results": "108", "hashOfConfig": "75"}, {"size": 1877, "mtime": 1749168476081, "results": "109", "hashOfConfig": "75"}, {"size": 649, "mtime": 1749166784387, "results": "110", "hashOfConfig": "75"}, {"size": 4839, "mtime": 1749168426480, "results": "111", "hashOfConfig": "75"}, {"size": 786, "mtime": 1749168534568, "results": "112", "hashOfConfig": "75"}, {"size": 419, "mtime": 1749170507743, "results": "113", "hashOfConfig": "75"}, {"size": 837, "mtime": 1749170449021, "results": "114", "hashOfConfig": "75"}, {"size": 386, "mtime": 1749169123909, "results": "115", "hashOfConfig": "75"}, {"size": 4175, "mtime": 1749170493593, "results": "116", "hashOfConfig": "75"}, {"size": 5279, "mtime": 1749170471575, "results": "117", "hashOfConfig": "75"}, {"size": 1267, "mtime": 1749169109664, "results": "118", "hashOfConfig": "75"}, {"size": 1779, "mtime": 1749170438784, "results": "119", "hashOfConfig": "75"}, {"size": 9028, "mtime": 1749173745991, "results": "120", "hashOfConfig": "75"}, {"size": 3895, "mtime": 1749168525264, "results": "121", "hashOfConfig": "75"}, {"size": 5139, "mtime": 1749170532650, "results": "122", "hashOfConfig": "75"}, {"size": 4346, "mtime": 1749170552365, "results": "123", "hashOfConfig": "75"}, {"size": 9033, "mtime": 1749164757662, "results": "124", "hashOfConfig": "75"}, {"size": 7955, "mtime": 1749171316081, "results": "125", "hashOfConfig": "75"}, {"size": 7847, "mtime": 1749174862243, "results": "126", "hashOfConfig": "75"}, {"size": 11572, "mtime": 1749175390403, "results": "127", "hashOfConfig": "75"}, {"size": 9314, "mtime": 1749174894961, "results": "128", "hashOfConfig": "75"}, {"size": 290, "mtime": 1749164142451, "results": "129", "hashOfConfig": "75"}, {"size": 6935, "mtime": 1749164588378, "results": "130", "hashOfConfig": "75"}, {"size": 7995, "mtime": 1749175279201, "results": "131", "hashOfConfig": "75"}, {"size": 2750, "mtime": 1749164159209, "results": "132", "hashOfConfig": "75"}, {"size": 209, "mtime": 1749166735213, "results": "133", "hashOfConfig": "75"}, {"size": 6558, "mtime": 1749164611554, "results": "134", "hashOfConfig": "75"}, {"size": 8253, "mtime": 1749164636970, "results": "135", "hashOfConfig": "75"}, {"size": 5472, "mtime": 1749164565150, "results": "136", "hashOfConfig": "75"}, {"size": 4412, "mtime": 1749164524789, "results": "137", "hashOfConfig": "75"}, {"size": 166, "mtime": 1749166540709, "results": "138", "hashOfConfig": "75"}, {"size": 7426, "mtime": 1749164781170, "results": "139", "hashOfConfig": "75"}, {"size": 172, "mtime": 1749164817266, "results": "140", "hashOfConfig": "75"}, {"size": 5837, "mtime": 1749164839601, "results": "141", "hashOfConfig": "75"}, {"size": 9078, "mtime": 1749164895237, "results": "142", "hashOfConfig": "75"}, {"size": 9436, "mtime": 1749164811671, "results": "143", "hashOfConfig": "75"}, {"size": 8107, "mtime": 1749164866269, "results": "144", "hashOfConfig": "75"}, {"size": 7716, "mtime": 1749174670984, "results": "145", "hashOfConfig": "75"}, {"size": 7711, "mtime": 1749168602279, "results": "146", "hashOfConfig": "75"}, {"size": 15689, "mtime": 1749164722943, "results": "147", "hashOfConfig": "75"}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "pikpo3", {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 48, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\about\\page.tsx", ["367", "368", "369", "370", "371"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\api\\tours\\route.ts", ["372", "373"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\api\\upload\\route.ts", ["374", "375", "376"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\auth\\signin\\page.tsx", ["377"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\contact\\page.tsx", ["378", "379"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\dashboard\\page.tsx", ["380", "381"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\not-found.tsx", ["382"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\pricing\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\privacy\\page.tsx", ["383"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\terms\\page.tsx", ["384", "385", "386", "387", "388", "389"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\test-supabase\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\tours\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\tours\\[slug]\\page.tsx", ["390", "391"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\3d\\tour-viewer.tsx", ["392", "393", "394", "395"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\error-boundary.tsx", ["396", "397"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\dashboard-layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\public-layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\layout\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\providers.tsx", ["398", "399"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\app-loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\loading.tsx", ["400", "401"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\components\\ui\\toaster.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-click-outside.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-debounce.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-device-detection.ts", ["402"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-file-upload.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-local-storage.ts", ["403"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-media-query.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-supabase.ts", ["404", "405", "406", "407", "408", "409", "410", "411"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\hooks\\use-toast.ts", ["412"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\api\\client.ts", ["413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423", "424"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\api\\error-handler.ts", ["425", "426", "427"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\env.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\supabase\\client.ts", ["428", "429", "430", "431", "432", "433"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\supabase\\operations.ts", ["434", "435", "436", "437", "438", "439"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\cn.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\date.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\file.ts", ["440", "441"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\format.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\math.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\storage.ts", ["442"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\url.ts", ["443"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils\\validation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\media.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\subscription.ts", [], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\tour.ts", ["444"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\lib\\validations\\user.ts", ["445", "446", "447"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\middleware.ts", ["448"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\types\\index.ts", ["449", "450", "451", "452", "453", "454", "455"], [], "C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\types\\supabase.ts", ["456", "457", "458", "459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471", "472", "473", "474", "475", "476", "477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503"], [], {"ruleId": "504", "severity": 2, "message": "505", "line": 75, "column": 17, "nodeType": "506", "messageId": "507", "suggestions": "508"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 109, "column": 28, "nodeType": "506", "messageId": "507", "suggestions": "509"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 120, "column": 33, "nodeType": "506", "messageId": "507", "suggestions": "510"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 195, "column": 70, "nodeType": "506", "messageId": "507", "suggestions": "511"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 206, "column": 17, "nodeType": "506", "messageId": "507", "suggestions": "512"}, {"ruleId": "513", "severity": 2, "message": "514", "line": 2, "column": 10, "nodeType": null, "messageId": "515", "endLine": 2, "endColumn": 34}, {"ruleId": "513", "severity": 2, "message": "516", "line": 6, "column": 15, "nodeType": null, "messageId": "515", "endLine": 6, "endColumn": 23}, {"ruleId": "513", "severity": 2, "message": "514", "line": 2, "column": 10, "nodeType": null, "messageId": "515", "endLine": 2, "endColumn": 34}, {"ruleId": "513", "severity": 2, "message": "516", "line": 5, "column": 15, "nodeType": null, "messageId": "515", "endLine": 5, "endColumn": 23}, {"ruleId": "513", "severity": 2, "message": "517", "line": 102, "column": 19, "nodeType": null, "messageId": "515", "endLine": 102, "endColumn": 29}, {"ruleId": "504", "severity": 2, "message": "505", "line": 67, "column": 16, "nodeType": "506", "messageId": "507", "suggestions": "518"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 65, "column": 55, "nodeType": "506", "messageId": "507", "suggestions": "519"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 77, "column": 51, "nodeType": "506", "messageId": "507", "suggestions": "520"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 20, "column": 17, "nodeType": "506", "messageId": "507", "suggestions": "521"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 20, "column": 24, "nodeType": "506", "messageId": "507", "suggestions": "522"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 25, "column": 26, "nodeType": "506", "messageId": "507", "suggestions": "523"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 237, "column": 39, "nodeType": "506", "messageId": "507", "suggestions": "524"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 37, "column": 41, "nodeType": "506", "messageId": "507", "suggestions": "525"}, {"ruleId": "504", "severity": 2, "message": "526", "line": 51, "column": 43, "nodeType": "506", "messageId": "507", "suggestions": "527"}, {"ruleId": "504", "severity": 2, "message": "526", "line": 51, "column": 49, "nodeType": "506", "messageId": "507", "suggestions": "528"}, {"ruleId": "504", "severity": 2, "message": "505", "line": 51, "column": 86, "nodeType": "506", "messageId": "507", "suggestions": "529"}, {"ruleId": "504", "severity": 2, "message": "526", "line": 248, "column": 47, "nodeType": "506", "messageId": "507", "suggestions": "530"}, {"ruleId": "504", "severity": 2, "message": "526", "line": 248, "column": 53, "nodeType": "506", "messageId": "507", "suggestions": "531"}, {"ruleId": "513", "severity": 2, "message": "532", "line": 19, "column": 8, "nodeType": null, "messageId": "515", "endLine": 19, "endColumn": 12}, {"ruleId": "513", "severity": 2, "message": "533", "line": 81, "column": 42, "nodeType": null, "messageId": "515", "endLine": 81, "endColumn": 48}, {"ruleId": "513", "severity": 2, "message": "534", "line": 25, "column": 13, "nodeType": null, "messageId": "515", "endLine": 25, "endColumn": 18}, {"ruleId": "513", "severity": 2, "message": "535", "line": 25, "column": 20, "nodeType": null, "messageId": "515", "endLine": 25, "endColumn": 25}, {"ruleId": "513", "severity": 2, "message": "536", "line": 56, "column": 3, "nodeType": null, "messageId": "515", "endLine": 56, "endColumn": 16}, {"ruleId": "513", "severity": 2, "message": "537", "line": 60, "column": 10, "nodeType": null, "messageId": "515", "endLine": 60, "endColumn": 22}, {"ruleId": "538", "severity": 2, "message": "539", "line": 32, "column": 53, "nodeType": "540", "messageId": "541", "endLine": 32, "endColumn": 56, "suggestions": "542"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 33, "column": 18, "nodeType": "540", "messageId": "541", "endLine": 33, "endColumn": 21, "suggestions": "543"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 108, "column": 26, "nodeType": "540", "messageId": "541", "endLine": 108, "endColumn": 29, "suggestions": "544"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 108, "column": 40, "nodeType": "540", "messageId": "541", "endLine": 108, "endColumn": 43, "suggestions": "545"}, {"ruleId": "513", "severity": 2, "message": "546", "line": 10, "column": 59, "nodeType": null, "messageId": "515", "endLine": 10, "endColumn": 63}, {"ruleId": "513", "severity": 2, "message": "547", "line": 11, "column": 9, "nodeType": null, "messageId": "515", "endLine": 11, "endColumn": 20}, {"ruleId": "538", "severity": 2, "message": "539", "line": 80, "column": 20, "nodeType": "540", "messageId": "541", "endLine": 80, "endColumn": 23, "suggestions": "548"}, {"ruleId": "513", "severity": 2, "message": "549", "line": 1, "column": 20, "nodeType": null, "messageId": "515", "endLine": 1, "endColumn": 29}, {"ruleId": "538", "severity": 2, "message": "539", "line": 12, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 12, "endColumn": 39, "suggestions": "550"}, {"ruleId": "551", "severity": 1, "message": "552", "line": 116, "column": 6, "nodeType": "553", "endLine": 116, "endColumn": 56, "suggestions": "554"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 164, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 164, "endColumn": 39, "suggestions": "555"}, {"ruleId": "513", "severity": 2, "message": "556", "line": 236, "column": 20, "nodeType": null, "messageId": "515", "endLine": 236, "endColumn": 23}, {"ruleId": "538", "severity": 2, "message": "539", "line": 291, "column": 28, "nodeType": "540", "messageId": "541", "endLine": 291, "endColumn": 31, "suggestions": "557"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 294, "column": 53, "nodeType": "540", "messageId": "541", "endLine": 294, "endColumn": 56, "suggestions": "558"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 313, "column": 46, "nodeType": "540", "messageId": "541", "endLine": 313, "endColumn": 49, "suggestions": "559"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 333, "column": 72, "nodeType": "540", "messageId": "541", "endLine": 333, "endColumn": 75, "suggestions": "560"}, {"ruleId": "513", "severity": 2, "message": "561", "line": 18, "column": 7, "nodeType": null, "messageId": "562", "endLine": 18, "endColumn": 18}, {"ruleId": "538", "severity": 2, "message": "539", "line": 9, "column": 22, "nodeType": "540", "messageId": "541", "endLine": 9, "endColumn": 25, "suggestions": "563"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 28, "column": 29, "nodeType": "540", "messageId": "541", "endLine": 28, "endColumn": 32, "suggestions": "564"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 82, "column": 17, "nodeType": "540", "messageId": "541", "endLine": 82, "endColumn": 20, "suggestions": "565"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 86, "column": 18, "nodeType": "540", "messageId": "541", "endLine": 86, "endColumn": 21, "suggestions": "566"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 88, "column": 12, "nodeType": "540", "messageId": "541", "endLine": 88, "endColumn": 15, "suggestions": "567"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 98, "column": 17, "nodeType": "540", "messageId": "541", "endLine": 98, "endColumn": 20, "suggestions": "568"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 100, "column": 12, "nodeType": "540", "messageId": "541", "endLine": 100, "endColumn": 15, "suggestions": "569"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 110, "column": 19, "nodeType": "540", "messageId": "541", "endLine": 110, "endColumn": 22, "suggestions": "570"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 112, "column": 12, "nodeType": "540", "messageId": "541", "endLine": 112, "endColumn": 15, "suggestions": "571"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 122, "column": 20, "nodeType": "540", "messageId": "541", "endLine": 122, "endColumn": 23, "suggestions": "572"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 127, "column": 20, "nodeType": "540", "messageId": "541", "endLine": 127, "endColumn": 23, "suggestions": "573"}, {"ruleId": "513", "severity": 2, "message": "574", "line": 160, "column": 18, "nodeType": null, "messageId": "515", "endLine": 160, "endColumn": 23}, {"ruleId": "538", "severity": 2, "message": "539", "line": 10, "column": 15, "nodeType": "540", "messageId": "541", "endLine": 10, "endColumn": 18, "suggestions": "575"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 18, "column": 13, "nodeType": "540", "messageId": "541", "endLine": 18, "endColumn": 16, "suggestions": "576"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 131, "column": 67, "nodeType": "540", "messageId": "541", "endLine": 131, "endColumn": 70, "suggestions": "577"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 172, "column": 30, "nodeType": "540", "messageId": "541", "endLine": 172, "endColumn": 33, "suggestions": "578"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 249, "column": 30, "nodeType": "540", "messageId": "541", "endLine": 249, "endColumn": 33, "suggestions": "579"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 278, "column": 25, "nodeType": "540", "messageId": "541", "endLine": 278, "endColumn": 28, "suggestions": "580"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 306, "column": 25, "nodeType": "540", "messageId": "541", "endLine": 306, "endColumn": 28, "suggestions": "581"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 317, "column": 25, "nodeType": "540", "messageId": "541", "endLine": 317, "endColumn": 28, "suggestions": "582"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 365, "column": 56, "nodeType": "540", "messageId": "541", "endLine": 365, "endColumn": 59, "suggestions": "583"}, {"ruleId": "513", "severity": 2, "message": "584", "line": 2, "column": 10, "nodeType": null, "messageId": "515", "endLine": 2, "endColumn": 36}, {"ruleId": "538", "severity": 2, "message": "539", "line": 264, "column": 69, "nodeType": "540", "messageId": "541", "endLine": 264, "endColumn": 72, "suggestions": "585"}, {"ruleId": "513", "severity": 2, "message": "586", "line": 335, "column": 15, "nodeType": null, "messageId": "515", "endLine": 335, "endColumn": 19}, {"ruleId": "538", "severity": 2, "message": "539", "line": 409, "column": 60, "nodeType": "540", "messageId": "541", "endLine": 409, "endColumn": 63, "suggestions": "587"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 429, "column": 61, "nodeType": "540", "messageId": "541", "endLine": 429, "endColumn": 64, "suggestions": "588"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 449, "column": 64, "nodeType": "540", "messageId": "541", "endLine": 449, "endColumn": 67, "suggestions": "589"}, {"ruleId": "513", "severity": 2, "message": "574", "line": 280, "column": 16, "nodeType": null, "messageId": "515", "endLine": 280, "endColumn": 21}, {"ruleId": "513", "severity": 2, "message": "574", "line": 297, "column": 16, "nodeType": null, "messageId": "515", "endLine": 297, "endColumn": 21}, {"ruleId": "538", "severity": 2, "message": "539", "line": 336, "column": 11, "nodeType": "540", "messageId": "541", "endLine": 336, "endColumn": 14, "suggestions": "590"}, {"ruleId": "513", "severity": 2, "message": "591", "line": 190, "column": 3, "nodeType": null, "messageId": "515", "endLine": 190, "endColumn": 12}, {"ruleId": "538", "severity": 2, "message": "539", "line": 215, "column": 50, "nodeType": "540", "messageId": "541", "endLine": 215, "endColumn": 53, "suggestions": "592"}, {"ruleId": "513", "severity": 2, "message": "593", "line": 2, "column": 38, "nodeType": null, "messageId": "515", "endLine": 2, "endColumn": 51}, {"ruleId": "538", "severity": 2, "message": "539", "line": 80, "column": 50, "nodeType": "540", "messageId": "541", "endLine": 80, "endColumn": 53, "suggestions": "594"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 119, "column": 50, "nodeType": "540", "messageId": "541", "endLine": 119, "endColumn": 53, "suggestions": "595"}, {"ruleId": "513", "severity": 2, "message": "596", "line": 73, "column": 52, "nodeType": null, "messageId": "515", "endLine": 73, "endColumn": 59}, {"ruleId": "538", "severity": 2, "message": "539", "line": 211, "column": 38, "nodeType": "540", "messageId": "541", "endLine": 211, "endColumn": 41, "suggestions": "597"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 259, "column": 28, "nodeType": "540", "messageId": "541", "endLine": 259, "endColumn": 31, "suggestions": "598"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 272, "column": 29, "nodeType": "540", "messageId": "541", "endLine": 272, "endColumn": 32, "suggestions": "599"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 285, "column": 34, "nodeType": "540", "messageId": "541", "endLine": 285, "endColumn": 37, "suggestions": "600"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 291, "column": 15, "nodeType": "540", "messageId": "541", "endLine": 291, "endColumn": 18, "suggestions": "601"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 355, "column": 13, "nodeType": "540", "messageId": "541", "endLine": 355, "endColumn": 16, "suggestions": "602"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 363, "column": 24, "nodeType": "540", "messageId": "541", "endLine": 363, "endColumn": 27, "suggestions": "603"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 25, "column": 39, "nodeType": "540", "messageId": "541", "endLine": 25, "endColumn": 42, "suggestions": "604"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 46, "column": 40, "nodeType": "540", "messageId": "541", "endLine": 46, "endColumn": 43, "suggestions": "605"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 67, "column": 40, "nodeType": "540", "messageId": "541", "endLine": 67, "endColumn": 43, "suggestions": "606"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 96, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 96, "endColumn": 39, "suggestions": "607"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 127, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 127, "endColumn": 40, "suggestions": "608"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 158, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 158, "endColumn": 40, "suggestions": "609"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 176, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 176, "endColumn": 39, "suggestions": "610"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 177, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 177, "endColumn": 39, "suggestions": "611"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 178, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 178, "endColumn": 39, "suggestions": "612"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 190, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 190, "endColumn": 40, "suggestions": "613"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 191, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 191, "endColumn": 40, "suggestions": "614"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 192, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 192, "endColumn": 40, "suggestions": "615"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 204, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 204, "endColumn": 40, "suggestions": "616"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 205, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 205, "endColumn": 40, "suggestions": "617"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 206, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 206, "endColumn": 40, "suggestions": "618"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 218, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 218, "endColumn": 39, "suggestions": "619"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 219, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 219, "endColumn": 39, "suggestions": "620"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 220, "column": 33, "nodeType": "540", "messageId": "541", "endLine": 220, "endColumn": 36, "suggestions": "621"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 226, "column": 40, "nodeType": "540", "messageId": "541", "endLine": 226, "endColumn": 43, "suggestions": "622"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 227, "column": 40, "nodeType": "540", "messageId": "541", "endLine": 227, "endColumn": 43, "suggestions": "623"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 228, "column": 44, "nodeType": "540", "messageId": "541", "endLine": 228, "endColumn": 47, "suggestions": "624"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 239, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 239, "endColumn": 39, "suggestions": "625"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 240, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 240, "endColumn": 40, "suggestions": "626"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 241, "column": 34, "nodeType": "540", "messageId": "541", "endLine": 241, "endColumn": 37, "suggestions": "627"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 247, "column": 41, "nodeType": "540", "messageId": "541", "endLine": 247, "endColumn": 44, "suggestions": "628"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 248, "column": 41, "nodeType": "540", "messageId": "541", "endLine": 248, "endColumn": 44, "suggestions": "629"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 249, "column": 45, "nodeType": "540", "messageId": "541", "endLine": 249, "endColumn": 48, "suggestions": "630"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 260, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 260, "endColumn": 40, "suggestions": "631"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 261, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 261, "endColumn": 40, "suggestions": "632"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 262, "column": 34, "nodeType": "540", "messageId": "541", "endLine": 262, "endColumn": 37, "suggestions": "633"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 268, "column": 41, "nodeType": "540", "messageId": "541", "endLine": 268, "endColumn": 44, "suggestions": "634"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 269, "column": 41, "nodeType": "540", "messageId": "541", "endLine": 269, "endColumn": 44, "suggestions": "635"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 270, "column": 45, "nodeType": "540", "messageId": "541", "endLine": 270, "endColumn": 48, "suggestions": "636"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 286, "column": 38, "nodeType": "540", "messageId": "541", "endLine": 286, "endColumn": 41, "suggestions": "637"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 289, "column": 47, "nodeType": "540", "messageId": "541", "endLine": 289, "endColumn": 50, "suggestions": "638"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 293, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 293, "endColumn": 39, "suggestions": "639"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 308, "column": 39, "nodeType": "540", "messageId": "541", "endLine": 308, "endColumn": 42, "suggestions": "640"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 311, "column": 48, "nodeType": "540", "messageId": "541", "endLine": 311, "endColumn": 51, "suggestions": "641"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 315, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 315, "endColumn": 40, "suggestions": "642"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 330, "column": 39, "nodeType": "540", "messageId": "541", "endLine": 330, "endColumn": 42, "suggestions": "643"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 333, "column": 48, "nodeType": "540", "messageId": "541", "endLine": 333, "endColumn": 51, "suggestions": "644"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 337, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 337, "endColumn": 40, "suggestions": "645"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 364, "column": 36, "nodeType": "540", "messageId": "541", "endLine": 364, "endColumn": 39, "suggestions": "646"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 387, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 387, "endColumn": 40, "suggestions": "647"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 410, "column": 37, "nodeType": "540", "messageId": "541", "endLine": 410, "endColumn": 40, "suggestions": "648"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 424, "column": 38, "nodeType": "540", "messageId": "541", "endLine": 424, "endColumn": 41, "suggestions": "649"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 443, "column": 39, "nodeType": "540", "messageId": "541", "endLine": 443, "endColumn": 42, "suggestions": "650"}, {"ruleId": "538", "severity": 2, "message": "539", "line": 462, "column": 39, "nodeType": "540", "messageId": "541", "endLine": 462, "endColumn": 42, "suggestions": "651"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["652", "653", "654", "655"], ["656", "657", "658", "659"], ["660", "661", "662", "663"], ["664", "665", "666", "667"], ["668", "669", "670", "671"], "@typescript-eslint/no-unused-vars", "'createRouteHandlerClient' is defined but never used.", "unusedVar", "'Database' is defined but never used.", "'uploadData' is assigned a value but never used.", ["672", "673", "674", "675"], ["676", "677", "678", "679"], ["680", "681", "682", "683"], ["684", "685", "686", "687"], ["688", "689", "690", "691"], ["692", "693", "694", "695"], ["696", "697", "698", "699"], ["700", "701", "702", "703"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["704", "705", "706", "707"], ["708", "709", "710", "711"], ["712", "713", "714", "715"], ["716", "717", "718", "719"], ["720", "721", "722", "723"], "'Link' is defined but never used.", "'params' is defined but never used.", "'state' is defined but never used.", "'delta' is defined but never used.", "'onSceneChange' is defined but never used.", "'isFullscreen' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["724", "725"], ["726", "727"], ["728", "729"], ["730", "731"], "'size' is assigned a value but never used.", "'sizeClasses' is assigned a value but never used.", ["732", "733"], "'useEffect' is defined but never used.", ["734", "735"], "react-hooks/exhaustive-deps", "React Hook useCallback has unnecessary dependencies: 'options.featured' and 'options.status'. Either exclude them or remove the dependency array.", "ArrayExpression", ["736"], ["737", "738"], "'err' is defined but never used.", ["739", "740"], ["741", "742"], ["743", "744"], ["745", "746"], "'actionTypes' is assigned a value but only used as a type.", "usedOnlyAsType", ["747", "748"], ["749", "750"], ["751", "752"], ["753", "754"], ["755", "756"], ["757", "758"], ["759", "760"], ["761", "762"], ["763", "764"], ["765", "766"], ["767", "768"], "'error' is defined but never used.", ["769", "770"], ["771", "772"], ["773", "774"], ["775", "776"], ["777", "778"], ["779", "780"], ["781", "782"], ["783", "784"], ["785", "786"], "'createServerSupabaseClient' is defined but never used.", ["787", "788"], "'data' is assigned a value but never used.", ["789", "790"], ["791", "792"], ["793", "794"], ["795", "796"], "'expiresIn' is assigned a value but never used.", ["797", "798"], "'THEME_OPTIONS' is defined but never used.", ["799", "800"], ["801", "802"], "'options' is defined but never used.", ["803", "804"], ["805", "806"], ["807", "808"], ["809", "810"], ["811", "812"], ["813", "814"], ["815", "816"], ["817", "818"], ["819", "820"], ["821", "822"], ["823", "824"], ["825", "826"], ["827", "828"], ["829", "830"], ["831", "832"], ["833", "834"], ["835", "836"], ["837", "838"], ["839", "840"], ["841", "842"], ["843", "844"], ["845", "846"], ["847", "848"], ["849", "850"], ["851", "852"], ["853", "854"], ["855", "856"], ["857", "858"], ["859", "860"], ["861", "862"], ["863", "864"], ["865", "866"], ["867", "868"], ["869", "870"], ["871", "872"], ["873", "874"], ["875", "876"], ["877", "878"], ["879", "880"], ["881", "882"], ["883", "884"], ["885", "886"], ["887", "888"], ["889", "890"], ["891", "892"], ["893", "894"], ["895", "896"], ["897", "898"], ["899", "900"], ["901", "902"], ["903", "904"], ["905", "906"], ["907", "908"], ["909", "910"], ["911", "912"], {"messageId": "913", "data": "914", "fix": "915", "desc": "916"}, {"messageId": "913", "data": "917", "fix": "918", "desc": "919"}, {"messageId": "913", "data": "920", "fix": "921", "desc": "922"}, {"messageId": "913", "data": "923", "fix": "924", "desc": "925"}, {"messageId": "913", "data": "926", "fix": "927", "desc": "916"}, {"messageId": "913", "data": "928", "fix": "929", "desc": "919"}, {"messageId": "913", "data": "930", "fix": "931", "desc": "922"}, {"messageId": "913", "data": "932", "fix": "933", "desc": "925"}, {"messageId": "913", "data": "934", "fix": "935", "desc": "916"}, {"messageId": "913", "data": "936", "fix": "937", "desc": "919"}, {"messageId": "913", "data": "938", "fix": "939", "desc": "922"}, {"messageId": "913", "data": "940", "fix": "941", "desc": "925"}, {"messageId": "913", "data": "942", "fix": "943", "desc": "916"}, {"messageId": "913", "data": "944", "fix": "945", "desc": "919"}, {"messageId": "913", "data": "946", "fix": "947", "desc": "922"}, {"messageId": "913", "data": "948", "fix": "949", "desc": "925"}, {"messageId": "913", "data": "950", "fix": "951", "desc": "916"}, {"messageId": "913", "data": "952", "fix": "953", "desc": "919"}, {"messageId": "913", "data": "954", "fix": "955", "desc": "922"}, {"messageId": "913", "data": "956", "fix": "957", "desc": "925"}, {"messageId": "913", "data": "958", "fix": "959", "desc": "916"}, {"messageId": "913", "data": "960", "fix": "961", "desc": "919"}, {"messageId": "913", "data": "962", "fix": "963", "desc": "922"}, {"messageId": "913", "data": "964", "fix": "965", "desc": "925"}, {"messageId": "913", "data": "966", "fix": "967", "desc": "916"}, {"messageId": "913", "data": "968", "fix": "969", "desc": "919"}, {"messageId": "913", "data": "970", "fix": "971", "desc": "922"}, {"messageId": "913", "data": "972", "fix": "973", "desc": "925"}, {"messageId": "913", "data": "974", "fix": "975", "desc": "916"}, {"messageId": "913", "data": "976", "fix": "977", "desc": "919"}, {"messageId": "913", "data": "978", "fix": "979", "desc": "922"}, {"messageId": "913", "data": "980", "fix": "981", "desc": "925"}, {"messageId": "913", "data": "982", "fix": "983", "desc": "916"}, {"messageId": "913", "data": "984", "fix": "985", "desc": "919"}, {"messageId": "913", "data": "986", "fix": "987", "desc": "922"}, {"messageId": "913", "data": "988", "fix": "989", "desc": "925"}, {"messageId": "913", "data": "990", "fix": "991", "desc": "916"}, {"messageId": "913", "data": "992", "fix": "993", "desc": "919"}, {"messageId": "913", "data": "994", "fix": "995", "desc": "922"}, {"messageId": "913", "data": "996", "fix": "997", "desc": "925"}, {"messageId": "913", "data": "998", "fix": "999", "desc": "916"}, {"messageId": "913", "data": "1000", "fix": "1001", "desc": "919"}, {"messageId": "913", "data": "1002", "fix": "1003", "desc": "922"}, {"messageId": "913", "data": "1004", "fix": "1005", "desc": "925"}, {"messageId": "913", "data": "1006", "fix": "1007", "desc": "916"}, {"messageId": "913", "data": "1008", "fix": "1009", "desc": "919"}, {"messageId": "913", "data": "1010", "fix": "1011", "desc": "922"}, {"messageId": "913", "data": "1012", "fix": "1013", "desc": "925"}, {"messageId": "913", "data": "1014", "fix": "1015", "desc": "916"}, {"messageId": "913", "data": "1016", "fix": "1017", "desc": "919"}, {"messageId": "913", "data": "1018", "fix": "1019", "desc": "922"}, {"messageId": "913", "data": "1020", "fix": "1021", "desc": "925"}, {"messageId": "913", "data": "1022", "fix": "1023", "desc": "1024"}, {"messageId": "913", "data": "1025", "fix": "1026", "desc": "1027"}, {"messageId": "913", "data": "1028", "fix": "1029", "desc": "1030"}, {"messageId": "913", "data": "1031", "fix": "1032", "desc": "1033"}, {"messageId": "913", "data": "1034", "fix": "1035", "desc": "1024"}, {"messageId": "913", "data": "1036", "fix": "1037", "desc": "1027"}, {"messageId": "913", "data": "1038", "fix": "1039", "desc": "1030"}, {"messageId": "913", "data": "1040", "fix": "1041", "desc": "1033"}, {"messageId": "913", "data": "1042", "fix": "1043", "desc": "916"}, {"messageId": "913", "data": "1044", "fix": "1045", "desc": "919"}, {"messageId": "913", "data": "1046", "fix": "1047", "desc": "922"}, {"messageId": "913", "data": "1048", "fix": "1049", "desc": "925"}, {"messageId": "913", "data": "1050", "fix": "1051", "desc": "1024"}, {"messageId": "913", "data": "1052", "fix": "1053", "desc": "1027"}, {"messageId": "913", "data": "1054", "fix": "1055", "desc": "1030"}, {"messageId": "913", "data": "1056", "fix": "1057", "desc": "1033"}, {"messageId": "913", "data": "1058", "fix": "1059", "desc": "1024"}, {"messageId": "913", "data": "1060", "fix": "1061", "desc": "1027"}, {"messageId": "913", "data": "1062", "fix": "1063", "desc": "1030"}, {"messageId": "913", "data": "1064", "fix": "1065", "desc": "1033"}, {"messageId": "1066", "fix": "1067", "desc": "1068"}, {"messageId": "1069", "fix": "1070", "desc": "1071"}, {"messageId": "1066", "fix": "1072", "desc": "1068"}, {"messageId": "1069", "fix": "1073", "desc": "1071"}, {"messageId": "1066", "fix": "1074", "desc": "1068"}, {"messageId": "1069", "fix": "1075", "desc": "1071"}, {"messageId": "1066", "fix": "1076", "desc": "1068"}, {"messageId": "1069", "fix": "1077", "desc": "1071"}, {"messageId": "1066", "fix": "1078", "desc": "1068"}, {"messageId": "1069", "fix": "1079", "desc": "1071"}, {"messageId": "1066", "fix": "1080", "desc": "1068"}, {"messageId": "1069", "fix": "1081", "desc": "1071"}, {"desc": "1082", "fix": "1083"}, {"messageId": "1066", "fix": "1084", "desc": "1068"}, {"messageId": "1069", "fix": "1085", "desc": "1071"}, {"messageId": "1066", "fix": "1086", "desc": "1068"}, {"messageId": "1069", "fix": "1087", "desc": "1071"}, {"messageId": "1066", "fix": "1088", "desc": "1068"}, {"messageId": "1069", "fix": "1089", "desc": "1071"}, {"messageId": "1066", "fix": "1090", "desc": "1068"}, {"messageId": "1069", "fix": "1091", "desc": "1071"}, {"messageId": "1066", "fix": "1092", "desc": "1068"}, {"messageId": "1069", "fix": "1093", "desc": "1071"}, {"messageId": "1066", "fix": "1094", "desc": "1068"}, {"messageId": "1069", "fix": "1095", "desc": "1071"}, {"messageId": "1066", "fix": "1096", "desc": "1068"}, {"messageId": "1069", "fix": "1097", "desc": "1071"}, {"messageId": "1066", "fix": "1098", "desc": "1068"}, {"messageId": "1069", "fix": "1099", "desc": "1071"}, {"messageId": "1066", "fix": "1100", "desc": "1068"}, {"messageId": "1069", "fix": "1101", "desc": "1071"}, {"messageId": "1066", "fix": "1102", "desc": "1068"}, {"messageId": "1069", "fix": "1103", "desc": "1071"}, {"messageId": "1066", "fix": "1104", "desc": "1068"}, {"messageId": "1069", "fix": "1105", "desc": "1071"}, {"messageId": "1066", "fix": "1106", "desc": "1068"}, {"messageId": "1069", "fix": "1107", "desc": "1071"}, {"messageId": "1066", "fix": "1108", "desc": "1068"}, {"messageId": "1069", "fix": "1109", "desc": "1071"}, {"messageId": "1066", "fix": "1110", "desc": "1068"}, {"messageId": "1069", "fix": "1111", "desc": "1071"}, {"messageId": "1066", "fix": "1112", "desc": "1068"}, {"messageId": "1069", "fix": "1113", "desc": "1071"}, {"messageId": "1066", "fix": "1114", "desc": "1068"}, {"messageId": "1069", "fix": "1115", "desc": "1071"}, {"messageId": "1066", "fix": "1116", "desc": "1068"}, {"messageId": "1069", "fix": "1117", "desc": "1071"}, {"messageId": "1066", "fix": "1118", "desc": "1068"}, {"messageId": "1069", "fix": "1119", "desc": "1071"}, {"messageId": "1066", "fix": "1120", "desc": "1068"}, {"messageId": "1069", "fix": "1121", "desc": "1071"}, {"messageId": "1066", "fix": "1122", "desc": "1068"}, {"messageId": "1069", "fix": "1123", "desc": "1071"}, {"messageId": "1066", "fix": "1124", "desc": "1068"}, {"messageId": "1069", "fix": "1125", "desc": "1071"}, {"messageId": "1066", "fix": "1126", "desc": "1068"}, {"messageId": "1069", "fix": "1127", "desc": "1071"}, {"messageId": "1066", "fix": "1128", "desc": "1068"}, {"messageId": "1069", "fix": "1129", "desc": "1071"}, {"messageId": "1066", "fix": "1130", "desc": "1068"}, {"messageId": "1069", "fix": "1131", "desc": "1071"}, {"messageId": "1066", "fix": "1132", "desc": "1068"}, {"messageId": "1069", "fix": "1133", "desc": "1071"}, {"messageId": "1066", "fix": "1134", "desc": "1068"}, {"messageId": "1069", "fix": "1135", "desc": "1071"}, {"messageId": "1066", "fix": "1136", "desc": "1068"}, {"messageId": "1069", "fix": "1137", "desc": "1071"}, {"messageId": "1066", "fix": "1138", "desc": "1068"}, {"messageId": "1069", "fix": "1139", "desc": "1071"}, {"messageId": "1066", "fix": "1140", "desc": "1068"}, {"messageId": "1069", "fix": "1141", "desc": "1071"}, {"messageId": "1066", "fix": "1142", "desc": "1068"}, {"messageId": "1069", "fix": "1143", "desc": "1071"}, {"messageId": "1066", "fix": "1144", "desc": "1068"}, {"messageId": "1069", "fix": "1145", "desc": "1071"}, {"messageId": "1066", "fix": "1146", "desc": "1068"}, {"messageId": "1069", "fix": "1147", "desc": "1071"}, {"messageId": "1066", "fix": "1148", "desc": "1068"}, {"messageId": "1069", "fix": "1149", "desc": "1071"}, {"messageId": "1066", "fix": "1150", "desc": "1068"}, {"messageId": "1069", "fix": "1151", "desc": "1071"}, {"messageId": "1066", "fix": "1152", "desc": "1068"}, {"messageId": "1069", "fix": "1153", "desc": "1071"}, {"messageId": "1066", "fix": "1154", "desc": "1068"}, {"messageId": "1069", "fix": "1155", "desc": "1071"}, {"messageId": "1066", "fix": "1156", "desc": "1068"}, {"messageId": "1069", "fix": "1157", "desc": "1071"}, {"messageId": "1066", "fix": "1158", "desc": "1068"}, {"messageId": "1069", "fix": "1159", "desc": "1071"}, {"messageId": "1066", "fix": "1160", "desc": "1068"}, {"messageId": "1069", "fix": "1161", "desc": "1071"}, {"messageId": "1066", "fix": "1162", "desc": "1068"}, {"messageId": "1069", "fix": "1163", "desc": "1071"}, {"messageId": "1066", "fix": "1164", "desc": "1068"}, {"messageId": "1069", "fix": "1165", "desc": "1071"}, {"messageId": "1066", "fix": "1166", "desc": "1068"}, {"messageId": "1069", "fix": "1167", "desc": "1071"}, {"messageId": "1066", "fix": "1168", "desc": "1068"}, {"messageId": "1069", "fix": "1169", "desc": "1071"}, {"messageId": "1066", "fix": "1170", "desc": "1068"}, {"messageId": "1069", "fix": "1171", "desc": "1071"}, {"messageId": "1066", "fix": "1172", "desc": "1068"}, {"messageId": "1069", "fix": "1173", "desc": "1071"}, {"messageId": "1066", "fix": "1174", "desc": "1068"}, {"messageId": "1069", "fix": "1175", "desc": "1071"}, {"messageId": "1066", "fix": "1176", "desc": "1068"}, {"messageId": "1069", "fix": "1177", "desc": "1071"}, {"messageId": "1066", "fix": "1178", "desc": "1068"}, {"messageId": "1069", "fix": "1179", "desc": "1071"}, {"messageId": "1066", "fix": "1180", "desc": "1068"}, {"messageId": "1069", "fix": "1181", "desc": "1071"}, {"messageId": "1066", "fix": "1182", "desc": "1068"}, {"messageId": "1069", "fix": "1183", "desc": "1071"}, {"messageId": "1066", "fix": "1184", "desc": "1068"}, {"messageId": "1069", "fix": "1185", "desc": "1071"}, {"messageId": "1066", "fix": "1186", "desc": "1068"}, {"messageId": "1069", "fix": "1187", "desc": "1071"}, {"messageId": "1066", "fix": "1188", "desc": "1068"}, {"messageId": "1069", "fix": "1189", "desc": "1071"}, {"messageId": "1066", "fix": "1190", "desc": "1068"}, {"messageId": "1069", "fix": "1191", "desc": "1071"}, {"messageId": "1066", "fix": "1192", "desc": "1068"}, {"messageId": "1069", "fix": "1193", "desc": "1071"}, {"messageId": "1066", "fix": "1194", "desc": "1068"}, {"messageId": "1069", "fix": "1195", "desc": "1071"}, {"messageId": "1066", "fix": "1196", "desc": "1068"}, {"messageId": "1069", "fix": "1197", "desc": "1071"}, {"messageId": "1066", "fix": "1198", "desc": "1068"}, {"messageId": "1069", "fix": "1199", "desc": "1071"}, {"messageId": "1066", "fix": "1200", "desc": "1068"}, {"messageId": "1069", "fix": "1201", "desc": "1071"}, {"messageId": "1066", "fix": "1202", "desc": "1068"}, {"messageId": "1069", "fix": "1203", "desc": "1071"}, {"messageId": "1066", "fix": "1204", "desc": "1068"}, {"messageId": "1069", "fix": "1205", "desc": "1071"}, {"messageId": "1066", "fix": "1206", "desc": "1068"}, {"messageId": "1069", "fix": "1207", "desc": "1071"}, {"messageId": "1066", "fix": "1208", "desc": "1068"}, {"messageId": "1069", "fix": "1209", "desc": "1071"}, {"messageId": "1066", "fix": "1210", "desc": "1068"}, {"messageId": "1069", "fix": "1211", "desc": "1071"}, {"messageId": "1066", "fix": "1212", "desc": "1068"}, {"messageId": "1069", "fix": "1213", "desc": "1071"}, {"messageId": "1066", "fix": "1214", "desc": "1068"}, {"messageId": "1069", "fix": "1215", "desc": "1071"}, {"messageId": "1066", "fix": "1216", "desc": "1068"}, {"messageId": "1069", "fix": "1217", "desc": "1071"}, {"messageId": "1066", "fix": "1218", "desc": "1068"}, {"messageId": "1069", "fix": "1219", "desc": "1071"}, {"messageId": "1066", "fix": "1220", "desc": "1068"}, {"messageId": "1069", "fix": "1221", "desc": "1071"}, {"messageId": "1066", "fix": "1222", "desc": "1068"}, {"messageId": "1069", "fix": "1223", "desc": "1071"}, {"messageId": "1066", "fix": "1224", "desc": "1068"}, {"messageId": "1069", "fix": "1225", "desc": "1071"}, {"messageId": "1066", "fix": "1226", "desc": "1068"}, {"messageId": "1069", "fix": "1227", "desc": "1071"}, {"messageId": "1066", "fix": "1228", "desc": "1068"}, {"messageId": "1069", "fix": "1229", "desc": "1071"}, {"messageId": "1066", "fix": "1230", "desc": "1068"}, {"messageId": "1069", "fix": "1231", "desc": "1071"}, {"messageId": "1066", "fix": "1232", "desc": "1068"}, {"messageId": "1069", "fix": "1233", "desc": "1071"}, {"messageId": "1066", "fix": "1234", "desc": "1068"}, {"messageId": "1069", "fix": "1235", "desc": "1071"}, {"messageId": "1066", "fix": "1236", "desc": "1068"}, {"messageId": "1069", "fix": "1237", "desc": "1071"}, {"messageId": "1066", "fix": "1238", "desc": "1068"}, {"messageId": "1069", "fix": "1239", "desc": "1071"}, {"messageId": "1066", "fix": "1240", "desc": "1068"}, {"messageId": "1069", "fix": "1241", "desc": "1071"}, {"messageId": "1066", "fix": "1242", "desc": "1068"}, {"messageId": "1069", "fix": "1243", "desc": "1071"}, {"messageId": "1066", "fix": "1244", "desc": "1068"}, {"messageId": "1069", "fix": "1245", "desc": "1071"}, {"messageId": "1066", "fix": "1246", "desc": "1068"}, {"messageId": "1069", "fix": "1247", "desc": "1071"}, {"messageId": "1066", "fix": "1248", "desc": "1068"}, {"messageId": "1069", "fix": "1249", "desc": "1071"}, {"messageId": "1066", "fix": "1250", "desc": "1068"}, {"messageId": "1069", "fix": "1251", "desc": "1071"}, {"messageId": "1066", "fix": "1252", "desc": "1068"}, {"messageId": "1069", "fix": "1253", "desc": "1071"}, {"messageId": "1066", "fix": "1254", "desc": "1068"}, {"messageId": "1069", "fix": "1255", "desc": "1071"}, {"messageId": "1066", "fix": "1256", "desc": "1068"}, {"messageId": "1069", "fix": "1257", "desc": "1071"}, {"messageId": "1066", "fix": "1258", "desc": "1068"}, {"messageId": "1069", "fix": "1259", "desc": "1071"}, "replaceWithAlt", {"alt": "1260"}, {"range": "1261", "text": "1262"}, "Replace with `&apos;`.", {"alt": "1263"}, {"range": "1264", "text": "1265"}, "Replace with `&lsquo;`.", {"alt": "1266"}, {"range": "1267", "text": "1268"}, "Replace with `&#39;`.", {"alt": "1269"}, {"range": "1270", "text": "1271"}, "Replace with `&rsquo;`.", {"alt": "1260"}, {"range": "1272", "text": "1273"}, {"alt": "1263"}, {"range": "1274", "text": "1275"}, {"alt": "1266"}, {"range": "1276", "text": "1277"}, {"alt": "1269"}, {"range": "1278", "text": "1279"}, {"alt": "1260"}, {"range": "1280", "text": "1281"}, {"alt": "1263"}, {"range": "1282", "text": "1283"}, {"alt": "1266"}, {"range": "1284", "text": "1285"}, {"alt": "1269"}, {"range": "1286", "text": "1287"}, {"alt": "1260"}, {"range": "1288", "text": "1289"}, {"alt": "1263"}, {"range": "1290", "text": "1291"}, {"alt": "1266"}, {"range": "1292", "text": "1293"}, {"alt": "1269"}, {"range": "1294", "text": "1295"}, {"alt": "1260"}, {"range": "1296", "text": "1297"}, {"alt": "1263"}, {"range": "1298", "text": "1299"}, {"alt": "1266"}, {"range": "1300", "text": "1301"}, {"alt": "1269"}, {"range": "1302", "text": "1303"}, {"alt": "1260"}, {"range": "1304", "text": "1305"}, {"alt": "1263"}, {"range": "1306", "text": "1307"}, {"alt": "1266"}, {"range": "1308", "text": "1309"}, {"alt": "1269"}, {"range": "1310", "text": "1311"}, {"alt": "1260"}, {"range": "1312", "text": "1313"}, {"alt": "1263"}, {"range": "1314", "text": "1315"}, {"alt": "1266"}, {"range": "1316", "text": "1317"}, {"alt": "1269"}, {"range": "1318", "text": "1319"}, {"alt": "1260"}, {"range": "1320", "text": "1321"}, {"alt": "1263"}, {"range": "1322", "text": "1323"}, {"alt": "1266"}, {"range": "1324", "text": "1325"}, {"alt": "1269"}, {"range": "1326", "text": "1327"}, {"alt": "1260"}, {"range": "1328", "text": "1329"}, {"alt": "1263"}, {"range": "1330", "text": "1331"}, {"alt": "1266"}, {"range": "1332", "text": "1333"}, {"alt": "1269"}, {"range": "1334", "text": "1335"}, {"alt": "1260"}, {"range": "1336", "text": "1337"}, {"alt": "1263"}, {"range": "1338", "text": "1339"}, {"alt": "1266"}, {"range": "1340", "text": "1341"}, {"alt": "1269"}, {"range": "1342", "text": "1343"}, {"alt": "1260"}, {"range": "1344", "text": "1345"}, {"alt": "1263"}, {"range": "1346", "text": "1347"}, {"alt": "1266"}, {"range": "1348", "text": "1349"}, {"alt": "1269"}, {"range": "1350", "text": "1351"}, {"alt": "1260"}, {"range": "1352", "text": "1353"}, {"alt": "1263"}, {"range": "1354", "text": "1355"}, {"alt": "1266"}, {"range": "1356", "text": "1357"}, {"alt": "1269"}, {"range": "1358", "text": "1359"}, {"alt": "1260"}, {"range": "1360", "text": "1361"}, {"alt": "1263"}, {"range": "1362", "text": "1363"}, {"alt": "1266"}, {"range": "1364", "text": "1365"}, {"alt": "1269"}, {"range": "1366", "text": "1367"}, {"alt": "1368"}, {"range": "1369", "text": "1370"}, "Replace with `&quot;`.", {"alt": "1371"}, {"range": "1372", "text": "1373"}, "Replace with `&ldquo;`.", {"alt": "1374"}, {"range": "1375", "text": "1376"}, "Replace with `&#34;`.", {"alt": "1377"}, {"range": "1378", "text": "1379"}, "Replace with `&rdquo;`.", {"alt": "1368"}, {"range": "1380", "text": "1381"}, {"alt": "1371"}, {"range": "1382", "text": "1383"}, {"alt": "1374"}, {"range": "1384", "text": "1385"}, {"alt": "1377"}, {"range": "1386", "text": "1387"}, {"alt": "1260"}, {"range": "1388", "text": "1389"}, {"alt": "1263"}, {"range": "1390", "text": "1391"}, {"alt": "1266"}, {"range": "1392", "text": "1393"}, {"alt": "1269"}, {"range": "1394", "text": "1395"}, {"alt": "1368"}, {"range": "1396", "text": "1397"}, {"alt": "1371"}, {"range": "1398", "text": "1399"}, {"alt": "1374"}, {"range": "1400", "text": "1401"}, {"alt": "1377"}, {"range": "1402", "text": "1403"}, {"alt": "1368"}, {"range": "1404", "text": "1405"}, {"alt": "1371"}, {"range": "1406", "text": "1407"}, {"alt": "1374"}, {"range": "1408", "text": "1409"}, {"alt": "1377"}, {"range": "1410", "text": "1411"}, "suggestUnknown", {"range": "1412", "text": "1413"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1414", "text": "1415"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1416", "text": "1413"}, {"range": "1417", "text": "1415"}, {"range": "1418", "text": "1413"}, {"range": "1419", "text": "1415"}, {"range": "1420", "text": "1413"}, {"range": "1421", "text": "1415"}, {"range": "1422", "text": "1413"}, {"range": "1423", "text": "1415"}, {"range": "1424", "text": "1413"}, {"range": "1425", "text": "1415"}, "Update the dependencies array to be: [options.userId]", {"range": "1426", "text": "1427"}, {"range": "1428", "text": "1413"}, {"range": "1429", "text": "1415"}, {"range": "1430", "text": "1413"}, {"range": "1431", "text": "1415"}, {"range": "1432", "text": "1413"}, {"range": "1433", "text": "1415"}, {"range": "1434", "text": "1413"}, {"range": "1435", "text": "1415"}, {"range": "1436", "text": "1413"}, {"range": "1437", "text": "1415"}, {"range": "1438", "text": "1413"}, {"range": "1439", "text": "1415"}, {"range": "1440", "text": "1413"}, {"range": "1441", "text": "1415"}, {"range": "1442", "text": "1413"}, {"range": "1443", "text": "1415"}, {"range": "1444", "text": "1413"}, {"range": "1445", "text": "1415"}, {"range": "1446", "text": "1413"}, {"range": "1447", "text": "1415"}, {"range": "1448", "text": "1413"}, {"range": "1449", "text": "1415"}, {"range": "1450", "text": "1413"}, {"range": "1451", "text": "1415"}, {"range": "1452", "text": "1413"}, {"range": "1453", "text": "1415"}, {"range": "1454", "text": "1413"}, {"range": "1455", "text": "1415"}, {"range": "1456", "text": "1413"}, {"range": "1457", "text": "1415"}, {"range": "1458", "text": "1413"}, {"range": "1459", "text": "1415"}, {"range": "1460", "text": "1413"}, {"range": "1461", "text": "1415"}, {"range": "1462", "text": "1413"}, {"range": "1463", "text": "1415"}, {"range": "1464", "text": "1413"}, {"range": "1465", "text": "1415"}, {"range": "1466", "text": "1413"}, {"range": "1467", "text": "1415"}, {"range": "1468", "text": "1413"}, {"range": "1469", "text": "1415"}, {"range": "1470", "text": "1413"}, {"range": "1471", "text": "1415"}, {"range": "1472", "text": "1413"}, {"range": "1473", "text": "1415"}, {"range": "1474", "text": "1413"}, {"range": "1475", "text": "1415"}, {"range": "1476", "text": "1413"}, {"range": "1477", "text": "1415"}, {"range": "1478", "text": "1413"}, {"range": "1479", "text": "1415"}, {"range": "1480", "text": "1413"}, {"range": "1481", "text": "1415"}, {"range": "1482", "text": "1413"}, {"range": "1483", "text": "1415"}, {"range": "1484", "text": "1413"}, {"range": "1485", "text": "1415"}, {"range": "1486", "text": "1413"}, {"range": "1487", "text": "1415"}, {"range": "1488", "text": "1413"}, {"range": "1489", "text": "1415"}, {"range": "1490", "text": "1413"}, {"range": "1491", "text": "1415"}, {"range": "1492", "text": "1413"}, {"range": "1493", "text": "1415"}, {"range": "1494", "text": "1413"}, {"range": "1495", "text": "1415"}, {"range": "1496", "text": "1413"}, {"range": "1497", "text": "1415"}, {"range": "1498", "text": "1413"}, {"range": "1499", "text": "1415"}, {"range": "1500", "text": "1413"}, {"range": "1501", "text": "1415"}, {"range": "1502", "text": "1413"}, {"range": "1503", "text": "1415"}, {"range": "1504", "text": "1413"}, {"range": "1505", "text": "1415"}, {"range": "1506", "text": "1413"}, {"range": "1507", "text": "1415"}, {"range": "1508", "text": "1413"}, {"range": "1509", "text": "1415"}, {"range": "1510", "text": "1413"}, {"range": "1511", "text": "1415"}, {"range": "1512", "text": "1413"}, {"range": "1513", "text": "1415"}, {"range": "1514", "text": "1413"}, {"range": "1515", "text": "1415"}, {"range": "1516", "text": "1413"}, {"range": "1517", "text": "1415"}, {"range": "1518", "text": "1413"}, {"range": "1519", "text": "1415"}, {"range": "1520", "text": "1413"}, {"range": "1521", "text": "1415"}, {"range": "1522", "text": "1413"}, {"range": "1523", "text": "1415"}, {"range": "1524", "text": "1413"}, {"range": "1525", "text": "1415"}, {"range": "1526", "text": "1413"}, {"range": "1527", "text": "1415"}, {"range": "1528", "text": "1413"}, {"range": "1529", "text": "1415"}, {"range": "1530", "text": "1413"}, {"range": "1531", "text": "1415"}, {"range": "1532", "text": "1413"}, {"range": "1533", "text": "1415"}, {"range": "1534", "text": "1413"}, {"range": "1535", "text": "1415"}, {"range": "1536", "text": "1413"}, {"range": "1537", "text": "1415"}, {"range": "1538", "text": "1413"}, {"range": "1539", "text": "1415"}, {"range": "1540", "text": "1413"}, {"range": "1541", "text": "1415"}, {"range": "1542", "text": "1413"}, {"range": "1543", "text": "1415"}, {"range": "1544", "text": "1413"}, {"range": "1545", "text": "1415"}, {"range": "1546", "text": "1413"}, {"range": "1547", "text": "1415"}, {"range": "1548", "text": "1413"}, {"range": "1549", "text": "1415"}, {"range": "1550", "text": "1413"}, {"range": "1551", "text": "1415"}, {"range": "1552", "text": "1413"}, {"range": "1553", "text": "1415"}, {"range": "1554", "text": "1413"}, {"range": "1555", "text": "1415"}, {"range": "1556", "text": "1413"}, {"range": "1557", "text": "1415"}, {"range": "1558", "text": "1413"}, {"range": "1559", "text": "1415"}, {"range": "1560", "text": "1413"}, {"range": "1561", "text": "1415"}, {"range": "1562", "text": "1413"}, {"range": "1563", "text": "1415"}, {"range": "1564", "text": "1413"}, {"range": "1565", "text": "1415"}, {"range": "1566", "text": "1413"}, {"range": "1567", "text": "1415"}, {"range": "1568", "text": "1413"}, {"range": "1569", "text": "1415"}, {"range": "1570", "text": "1413"}, {"range": "1571", "text": "1415"}, {"range": "1572", "text": "1413"}, {"range": "1573", "text": "1415"}, {"range": "1574", "text": "1413"}, {"range": "1575", "text": "1415"}, {"range": "1576", "text": "1413"}, {"range": "1577", "text": "1415"}, {"range": "1578", "text": "1413"}, {"range": "1579", "text": "1415"}, {"range": "1580", "text": "1413"}, {"range": "1581", "text": "1415"}, {"range": "1582", "text": "1413"}, {"range": "1583", "text": "1415"}, {"range": "1584", "text": "1413"}, {"range": "1585", "text": "1415"}, {"range": "1586", "text": "1413"}, {"range": "1587", "text": "1415"}, {"range": "1588", "text": "1413"}, {"range": "1589", "text": "1415"}, {"range": "1590", "text": "1413"}, {"range": "1591", "text": "1415"}, {"range": "1592", "text": "1413"}, {"range": "1593", "text": "1415"}, {"range": "1594", "text": "1413"}, {"range": "1595", "text": "1415"}, {"range": "1596", "text": "1413"}, {"range": "1597", "text": "1415"}, {"range": "1598", "text": "1413"}, {"range": "1599", "text": "1415"}, {"range": "1600", "text": "1413"}, {"range": "1601", "text": "1415"}, {"range": "1602", "text": "1413"}, {"range": "1603", "text": "1415"}, "&apos;", [2483, 2667], "\n              We&apos;re on a mission to make immersive 360° virtual tours accessible to every \n              business in Nigeria, from small local shops to large enterprises.\n            ", "&lsquo;", [2483, 2667], "\n              We&lsquo;re on a mission to make immersive 360° virtual tours accessible to every \n              business in Nigeria, from small local shops to large enterprises.\n            ", "&#39;", [2483, 2667], "\n              We&#39;re on a mission to make immersive 360° virtual tours accessible to every \n              business in Nigeria, from small local shops to large enterprises.\n            ", "&rsquo;", [2483, 2667], "\n              We&rsquo;re on a mission to make immersive 360° virtual tours accessible to every \n              business in Nigeria, from small local shops to large enterprises.\n            ", [4150, 4469], "\n                  Today, we&apos;re proud to serve thousands of businesses across Nigeria and \n                  beyond, from real estate agencies in Lagos to educational institutions \n                  in Abuja, helping them create immersive experiences that drive engagement \n                  and sales.\n                ", [4150, 4469], "\n                  Today, we&lsquo;re proud to serve thousands of businesses across Nigeria and \n                  beyond, from real estate agencies in Lagos to educational institutions \n                  in Abuja, helping them create immersive experiences that drive engagement \n                  and sales.\n                ", [4150, 4469], "\n                  Today, we&#39;re proud to serve thousands of businesses across Nigeria and \n                  beyond, from real estate agencies in Lagos to educational institutions \n                  in Abuja, helping them create immersive experiences that drive engagement \n                  and sales.\n                ", [4150, 4469], "\n                  Today, we&rsquo;re proud to serve thousands of businesses across Nigeria and \n                  beyond, from real estate agencies in Lagos to educational institutions \n                  in Abuja, helping them create immersive experiences that drive engagement \n                  and sales.\n                ", [4774, 5001], "\n                To become Africa&apos;s leading virtual tour platform, empowering businesses \n                to create immersive digital experiences that connect with customers \n                across the continent.\n              ", [4774, 5001], "\n                To become Africa&lsquo;s leading virtual tour platform, empowering businesses \n                to create immersive digital experiences that connect with customers \n                across the continent.\n              ", [4774, 5001], "\n                To become Africa&#39;s leading virtual tour platform, empowering businesses \n                to create immersive digital experiences that connect with customers \n                across the continent.\n              ", [4774, 5001], "\n                To become Africa&rsquo;s leading virtual tour platform, empowering businesses \n                to create immersive digital experiences that connect with customers \n                across the continent.\n              ", [8094, 8213], "\n                    Optimized for mobile devices, considering Nigeria&apos;s mobile-first internet usage\n                  ", [8094, 8213], "\n                    Optimized for mobile devices, considering Nigeria&lsquo;s mobile-first internet usage\n                  ", [8094, 8213], "\n                    Optimized for mobile devices, considering Nigeria&#39;s mobile-first internet usage\n                  ", [8094, 8213], "\n                    Optimized for mobile devices, considering Nigeria&rsquo;s mobile-first internet usage\n                  ", [8509, 8608], "\n              We&apos;d love to hear from you and answer any questions about our platform.\n            ", [8509, 8608], "\n              We&lsquo;d love to hear from you and answer any questions about our platform.\n            ", [8509, 8608], "\n              We&#39;d love to hear from you and answer any questions about our platform.\n            ", [8509, 8608], "\n              We&rsquo;d love to hear from you and answer any questions about our platform.\n            ", [2387, 2422], "\n            Don&apos;t have an account?", [2387, 2422], "\n            Don&lsquo;t have an account?", [2387, 2422], "\n            Don&#39;t have an account?", [2387, 2422], "\n            Don&rsquo;t have an account?", [1966, 2123], "\n              Have questions about VirtualRealTour? We&apos;re here to help you create \n              amazing virtual experiences for your business.\n            ", [1966, 2123], "\n              Have questions about VirtualRealTour? We&lsquo;re here to help you create \n              amazing virtual experiences for your business.\n            ", [1966, 2123], "\n              Have questions about VirtualRealTour? We&#39;re here to help you create \n              amazing virtual experiences for your business.\n            ", [1966, 2123], "\n              Have questions about VirtualRealTour? We&rsquo;re here to help you create \n              amazing virtual experiences for your business.\n            ", [2422, 2532], "\n                    Fill out the form below and we&apos;ll get back to you as soon as possible.\n                  ", [2422, 2532], "\n                    Fill out the form below and we&lsquo;ll get back to you as soon as possible.\n                  ", [2422, 2532], "\n                    Fill out the form below and we&#39;ll get back to you as soon as possible.\n                  ", [2422, 2532], "\n                    Fill out the form below and we&rsquo;ll get back to you as soon as possible.\n                  ", [701, 779], "\n            Here&apos;s what's happening with your virtual tours today.\n          ", [701, 779], "\n            Here&lsquo;s what's happening with your virtual tours today.\n          ", [701, 779], "\n            Here&#39;s what's happening with your virtual tours today.\n          ", [701, 779], "\n            Here&rsquo;s what's happening with your virtual tours today.\n          ", [701, 779], "\n            Here's what&apos;s happening with your virtual tours today.\n          ", [701, 779], "\n            Here's what&lsquo;s happening with your virtual tours today.\n          ", [701, 779], "\n            Here's what&#39;s happening with your virtual tours today.\n          ", [701, 779], "\n            Here's what&rsquo;s happening with your virtual tours today.\n          ", [1204, 1244], "You don&apos;t have the necessary permissions", [1204, 1244], "You don&lsquo;t have the necessary permissions", [1204, 1244], "You don&#39;t have the necessary permissions", [1204, 1244], "You don&rsquo;t have the necessary permissions", [10965, 10986], "9. Children&apos;s Privacy", [10965, 10986], "9. Children&lsquo;s Privacy", [10965, 10986], "9. Children&#39;s Privacy", [10965, 10986], "9. Children&rsquo;s Privacy", [1378, 1555], "\n                By using VirtualRealTour&apos;s services, you agree to be bound by these terms. \n                Please read them carefully before using our platform.\n              ", [1378, 1555], "\n                By using VirtualRealTour&lsquo;s services, you agree to be bound by these terms. \n                Please read them carefully before using our platform.\n              ", [1378, 1555], "\n                By using VirtualRealTour&#39;s services, you agree to be bound by these terms. \n                Please read them carefully before using our platform.\n              ", [1378, 1555], "\n                By using VirtualRealTour&rsquo;s services, you agree to be bound by these terms. \n                Please read them carefully before using our platform.\n              ", "&quot;", [1901, 2249], "\n                  These Terms of Service (&quot;Terms\") govern your use of VirtualRealTour's \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", "&ldquo;", [1901, 2249], "\n                  These Terms of Service (&ldquo;Terms\") govern your use of VirtualRealTour's \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", "&#34;", [1901, 2249], "\n                  These Terms of Service (&#34;Terms\") govern your use of VirtualRealTour's \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", "&rdquo;", [1901, 2249], "\n                  These Terms of Service (&rdquo;Terms\") govern your use of VirtualRealTour's \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", [1901, 2249], "\n                  These Terms of Service (\"Terms&quot;) govern your use of VirtualRealTour's \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", [1901, 2249], "\n                  These Terms of Service (\"Terms&ldquo;) govern your use of VirtualRealTour's \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", [1901, 2249], "\n                  These Terms of Service (\"Terms&#34;) govern your use of VirtualRealTour's \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", [1901, 2249], "\n                  These Terms of Service (\"Terms&rdquo;) govern your use of VirtualRealTour's \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", [1901, 2249], "\n                  These Terms of Service (\"Terms\") govern your use of VirtualRealTour&apos;s \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", [1901, 2249], "\n                  These Terms of Service (\"Terms\") govern your use of VirtualRealTour&lsquo;s \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", [1901, 2249], "\n                  These Terms of Service (\"Terms\") govern your use of VirtualRealTour&#39;s \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", [1901, 2249], "\n                  These Terms of Service (\"Terms\") govern your use of VirtualRealTour&rsquo;s \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                ", [11019, 11218], "\n                    We strive to provide reliable services but cannot guarantee 100% uptime. \n                    Our services are provided &quot;as is\" without warranties of any kind.\n                  ", [11019, 11218], "\n                    We strive to provide reliable services but cannot guarantee 100% uptime. \n                    Our services are provided &ldquo;as is\" without warranties of any kind.\n                  ", [11019, 11218], "\n                    We strive to provide reliable services but cannot guarantee 100% uptime. \n                    Our services are provided &#34;as is\" without warranties of any kind.\n                  ", [11019, 11218], "\n                    We strive to provide reliable services but cannot guarantee 100% uptime. \n                    Our services are provided &rdquo;as is\" without warranties of any kind.\n                  ", [11019, 11218], "\n                    We strive to provide reliable services but cannot guarantee 100% uptime. \n                    Our services are provided \"as is&quot; without warranties of any kind.\n                  ", [11019, 11218], "\n                    We strive to provide reliable services but cannot guarantee 100% uptime. \n                    Our services are provided \"as is&ldquo; without warranties of any kind.\n                  ", [11019, 11218], "\n                    We strive to provide reliable services but cannot guarantee 100% uptime. \n                    Our services are provided \"as is&#34; without warranties of any kind.\n                  ", [11019, 11218], "\n                    We strive to provide reliable services but cannot guarantee 100% uptime. \n                    Our services are provided \"as is&rdquo; without warranties of any kind.\n                  ", [1015, 1018], "unknown", [1015, 1018], "never", [1045, 1048], [1045, 1048], [2950, 2953], [2950, 2953], [2964, 2967], [2964, 2967], [2358, 2361], [2358, 2361], [483, 486], [483, 486], [3158, 3208], "[options.userId]", [4521, 4524], [4521, 4524], [7830, 7833], [7830, 7833], [7979, 7982], [7979, 7982], [8342, 8345], [8342, 8345], [8860, 8863], [8860, 8863], [226, 229], [226, 229], [595, 598], [595, 598], [1945, 1948], [1945, 1948], [2112, 2115], [2112, 2115], [2151, 2154], [2151, 2154], [2377, 2380], [2377, 2380], [2416, 2419], [2416, 2419], [2643, 2646], [2643, 2646], [2682, 2685], [2682, 2685], [2912, 2915], [2912, 2915], [3108, 3111], [3108, 3111], [229, 232], [229, 232], [351, 354], [351, 354], [2711, 2714], [2711, 2714], [3915, 3918], [3915, 3918], [5439, 5442], [5439, 5442], [6019, 6022], [6019, 6022], [6519, 6522], [6519, 6522], [6744, 6747], [6744, 6747], [7680, 7683], [7680, 7683], [6242, 6245], [6242, 6245], [10185, 10188], [10185, 10188], [10671, 10674], [10671, 10674], [11171, 11174], [11171, 11174], [7884, 7887], [7884, 7887], [5473, 5476], [5473, 5476], [2397, 2400], [2397, 2400], [3550, 3553], [3550, 3553], [4495, 4498], [4495, 4498], [5641, 5644], [5641, 5644], [5886, 5889], [5886, 5889], [6163, 6166], [6163, 6166], [6267, 6270], [6267, 6270], [7466, 7469], [7466, 7469], [7597, 7600], [7597, 7600], [752, 755], [752, 755], [1434, 1437], [1434, 1437], [2120, 2123], [2120, 2123], [2969, 2972], [2969, 2972], [3973, 3976], [3973, 3976], [4986, 4989], [4986, 4989], [5518, 5521], [5518, 5521], [5566, 5569], [5566, 5569], [5614, 5617], [5614, 5617], [5965, 5968], [5965, 5968], [6014, 6017], [6014, 6017], [6063, 6066], [6063, 6066], [6419, 6422], [6419, 6422], [6468, 6471], [6468, 6471], [6517, 6520], [6517, 6520], [6817, 6820], [6817, 6820], [6858, 6861], [6858, 6861], [6903, 6906], [6903, 6906], [7147, 7150], [7147, 7150], [7199, 7202], [7199, 7202], [7248, 7251], [7248, 7251], [7556, 7559], [7556, 7559], [7598, 7601], [7598, 7601], [7644, 7647], [7644, 7647], [7894, 7897], [7894, 7897], [7947, 7950], [7947, 7950], [7997, 8000], [7997, 8000], [8311, 8314], [8311, 8314], [8353, 8356], [8353, 8356], [8399, 8402], [8399, 8402], [8649, 8652], [8649, 8652], [8702, 8705], [8702, 8705], [8752, 8755], [8752, 8755], [9184, 9187], [9184, 9187], [9315, 9318], [9315, 9318], [9474, 9477], [9474, 9477], [9923, 9926], [9923, 9926], [10057, 10060], [10057, 10060], [10220, 10223], [10220, 10223], [10678, 10681], [10678, 10681], [10812, 10815], [10812, 10815], [10975, 10978], [10975, 10978], [11866, 11869], [11866, 11869], [12669, 12672], [12669, 12672], [13480, 13483], [13480, 13483], [13868, 13871], [13868, 13871], [14480, 14483], [14480, 14483], [15102, 15105], [15102, 15105]]