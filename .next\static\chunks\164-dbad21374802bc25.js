"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[164],{133:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},620:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],f=[],d=1,p=null,h=3,m=!1,v=!1,b=!1,y="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(f);null!==t;){if(null===t.callback)o(f);else if(t.startTime<=e)o(f),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(f)}}function E(e){if(b=!1,j(e),!v)if(null!==r(u))v=!0,_();else{var t=r(f);null!==t&&S(E,t.startTime-e)}}var x=!1,P=-1,M=5,k=-1;function C(){return!(t.unstable_now()-k<M)}function O(){if(x){var e=t.unstable_now();k=e;var n=!0;try{e:{v=!1,b&&(b=!1,g(P),P=-1),m=!0;var i=h;try{t:{for(j(e),p=r(u);null!==p&&!(p.expirationTime>e&&C());){var l=p.callback;if("function"==typeof l){p.callback=null,h=p.priorityLevel;var s=l(p.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){p.callback=s,j(e),n=!0;break t}p===r(u)&&o(u),j(e)}else o(u);p=r(u)}if(null!==p)n=!0;else{var c=r(f);null!==c&&S(E,c.startTime-e),n=!1}}break e}finally{p=null,h=i,m=!1}}}finally{n?a():x=!1}}}if("function"==typeof w)a=function(){w(O)};else if("undefined"!=typeof MessageChannel){var T=new MessageChannel,A=T.port2;T.port1.onmessage=O,a=function(){A.postMessage(null)}}else a=function(){y(O,0)};function _(){x||(x=!0,a())}function S(e,n){P=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){v||m||(v=!0,_())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:d++,callback:o,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>a?(e.sortIndex=i,n(f,e),null===r(u)&&e===r(f)&&(b?(g(P),P=-1):b=!0,S(E,i-a))):(e.sortIndex=l,n(u,e),v||m||(v=!0,_())),e},t.unstable_shouldYield=C,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},1933:(e,t,n)=>{e.exports=n(6500)},2152:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("volume-x",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]])},2178:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},3816:(e,t,n)=>{let r,o,i,a,l;n.d(t,{A:()=>eu,B:()=>D,C:()=>ef,E:()=>F,F:()=>em,a:()=>N,b:()=>z,c:()=>ez,d:()=>eH,e:()=>ej,f:()=>eG,i:()=>L,u:()=>H});var s=n(3264),c=n(7431),u=n(2115),f=n.t(u,2),d=n(1933),p=n(5643);let h=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},m=e=>e?h(e):h,{useSyncExternalStoreWithSelector:v}=p,b=e=>e,y=(e,t)=>{let n=m(e),r=(e,r=t)=>(function(e,t=b,n){let r=v(e.subscribe,e.getState,e.getInitialState,t,n);return u.useDebugValue(r),r})(n,e,r);return Object.assign(r,n),r},g=(e,t)=>e?y(e,t):y;var w=n(5220),j=n.n(w),E=n(4342);let x=e=>"object"==typeof e&&"function"==typeof e.then,P=[];function M(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let r=e.length;if(t.length!==r)return!1;for(let o=0;o<r;o++)if(!n(e[o],t[o]))return!1;return!0}function k(e,t=null,n=!1,r={}){for(let o of(null===t&&(t=[e]),P))if(M(t,o.keys,o.equal)){if(n)return;if(Object.prototype.hasOwnProperty.call(o,"error"))throw o.error;if(Object.prototype.hasOwnProperty.call(o,"response"))return r.lifespan&&r.lifespan>0&&(o.timeout&&clearTimeout(o.timeout),o.timeout=setTimeout(o.remove,r.lifespan)),o.response;if(!n)throw o.promise}let o={keys:t,equal:r.equal,remove:()=>{let e=P.indexOf(o);-1!==e&&P.splice(e,1)},promise:(x(e)?e:e(...t)).then(e=>{o.response=e,r.lifespan&&r.lifespan>0&&(o.timeout=setTimeout(o.remove,r.lifespan))}).catch(e=>o.error=e)};if(P.push(o),!n)throw o.promise}let C=(e,t,n)=>k(e,t,!1,n),O=(e,t,n)=>void k(e,t,!0,n),T=e=>{if(void 0===e||0===e.length)P.splice(0,P.length);else{let t=P.find(t=>M(e,t.keys,t.equal));t&&t.remove()}};var A=n(5155),_=n(6354);function S(e){let t=e.root;for(;t.getState().previousRoot;)t=t.getState().previousRoot;return t}n(9538),f.act;let I=e=>e&&e.isOrthographicCamera,L=e=>e&&e.hasOwnProperty("current"),R=e=>null!=e&&("string"==typeof e||"number"==typeof e||e.isColor),z=((e,t)=>"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative"))()?u.useLayoutEffect:u.useEffect;function N(e){let t=u.useRef(e);return z(()=>void(t.current=e),[e]),t}function H(){let e=(0,_.u5)(),t=(0,_.y3)();return u.useMemo(()=>({children:n})=>{let r=(0,_.Nz)(e,!0,e=>e.type===u.StrictMode)?u.StrictMode:u.Fragment;return(0,A.jsx)(r,{children:(0,A.jsx)(t,{children:n})})},[e,t])}function D({set:e}){return z(()=>(e(new Promise(()=>null)),()=>e(!1)),[e]),null}let F=(e=>((e=class extends u.Component{constructor(...e){super(...e),this.state={error:!1}}componentDidCatch(e){this.props.set(e)}render(){return this.state.error?null:this.props.children}}).getDerivedStateFromError=()=>({error:!0}),e))();function Y(e){var t;let n="undefined"!=typeof window?null!=(t=window.devicePixelRatio)?t:2:1;return Array.isArray(e)?Math.min(Math.max(e[0],n),e[1]):e}function q(e){var t;return null==(t=e.__r3f)?void 0:t.root.getState()}let W={obj:e=>e===Object(e)&&!W.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,nul:e=>null===e,arr:e=>Array.isArray(e),equ(e,t,{arrays:n="shallow",objects:r="reference",strict:o=!0}={}){let i;if(typeof e!=typeof t||!!e!=!!t)return!1;if(W.str(e)||W.num(e)||W.boo(e))return e===t;let a=W.obj(e);if(a&&"reference"===r)return e===t;let l=W.arr(e);if(l&&"reference"===n)return e===t;if((l||a)&&e===t)return!0;for(i in e)if(!(i in t))return!1;if(a&&"shallow"===n&&"shallow"===r){for(i in o?t:e)if(!W.equ(e[i],t[i],{strict:o,objects:"reference"}))return!1}else for(i in o?t:e)if(e[i]!==t[i])return!1;if(W.und(i)){if(l&&0===e.length&&0===t.length||a&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}},U=["children","key","ref"];function $(e,t,n,r){let o=null==e?void 0:e.__r3f;return!o&&(o={root:t,type:n,parent:null,children:[],props:function(e){let t={};for(let n in e)U.includes(n)||(t[n]=e[n]);return t}(r),object:e,eventCount:0,handlers:{},isHidden:!1},e&&(e.__r3f=o)),o}function B(e,t){let n=e[t];if(!t.includes("-"))return{root:e,key:t,target:n};for(let o of(n=e,t.split("-"))){var r;t=o,e=n,n=null==(r=n)?void 0:r[t]}return{root:e,key:t,target:n}}let V=/-\d+$/;function X(e,t){if(W.str(t.props.attach)){if(V.test(t.props.attach)){let n=t.props.attach.replace(V,""),{root:r,key:o}=B(e.object,n);Array.isArray(r[o])||(r[o]=[])}let{root:n,key:r}=B(e.object,t.props.attach);t.previousAttach=n[r],n[r]=t.object}else W.fun(t.props.attach)&&(t.previousAttach=t.props.attach(e.object,t.object))}function Z(e,t){if(W.str(t.props.attach)){let{root:n,key:r}=B(e.object,t.props.attach),o=t.previousAttach;void 0===o?delete n[r]:n[r]=o}else null==t.previousAttach||t.previousAttach(e.object,t.object);delete t.previousAttach}let K=[...U,"args","dispose","attach","object","onUpdate","dispose"],G=new Map,Q=["map","emissiveMap","sheenColorMap","specularColorMap","envMap"],J=/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;function ee(e,t){var n,r;let o=e.__r3f,i=o&&S(o).getState(),a=null==o?void 0:o.eventCount;for(let n in t){let a=t[n];if(K.includes(n))continue;if(o&&J.test(n)){"function"==typeof a?o.handlers[n]=a:delete o.handlers[n],o.eventCount=Object.keys(o.handlers).length;continue}if(void 0===a)continue;let{root:l,key:c,target:u}=B(e,n);u instanceof s.zgK&&a instanceof s.zgK?u.mask=a.mask:u instanceof s.Q1f&&R(a)?u.set(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"function"==typeof u.copy&&null!=a&&a.constructor&&u.constructor===a.constructor?u.copy(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&Array.isArray(a)?"function"==typeof u.fromArray?u.fromArray(a):u.set(...a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"number"==typeof a?"function"==typeof u.setScalar?u.setScalar(a):u.set(a):(l[c]=a,i&&!i.linear&&Q.includes(c)&&null!=(r=l[c])&&r.isTexture&&l[c].format===s.GWd&&l[c].type===s.OUM&&(l[c].colorSpace=s.er$))}if(null!=o&&o.parent&&null!=i&&i.internal&&null!=(n=o.object)&&n.isObject3D&&a!==o.eventCount){let e=o.object,t=i.internal.interaction.indexOf(e);t>-1&&i.internal.interaction.splice(t,1),o.eventCount&&null!==e.raycast&&i.internal.interaction.push(e)}return o&&void 0===o.props.attach&&(o.object.isBufferGeometry?o.props.attach="geometry":o.object.isMaterial&&(o.props.attach="material")),o&&et(o),e}function et(e){var t;if(!e.parent)return;null==e.props.onUpdate||e.props.onUpdate(e.object);let n=null==(t=e.root)||null==t.getState?void 0:t.getState();n&&0===n.internal.frames&&n.invalidate()}function en(e,t){e.manual||(I(e)?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix())}let er=e=>null==e?void 0:e.isObject3D;function eo(e){return(e.eventObject||e.object).uuid+"/"+e.index+e.instanceId}function ei(e,t,n,r){let o=n.get(t);o&&(n.delete(t),0===n.size&&(e.delete(r),o.target.releasePointerCapture(r)))}let ea=e=>!!(null!=e&&e.render),el=u.createContext(null),es=(e,t)=>{let n=g((n,r)=>{let o,i=new s.Pq0,a=new s.Pq0,l=new s.Pq0;function c(e=r().camera,t=a,n=r().size){let{width:o,height:s,top:u,left:f}=n,d=o/s;t.isVector3?l.copy(t):l.set(...t);let p=e.getWorldPosition(i).distanceTo(l);if(I(e))return{width:o/e.zoom,height:s/e.zoom,top:u,left:f,factor:1,distance:p,aspect:d};{let t=2*Math.tan(e.fov*Math.PI/180/2)*p,n=o/s*t;return{width:n,height:t,top:u,left:f,factor:o/n,distance:p,aspect:d}}}let f=e=>n(t=>({performance:{...t.performance,current:e}})),d=new s.I9Y;return{set:n,get:r,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},scene:null,xr:null,invalidate:(t=1)=>e(r(),t),advance:(e,n)=>t(e,n,r()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new s.zD7,pointer:d,mouse:d,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let e=r();o&&clearTimeout(o),e.performance.current!==e.performance.min&&f(e.performance.min),o=setTimeout(()=>f(r().performance.max),e.performance.debounce)}},size:{width:0,height:0,top:0,left:0},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:c},setEvents:e=>n(t=>({...t,events:{...t.events,...e}})),setSize:(e,t,o=0,i=0)=>{let l=r().camera,s={width:e,height:t,top:o,left:i};n(e=>({size:s,viewport:{...e.viewport,...c(l,a,s)}}))},setDpr:e=>n(t=>{let n=Y(e);return{viewport:{...t.viewport,dpr:n,initialDpr:t.viewport.initialDpr||n}}}),setFrameloop:(e="always")=>{let t=r().clock;t.stop(),t.elapsedTime=0,"never"!==e&&(t.start(),t.elapsedTime=0),n(()=>({frameloop:e}))},previousRoot:void 0,internal:{interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,lastEvent:u.createRef(),active:!1,frames:0,priority:0,subscribe:(e,t,n)=>{let o=r().internal;return o.priority=o.priority+ +(t>0),o.subscribers.push({ref:e,priority:t,store:n}),o.subscribers=o.subscribers.sort((e,t)=>e.priority-t.priority),()=>{let n=r().internal;null!=n&&n.subscribers&&(n.priority=n.priority-(t>0),n.subscribers=n.subscribers.filter(t=>t.ref!==e))}}}}}),r=n.getState(),o=r.size,i=r.viewport.dpr,a=r.camera;return n.subscribe(()=>{let{camera:e,size:t,viewport:r,gl:l,set:s}=n.getState();if(t.width!==o.width||t.height!==o.height||r.dpr!==i){o=t,i=r.dpr,en(e,t),r.dpr>0&&l.setPixelRatio(r.dpr);let n="undefined"!=typeof HTMLCanvasElement&&l.domElement instanceof HTMLCanvasElement;l.setSize(t.width,t.height,n)}e!==a&&(a=e,s(t=>({viewport:{...t.viewport,...t.viewport.getCurrentViewport(e)}})))}),n.subscribe(t=>e(t)),n};function ec(){let e=u.useContext(el);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function eu(e=e=>e,t){return ec()(e,t)}function ef(e,t=0){let n=ec(),r=n.getState().internal.subscribe,o=N(e);return z(()=>r(o,t,n),[t,r,n]),null}let ed=new WeakMap,ep=e=>{var t;return"function"==typeof e&&(null==e||null==(t=e.prototype)?void 0:t.constructor)===e};function eh(e,t){return function(n,...r){let o;return ep(n)?(o=ed.get(n))||(o=new n,ed.set(n,o)):o=n,e&&e(o),Promise.all(r.map(e=>new Promise((n,r)=>o.load(e,e=>{er(null==e?void 0:e.scene)&&Object.assign(e,function(e){let t={nodes:{},materials:{},meshes:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material),e.isMesh&&!t.meshes[e.name]&&(t.meshes[e.name]=e)}),t}(e.scene)),n(e)},t,t=>r(Error(`Could not load ${e}: ${null==t?void 0:t.message}`))))))}}function em(e,t,n,r){let o=Array.isArray(t)?t:[t],i=C(eh(n,r),[e,...o],{equal:W.equ});return Array.isArray(t)?i:i[0]}em.preload=function(e,t,n){let r=Array.isArray(t)?t:[t];return O(eh(n),[e,...r])},em.clear=function(e,t){return T([e,...Array.isArray(t)?t:[t]])};let ev={},eb=/^three(?=[A-Z])/,ey=e=>`${e[0].toUpperCase()}${e.slice(1)}`,eg=0,ew=e=>"function"==typeof e;function ej(e){if(ew(e)){let t=`${eg++}`;return ev[t]=e,t}Object.assign(ev,e)}function eE(e,t){let n=ey(e),r=ev[n];if("primitive"!==e&&!r)throw Error(`R3F: ${n} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);if("primitive"===e&&!t.object)throw Error("R3F: Primitives without 'object' are invalid!");if(void 0!==t.args&&!Array.isArray(t.args))throw Error("R3F: The args prop must be an array!")}function ex(e){if(e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?X(e.parent,e):er(e.object)&&!1!==e.props.visible&&(e.object.visible=!0),e.isHidden=!1,et(e)}}function eP(e,t,n){let r=t.root.getState();if(e.parent||e.object===r.scene){if(!t.object){var o,i;let e=ev[ey(t.type)];t.object=null!=(o=t.props.object)?o:new e(...null!=(i=t.props.args)?i:[]),t.object.__r3f=t}if(ee(t.object,t.props),t.props.attach)X(e,t);else if(er(t.object)&&er(e.object)){let r=e.object.children.indexOf(null==n?void 0:n.object);if(n&&-1!==r){let n=e.object.children.indexOf(t.object);-1!==n?(e.object.children.splice(n,1),e.object.children.splice(n<r?r-1:r,0,t.object)):(t.object.parent=e.object,e.object.children.splice(r,0,t.object),t.object.dispatchEvent({type:"added"}),e.object.dispatchEvent({type:"childadded",child:t.object}))}else e.object.add(t.object)}for(let e of t.children)eP(t,e);et(t)}}function eM(e,t){t&&(t.parent=e,e.children.push(t),eP(e,t))}function ek(e,t,n){if(!t||!n)return;t.parent=e;let r=e.children.indexOf(n);-1!==r?e.children.splice(r,0,t):e.children.push(t),eP(e,t,n)}function eC(e){if("function"==typeof e.dispose){let t=()=>{try{e.dispose()}catch{}};"undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?t():(0,E.unstable_scheduleCallback)(E.unstable_IdlePriority,t)}}function eO(e,t,n){if(!t)return;t.parent=null;let r=e.children.indexOf(t);-1!==r&&e.children.splice(r,1),t.props.attach?Z(e,t):er(t.object)&&er(e.object)&&(e.object.remove(t.object),function(e,t){let{internal:n}=e.getState();n.interaction=n.interaction.filter(e=>e!==t),n.initialHits=n.initialHits.filter(e=>e!==t),n.hovered.forEach((e,r)=>{(e.eventObject===t||e.object===t)&&n.hovered.delete(r)}),n.capturedMap.forEach((e,r)=>{ei(n.capturedMap,t,e,r)})}(S(t),t.object));let o=null!==t.props.dispose&&!1!==n;for(let e=t.children.length-1;e>=0;e--){let n=t.children[e];eO(t,n,o)}t.children.length=0,delete t.object.__r3f,o&&"primitive"!==t.type&&"Scene"!==t.object.type&&eC(t.object),void 0===n&&et(t)}let eT=[],eA=()=>{},e_={},eS=0,eI=function(e){let t=j()(e);return t.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:u.version}),t}({isPrimaryRenderer:!1,warnsIfNotActing:!1,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,createInstance:function(e,t,n){var r;return eE(e=ey(e)in ev?e:e.replace(eb,""),t),"primitive"===e&&null!=(r=t.object)&&r.__r3f&&delete t.object.__r3f,$(t.object,n,e,t)},removeChild:eO,appendChild:eM,appendInitialChild:eM,insertBefore:ek,appendChildToContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&eM(n,t)},removeChildFromContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&eO(n,t)},insertInContainerBefore(e,t,n){let r=e.getState().scene.__r3f;t&&n&&r&&ek(r,t,n)},getRootHostContext:()=>e_,getChildHostContext:()=>e_,commitUpdate(e,t,n,r,o){var i,a,l;eE(t,r);let s=!1;if("primitive"===e.type&&n.object!==r.object||(null==(i=r.args)?void 0:i.length)!==(null==(a=n.args)?void 0:a.length)?s=!0:null!=(l=r.args)&&l.some((e,t)=>{var r;return e!==(null==(r=n.args)?void 0:r[t])})&&(s=!0),s)eT.push([e,{...r},o]);else{let t=function(e,t){let n={};for(let r in t)if(!K.includes(r)&&!W.equ(t[r],e.props[r]))for(let e in n[r]=t[r],t)e.startsWith(`${r}-`)&&(n[e]=t[e]);for(let r in e.props){if(K.includes(r)||t.hasOwnProperty(r))continue;let{root:o,key:i}=B(e.object,r);if(o.constructor&&0===o.constructor.length){let e=function(e){let t=G.get(e.constructor);try{t||(t=new e.constructor,G.set(e.constructor,t))}catch(e){}return t}(o);W.und(e)||(n[i]=e[i])}else n[i]=0}return n}(e,r);Object.keys(t).length&&(Object.assign(e.props,t),ee(e.object,t))}(null===o.sibling||(4&o.flags)==0)&&function(){for(let[e]of eT){let t=e.parent;if(t)for(let n of(e.props.attach?Z(t,e):er(e.object)&&er(t.object)&&t.object.remove(e.object),e.children))n.props.attach?Z(e,n):er(n.object)&&er(e.object)&&e.object.remove(n.object);e.isHidden&&ex(e),e.object.__r3f&&delete e.object.__r3f,"primitive"!==e.type&&eC(e.object)}for(let[r,o,i]of eT){r.props=o;let a=r.parent;if(a){let o=ev[ey(r.type)];r.object=null!=(e=r.props.object)?e:new o(...null!=(t=r.props.args)?t:[]),r.object.__r3f=r;var e,t,n=r.object;for(let e of[i,i.alternate])if(null!==e)if("function"==typeof e.ref){null==e.refCleanup||e.refCleanup();let t=e.ref(n);"function"==typeof t&&(e.refCleanup=t)}else e.ref&&(e.ref.current=n);for(let e of(ee(r.object,r.props),r.props.attach?X(a,r):er(r.object)&&er(a.object)&&a.object.add(r.object),r.children))e.props.attach?X(r,e):er(e.object)&&er(r.object)&&r.object.add(e.object);et(r)}}eT.length=0}()},finalizeInitialChildren:()=>!1,commitMount(){},getPublicInstance:e=>null==e?void 0:e.object,prepareForCommit:()=>null,preparePortalMount:e=>$(e.getState().scene,e,"",{}),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance:function(e){if(!e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?Z(e.parent,e):er(e.object)&&(e.object.visible=!1),e.isHidden=!0,et(e)}},unhideInstance:ex,createTextInstance:eA,hideTextInstance:eA,unhideTextInstance:eA,scheduleTimeout:"function"==typeof setTimeout?setTimeout:void 0,cancelTimeout:"function"==typeof clearTimeout?clearTimeout:void 0,noTimeout:-1,getInstanceFromNode:()=>null,beforeActiveInstanceBlur(){},afterActiveInstanceBlur(){},detachDeletedInstance(){},prepareScopeUpdate(){},getInstanceFromScope:()=>null,shouldAttemptEagerTransition:()=>!1,trackSchedulerEvent:()=>{},resolveEventType:()=>null,resolveEventTimeStamp:()=>-1.1,requestPostPaintCallback(){},maySuspendCommit:()=>!1,preloadInstance:()=>!0,startSuspendingCommit(){},suspendInstance(){},waitForCommitToBeReady:()=>null,NotPendingTransition:null,HostTransitionContext:u.createContext(null),setCurrentUpdatePriority(e){eS=e},getCurrentUpdatePriority:()=>eS,resolveUpdatePriority(){var e;if(0!==eS)return eS;switch("undefined"!=typeof window&&(null==(e=window.event)?void 0:e.type)){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return d.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return d.ContinuousEventPriority;default:return d.DefaultEventPriority}},resetFormInstance(){}}),eL=new Map,eR={objects:"shallow",strict:!1};function ez(e){let t,n,r=eL.get(e),o=null==r?void 0:r.fiber,i=null==r?void 0:r.store;r&&console.warn("R3F.createRoot should only be called once!");let a="function"==typeof reportError?reportError:console.error,l=i||es(eX,eZ),u=o||eI.createContainer(l,d.ConcurrentRoot,null,!1,null,"",a,a,a,null);r||eL.set(e,{fiber:u,store:l});let f=!1,p=null;return{async configure(r={}){var o,i;let a;p=new Promise(e=>a=e);let{gl:u,size:d,scene:h,events:m,onCreated:v,shadows:b=!1,linear:y=!1,flat:g=!1,legacy:w=!1,orthographic:j=!1,frameloop:E="always",dpr:x=[1,2],performance:P,raycaster:M,camera:k,onPointerMissed:C}=r,O=l.getState(),T=O.gl;if(!O.gl){let t={canvas:e,powerPreference:"high-performance",antialias:!0,alpha:!0},n="function"==typeof u?await u(t):u;T=ea(n)?n:new c.WebGLRenderer({...t,...u}),O.set({gl:T})}let A=O.raycaster;A||O.set({raycaster:A=new s.tBo});let{params:_,...S}=M||{};if(W.equ(S,A,eR)||ee(A,{...S}),W.equ(_,A.params,eR)||ee(A,{params:{...A.params,..._}}),!O.camera||O.camera===n&&!W.equ(n,k,eR)){n=k;let e=null==k?void 0:k.isCamera,t=e?k:j?new s.qUd(0,0,0,0,.1,1e3):new s.ubm(75,0,.1,1e3);!e&&(t.position.z=5,k&&(ee(t,k),!t.manual&&("aspect"in k||"left"in k||"right"in k||"bottom"in k||"top"in k)&&(t.manual=!0,t.updateProjectionMatrix())),O.camera||null!=k&&k.rotation||t.lookAt(0,0,0)),O.set({camera:t}),A.camera=t}if(!O.scene){let e;null!=h&&h.isScene?$(e=h,l,"",{}):($(e=new s.Z58,l,"",{}),h&&ee(e,h)),O.set({scene:e})}m&&!O.events.handlers&&O.set({events:m(l)});let I=function(e,t){if(!t&&"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&e.parentElement){let{width:t,height:n,top:r,left:o}=e.parentElement.getBoundingClientRect();return{width:t,height:n,top:r,left:o}}return!t&&"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas?{width:e.width,height:e.height,top:0,left:0}:{width:0,height:0,top:0,left:0,...t}}(e,d);if(W.equ(I,O.size,eR)||O.setSize(I.width,I.height,I.top,I.left),x&&O.viewport.dpr!==Y(x)&&O.setDpr(x),O.frameloop!==E&&O.setFrameloop(E),O.onPointerMissed||O.set({onPointerMissed:C}),P&&!W.equ(P,O.performance,eR)&&O.set(e=>({performance:{...e.performance,...P}})),!O.xr){let e=(e,t)=>{let n=l.getState();"never"!==n.frameloop&&eZ(e,!0,n,t)},t=()=>{let t=l.getState();t.gl.xr.enabled=t.gl.xr.isPresenting,t.gl.xr.setAnimationLoop(t.gl.xr.isPresenting?e:null),t.gl.xr.isPresenting||eX(t)},n={connect(){let e=l.getState().gl;e.xr.addEventListener("sessionstart",t),e.xr.addEventListener("sessionend",t)},disconnect(){let e=l.getState().gl;e.xr.removeEventListener("sessionstart",t),e.xr.removeEventListener("sessionend",t)}};"function"==typeof(null==(o=T.xr)?void 0:o.addEventListener)&&n.connect(),O.set({xr:n})}if(T.shadowMap){let e=T.shadowMap.enabled,t=T.shadowMap.type;if(T.shadowMap.enabled=!!b,W.boo(b))T.shadowMap.type=s.Wk7;else if(W.str(b)){let e={basic:s.bTm,percentage:s.QP0,soft:s.Wk7,variance:s.RyA};T.shadowMap.type=null!=(i=e[b])?i:s.Wk7}else W.obj(b)&&Object.assign(T.shadowMap,b);(e!==T.shadowMap.enabled||t!==T.shadowMap.type)&&(T.shadowMap.needsUpdate=!0)}return s.ppV.enabled=!w,f||(T.outputColorSpace=y?s.Zr2:s.er$,T.toneMapping=g?s.y_p:s.FV),O.legacy!==w&&O.set(()=>({legacy:w})),O.linear!==y&&O.set(()=>({linear:y})),O.flat!==g&&O.set(()=>({flat:g})),!u||W.fun(u)||ea(u)||W.equ(u,T,eR)||ee(T,u),t=v,f=!0,a(),this},render(n){return f||p||this.configure(),p.then(()=>{eI.updateContainer((0,A.jsx)(eN,{store:l,children:n,onCreated:t,rootElement:e}),u,null,()=>void 0)}),l},unmount(){eH(e)}}}function eN({store:e,children:t,onCreated:n,rootElement:r}){return z(()=>{let t=e.getState();t.set(e=>({internal:{...e.internal,active:!0}})),n&&n(t),e.getState().events.connected||null==t.events.connect||t.events.connect(r)},[]),(0,A.jsx)(el.Provider,{value:e,children:t})}function eH(e,t){let n=eL.get(e),r=null==n?void 0:n.fiber;if(r){let o=null==n?void 0:n.store.getState();o&&(o.internal.active=!1),eI.updateContainer(null,r,null,()=>{o&&setTimeout(()=>{try{null==o.events.disconnect||o.events.disconnect(),null==(n=o.gl)||null==(r=n.renderLists)||null==r.dispose||r.dispose(),null==(i=o.gl)||null==i.forceContextLoss||i.forceContextLoss(),null!=(a=o.gl)&&a.xr&&o.xr.disconnect();var n,r,i,a,l=o.scene;for(let e in"Scene"!==l.type&&(null==l.dispose||l.dispose()),l){let t=l[e];(null==t?void 0:t.type)!=="Scene"&&(null==t||null==t.dispose||t.dispose())}eL.delete(e),t&&t(e)}catch(e){}},500)})}}let eD=new Set,eF=new Set,eY=new Set;function eq(e,t){if(e.size)for(let{callback:n}of e.values())n(t)}function eW(e,t){switch(e){case"before":return eq(eD,t);case"after":return eq(eF,t);case"tail":return eq(eY,t)}}function eU(e,t,n){let i=t.clock.getDelta();"never"===t.frameloop&&"number"==typeof e&&(i=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),r=t.internal.subscribers;for(let e=0;e<r.length;e++)(o=r[e]).ref.current(o.store.getState(),i,n);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}let e$=!1,eB=!1;function eV(e){for(let n of(a=requestAnimationFrame(eV),e$=!0,i=0,eW("before",e),eB=!0,eL.values())){var t;(l=n.store.getState()).internal.active&&("always"===l.frameloop||l.internal.frames>0)&&!(null!=(t=l.gl.xr)&&t.isPresenting)&&(i+=eU(e,l))}if(eB=!1,eW("after",e),0===i)return eW("tail",e),e$=!1,cancelAnimationFrame(a)}function eX(e,t=1){var n;if(!e)return eL.forEach(e=>eX(e.store.getState(),t));(null==(n=e.gl.xr)||!n.isPresenting)&&e.internal.active&&"never"!==e.frameloop&&(t>1?e.internal.frames=Math.min(60,e.internal.frames+t):eB?e.internal.frames=2:e.internal.frames=1,e$||(e$=!0,requestAnimationFrame(eV)))}function eZ(e,t=!0,n,r){if(t&&eW("before",e),n)eU(e,n,r);else for(let t of eL.values())eU(e,t.store.getState());t&&eW("after",e)}let eK={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function eG(e){let{handlePointer:t}=function(e){function t(e){return e.filter(e=>["Move","Over","Enter","Out","Leave"].some(t=>{var n;return null==(n=e.__r3f)?void 0:n.handlers["onPointer"+t]}))}function n(t){let{internal:n}=e.getState();for(let e of n.hovered.values())if(!t.length||!t.find(t=>t.object===e.object&&t.index===e.index&&t.instanceId===e.instanceId)){let r=e.eventObject.__r3f;if(n.hovered.delete(eo(e)),null!=r&&r.eventCount){let n=r.handlers,o={...e,intersections:t};null==n.onPointerOut||n.onPointerOut(o),null==n.onPointerLeave||n.onPointerLeave(o)}}}function r(e,t){for(let n=0;n<t.length;n++){let r=t[n].__r3f;null==r||null==r.handlers.onPointerMissed||r.handlers.onPointerMissed(e)}}return{handlePointer:function(o){switch(o){case"onPointerLeave":case"onPointerCancel":return()=>n([]);case"onLostPointerCapture":return t=>{let{internal:r}=e.getState();"pointerId"in t&&r.capturedMap.has(t.pointerId)&&requestAnimationFrame(()=>{r.capturedMap.has(t.pointerId)&&(r.capturedMap.delete(t.pointerId),n([]))})}}return function(i){let{onPointerMissed:a,internal:l}=e.getState();l.lastEvent.current=i;let c="onPointerMove"===o,u="onClick"===o||"onContextMenu"===o||"onDoubleClick"===o,f=function(t,n){let r=e.getState(),o=new Set,i=[],a=n?n(r.internal.interaction):r.internal.interaction;for(let e=0;e<a.length;e++){let t=q(a[e]);t&&(t.raycaster.camera=void 0)}r.previousRoot||null==r.events.compute||r.events.compute(t,r);let l=a.flatMap(function(e){let n=q(e);if(!n||!n.events.enabled||null===n.raycaster.camera)return[];if(void 0===n.raycaster.camera){var r;null==n.events.compute||n.events.compute(t,n,null==(r=n.previousRoot)?void 0:r.getState()),void 0===n.raycaster.camera&&(n.raycaster.camera=null)}return n.raycaster.camera?n.raycaster.intersectObject(e,!0):[]}).sort((e,t)=>{let n=q(e.object),r=q(t.object);return n&&r&&r.events.priority-n.events.priority||e.distance-t.distance}).filter(e=>{let t=eo(e);return!o.has(t)&&(o.add(t),!0)});for(let e of(r.events.filter&&(l=r.events.filter(l,r)),l)){let t=e.object;for(;t;){var s;null!=(s=t.__r3f)&&s.eventCount&&i.push({...e,eventObject:t}),t=t.parent}}if("pointerId"in t&&r.internal.capturedMap.has(t.pointerId))for(let e of r.internal.capturedMap.get(t.pointerId).values())o.has(eo(e.intersection))||i.push(e.intersection);return i}(i,c?t:void 0),d=u?function(t){let{internal:n}=e.getState(),r=t.offsetX-n.initialClick[0],o=t.offsetY-n.initialClick[1];return Math.round(Math.sqrt(r*r+o*o))}(i):0;"onPointerDown"===o&&(l.initialClick=[i.offsetX,i.offsetY],l.initialHits=f.map(e=>e.eventObject)),u&&!f.length&&d<=2&&(r(i,l.interaction),a&&a(i)),c&&n(f),!function(e,t,r,o){if(e.length){let i={stopped:!1};for(let a of e){let l=q(a.object);if(l||a.object.traverseAncestors(e=>{let t=q(e);if(t)return l=t,!1}),l){let{raycaster:c,pointer:u,camera:f,internal:d}=l,p=new s.Pq0(u.x,u.y,0).unproject(f),h=e=>{var t,n;return null!=(t=null==(n=d.capturedMap.get(e))?void 0:n.has(a.eventObject))&&t},m=e=>{let n={intersection:a,target:t.target};d.capturedMap.has(e)?d.capturedMap.get(e).set(a.eventObject,n):d.capturedMap.set(e,new Map([[a.eventObject,n]])),t.target.setPointerCapture(e)},v=e=>{let t=d.capturedMap.get(e);t&&ei(d.capturedMap,a.eventObject,t,e)},b={};for(let e in t){let n=t[e];"function"!=typeof n&&(b[e]=n)}let y={...a,...b,pointer:u,intersections:e,stopped:i.stopped,delta:r,unprojectedPoint:p,ray:c.ray,camera:f,stopPropagation(){let r="pointerId"in t&&d.capturedMap.get(t.pointerId);(!r||r.has(a.eventObject))&&(y.stopped=i.stopped=!0,d.hovered.size&&Array.from(d.hovered.values()).find(e=>e.eventObject===a.eventObject)&&n([...e.slice(0,e.indexOf(a)),a]))},target:{hasPointerCapture:h,setPointerCapture:m,releasePointerCapture:v},currentTarget:{hasPointerCapture:h,setPointerCapture:m,releasePointerCapture:v},nativeEvent:t};if(o(y),!0===i.stopped)break}}}}(f,i,d,function(e){let t=e.eventObject,n=t.__r3f;if(!(null!=n&&n.eventCount))return;let a=n.handlers;if(c){if(a.onPointerOver||a.onPointerEnter||a.onPointerOut||a.onPointerLeave){let t=eo(e),n=l.hovered.get(t);n?n.stopped&&e.stopPropagation():(l.hovered.set(t,e),null==a.onPointerOver||a.onPointerOver(e),null==a.onPointerEnter||a.onPointerEnter(e))}null==a.onPointerMove||a.onPointerMove(e)}else{let n=a[o];n?(!u||l.initialHits.includes(t))&&(r(i,l.interaction.filter(e=>!l.initialHits.includes(e))),n(e)):u&&l.initialHits.includes(t)&&r(i,l.interaction.filter(e=>!l.initialHits.includes(e)))}})}}}}(e);return{priority:1,enabled:!0,compute(e,t,n){t.pointer.set(e.offsetX/t.size.width*2-1,-(2*(e.offsetY/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)},connected:void 0,handlers:Object.keys(eK).reduce((e,n)=>({...e,[n]:t(n)}),{}),update:()=>{var t;let{events:n,internal:r}=e.getState();null!=(t=r.lastEvent)&&t.current&&n.handlers&&n.handlers.onPointerMove(r.lastEvent.current)},connect:t=>{let{set:n,events:r}=e.getState();if(null==r.disconnect||r.disconnect(),n(e=>({events:{...e.events,connected:t}})),r.handlers)for(let e in r.handlers){let n=r.handlers[e],[o,i]=eK[e];t.addEventListener(o,n,{passive:i})}},disconnect:()=>{let{set:t,events:n}=e.getState();if(n.connected){if(n.handlers)for(let e in n.handlers){let t=n.handlers[e],[r]=eK[e];n.connected.removeEventListener(r,t)}t(e=>({events:{...e.events,connected:void 0}}))}}}}},4342:(e,t,n)=>{e.exports=n(7319)},4688:(e,t,n)=>{n.d(t,{N:()=>g});var r=n(9630),o=n(3816),i=n(2115),a=n(3264),l=Object.defineProperty,s=(e,t,n)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,c=(e,t,n)=>(s(e,"symbol"!=typeof t?t+"":t,n),n);class u{constructor(){c(this,"_listeners")}addEventListener(e,t){void 0===this._listeners&&(this._listeners={});let n=this._listeners;void 0===n[e]&&(n[e]=[]),-1===n[e].indexOf(t)&&n[e].push(t)}hasEventListener(e,t){if(void 0===this._listeners)return!1;let n=this._listeners;return void 0!==n[e]&&-1!==n[e].indexOf(t)}removeEventListener(e,t){if(void 0===this._listeners)return;let n=this._listeners[e];if(void 0!==n){let e=n.indexOf(t);-1!==e&&n.splice(e,1)}}dispatchEvent(e){if(void 0===this._listeners)return;let t=this._listeners[e.type];if(void 0!==t){e.target=this;let n=t.slice(0);for(let t=0,r=n.length;t<r;t++)n[t].call(this,e);e.target=null}}}var f=Object.defineProperty,d=(e,t,n)=>t in e?f(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p=(e,t,n)=>(d(e,"symbol"!=typeof t?t+"":t,n),n);let h=new a.RlV,m=new a.Zcv,v=Math.cos(Math.PI/180*70),b=(e,t)=>(e%t+t)%t;class y extends u{constructor(e,t){super(),p(this,"object"),p(this,"domElement"),p(this,"enabled",!0),p(this,"target",new a.Pq0),p(this,"minDistance",0),p(this,"maxDistance",1/0),p(this,"minZoom",0),p(this,"maxZoom",1/0),p(this,"minPolarAngle",0),p(this,"maxPolarAngle",Math.PI),p(this,"minAzimuthAngle",-1/0),p(this,"maxAzimuthAngle",1/0),p(this,"enableDamping",!1),p(this,"dampingFactor",.05),p(this,"enableZoom",!0),p(this,"zoomSpeed",1),p(this,"enableRotate",!0),p(this,"rotateSpeed",1),p(this,"enablePan",!0),p(this,"panSpeed",1),p(this,"screenSpacePanning",!0),p(this,"keyPanSpeed",7),p(this,"zoomToCursor",!1),p(this,"autoRotate",!1),p(this,"autoRotateSpeed",2),p(this,"reverseOrbit",!1),p(this,"reverseHorizontalOrbit",!1),p(this,"reverseVerticalOrbit",!1),p(this,"keys",{LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"}),p(this,"mouseButtons",{LEFT:a.kBv.ROTATE,MIDDLE:a.kBv.DOLLY,RIGHT:a.kBv.PAN}),p(this,"touches",{ONE:a.wtR.ROTATE,TWO:a.wtR.DOLLY_PAN}),p(this,"target0"),p(this,"position0"),p(this,"zoom0"),p(this,"_domElementKeyEvents",null),p(this,"getPolarAngle"),p(this,"getAzimuthalAngle"),p(this,"setPolarAngle"),p(this,"setAzimuthalAngle"),p(this,"getDistance"),p(this,"getZoomScale"),p(this,"listenToKeyEvents"),p(this,"stopListenToKeyEvents"),p(this,"saveState"),p(this,"reset"),p(this,"update"),p(this,"connect"),p(this,"dispose"),p(this,"dollyIn"),p(this,"dollyOut"),p(this,"getScale"),p(this,"setScale"),this.object=e,this.domElement=t,this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this.getPolarAngle=()=>u.phi,this.getAzimuthalAngle=()=>u.theta,this.setPolarAngle=e=>{let t=b(e,2*Math.PI),r=u.phi;r<0&&(r+=2*Math.PI),t<0&&(t+=2*Math.PI);let o=Math.abs(t-r);2*Math.PI-o<o&&(t<r?t+=2*Math.PI:r+=2*Math.PI),f.phi=t-r,n.update()},this.setAzimuthalAngle=e=>{let t=b(e,2*Math.PI),r=u.theta;r<0&&(r+=2*Math.PI),t<0&&(t+=2*Math.PI);let o=Math.abs(t-r);2*Math.PI-o<o&&(t<r?t+=2*Math.PI:r+=2*Math.PI),f.theta=t-r,n.update()},this.getDistance=()=>n.object.position.distanceTo(n.target),this.listenToKeyEvents=e=>{e.addEventListener("keydown",ee),this._domElementKeyEvents=e},this.stopListenToKeyEvents=()=>{this._domElementKeyEvents.removeEventListener("keydown",ee),this._domElementKeyEvents=null},this.saveState=()=>{n.target0.copy(n.target),n.position0.copy(n.object.position),n.zoom0=n.object.zoom},this.reset=()=>{n.target.copy(n.target0),n.object.position.copy(n.position0),n.object.zoom=n.zoom0,n.object.updateProjectionMatrix(),n.dispatchEvent(r),n.update(),s=l.NONE},this.update=(()=>{let t=new a.Pq0,o=new a.Pq0(0,1,0),i=new a.PTz().setFromUnitVectors(e.up,o),p=i.clone().invert(),b=new a.Pq0,g=new a.PTz,w=2*Math.PI;return function(){let j=n.object.position;i.setFromUnitVectors(e.up,o),p.copy(i).invert(),t.copy(j).sub(n.target),t.applyQuaternion(i),u.setFromVector3(t),n.autoRotate&&s===l.NONE&&L(2*Math.PI/60/60*n.autoRotateSpeed),n.enableDamping?(u.theta+=f.theta*n.dampingFactor,u.phi+=f.phi*n.dampingFactor):(u.theta+=f.theta,u.phi+=f.phi);let E=n.minAzimuthAngle,x=n.maxAzimuthAngle;isFinite(E)&&isFinite(x)&&(E<-Math.PI?E+=w:E>Math.PI&&(E-=w),x<-Math.PI?x+=w:x>Math.PI&&(x-=w),E<=x?u.theta=Math.max(E,Math.min(x,u.theta)):u.theta=u.theta>(E+x)/2?Math.max(E,u.theta):Math.min(x,u.theta)),u.phi=Math.max(n.minPolarAngle,Math.min(n.maxPolarAngle,u.phi)),u.makeSafe(),!0===n.enableDamping?n.target.addScaledVector(y,n.dampingFactor):n.target.add(y),n.zoomToCursor&&A||n.object.isOrthographicCamera?u.radius=Y(u.radius):u.radius=Y(u.radius*d),t.setFromSpherical(u),t.applyQuaternion(p),j.copy(n.target).add(t),n.object.matrixAutoUpdate||n.object.updateMatrix(),n.object.lookAt(n.target),!0===n.enableDamping?(f.theta*=1-n.dampingFactor,f.phi*=1-n.dampingFactor,y.multiplyScalar(1-n.dampingFactor)):(f.set(0,0,0),y.set(0,0,0));let P=!1;if(n.zoomToCursor&&A){let r=null;if(n.object instanceof a.ubm&&n.object.isPerspectiveCamera){let e=t.length();r=Y(e*d);let o=e-r;n.object.position.addScaledVector(O,o),n.object.updateMatrixWorld()}else if(n.object.isOrthographicCamera){let e=new a.Pq0(T.x,T.y,0);e.unproject(n.object),n.object.zoom=Math.max(n.minZoom,Math.min(n.maxZoom,n.object.zoom/d)),n.object.updateProjectionMatrix(),P=!0;let o=new a.Pq0(T.x,T.y,0);o.unproject(n.object),n.object.position.sub(o).add(e),n.object.updateMatrixWorld(),r=t.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),n.zoomToCursor=!1;null!==r&&(n.screenSpacePanning?n.target.set(0,0,-1).transformDirection(n.object.matrix).multiplyScalar(r).add(n.object.position):(h.origin.copy(n.object.position),h.direction.set(0,0,-1).transformDirection(n.object.matrix),Math.abs(n.object.up.dot(h.direction))<v?e.lookAt(n.target):(m.setFromNormalAndCoplanarPoint(n.object.up,n.target),h.intersectPlane(m,n.target))))}else n.object instanceof a.qUd&&n.object.isOrthographicCamera&&(P=1!==d)&&(n.object.zoom=Math.max(n.minZoom,Math.min(n.maxZoom,n.object.zoom/d)),n.object.updateProjectionMatrix());return d=1,A=!1,!!(P||b.distanceToSquared(n.object.position)>c||8*(1-g.dot(n.object.quaternion))>c)&&(n.dispatchEvent(r),b.copy(n.object.position),g.copy(n.object.quaternion),P=!1,!0)}})(),this.connect=e=>{n.domElement=e,n.domElement.style.touchAction="none",n.domElement.addEventListener("contextmenu",et),n.domElement.addEventListener("pointerdown",K),n.domElement.addEventListener("pointercancel",Q),n.domElement.addEventListener("wheel",J)},this.dispose=()=>{var e,t,r,o,i,a;n.domElement&&(n.domElement.style.touchAction="auto"),null==(e=n.domElement)||e.removeEventListener("contextmenu",et),null==(t=n.domElement)||t.removeEventListener("pointerdown",K),null==(r=n.domElement)||r.removeEventListener("pointercancel",Q),null==(o=n.domElement)||o.removeEventListener("wheel",J),null==(i=n.domElement)||i.ownerDocument.removeEventListener("pointermove",G),null==(a=n.domElement)||a.ownerDocument.removeEventListener("pointerup",Q),null!==n._domElementKeyEvents&&n._domElementKeyEvents.removeEventListener("keydown",ee)};let n=this,r={type:"change"},o={type:"start"},i={type:"end"},l={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6},s=l.NONE,c=1e-6,u=new a.YHV,f=new a.YHV,d=1,y=new a.Pq0,g=new a.I9Y,w=new a.I9Y,j=new a.I9Y,E=new a.I9Y,x=new a.I9Y,P=new a.I9Y,M=new a.I9Y,k=new a.I9Y,C=new a.I9Y,O=new a.Pq0,T=new a.I9Y,A=!1,_=[],S={};function I(){return Math.pow(.95,n.zoomSpeed)}function L(e){n.reverseOrbit||n.reverseHorizontalOrbit?f.theta+=e:f.theta-=e}function R(e){n.reverseOrbit||n.reverseVerticalOrbit?f.phi+=e:f.phi-=e}let z=(()=>{let e=new a.Pq0;return function(t,n){e.setFromMatrixColumn(n,0),e.multiplyScalar(-t),y.add(e)}})(),N=(()=>{let e=new a.Pq0;return function(t,r){!0===n.screenSpacePanning?e.setFromMatrixColumn(r,1):(e.setFromMatrixColumn(r,0),e.crossVectors(n.object.up,e)),e.multiplyScalar(t),y.add(e)}})(),H=(()=>{let e=new a.Pq0;return function(t,r){let o=n.domElement;if(o&&n.object instanceof a.ubm&&n.object.isPerspectiveCamera){let i=n.object.position;e.copy(i).sub(n.target);let a=e.length();z(2*t*(a*=Math.tan(n.object.fov/2*Math.PI/180))/o.clientHeight,n.object.matrix),N(2*r*a/o.clientHeight,n.object.matrix)}else o&&n.object instanceof a.qUd&&n.object.isOrthographicCamera?(z(t*(n.object.right-n.object.left)/n.object.zoom/o.clientWidth,n.object.matrix),N(r*(n.object.top-n.object.bottom)/n.object.zoom/o.clientHeight,n.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),n.enablePan=!1)}})();function D(e){n.object instanceof a.ubm&&n.object.isPerspectiveCamera||n.object instanceof a.qUd&&n.object.isOrthographicCamera?d=e:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),n.enableZoom=!1)}function F(e){if(!n.zoomToCursor||!n.domElement)return;A=!0;let t=n.domElement.getBoundingClientRect(),r=e.clientX-t.left,o=e.clientY-t.top,i=t.width,a=t.height;T.x=r/i*2-1,T.y=-(o/a*2)+1,O.set(T.x,T.y,1).unproject(n.object).sub(n.object.position).normalize()}function Y(e){return Math.max(n.minDistance,Math.min(n.maxDistance,e))}function q(e){g.set(e.clientX,e.clientY)}function W(e){E.set(e.clientX,e.clientY)}function U(){if(1==_.length)g.set(_[0].pageX,_[0].pageY);else{let e=.5*(_[0].pageX+_[1].pageX),t=.5*(_[0].pageY+_[1].pageY);g.set(e,t)}}function $(){if(1==_.length)E.set(_[0].pageX,_[0].pageY);else{let e=.5*(_[0].pageX+_[1].pageX),t=.5*(_[0].pageY+_[1].pageY);E.set(e,t)}}function B(){let e=_[0].pageX-_[1].pageX,t=_[0].pageY-_[1].pageY,n=Math.sqrt(e*e+t*t);M.set(0,n)}function V(e){if(1==_.length)w.set(e.pageX,e.pageY);else{let t=er(e),n=.5*(e.pageX+t.x),r=.5*(e.pageY+t.y);w.set(n,r)}j.subVectors(w,g).multiplyScalar(n.rotateSpeed);let t=n.domElement;t&&(L(2*Math.PI*j.x/t.clientHeight),R(2*Math.PI*j.y/t.clientHeight)),g.copy(w)}function X(e){if(1==_.length)x.set(e.pageX,e.pageY);else{let t=er(e),n=.5*(e.pageX+t.x),r=.5*(e.pageY+t.y);x.set(n,r)}P.subVectors(x,E).multiplyScalar(n.panSpeed),H(P.x,P.y),E.copy(x)}function Z(e){var t;let r=er(e),o=e.pageX-r.x,i=e.pageY-r.y,a=Math.sqrt(o*o+i*i);k.set(0,a),C.set(0,Math.pow(k.y/M.y,n.zoomSpeed)),t=C.y,D(d/t),M.copy(k)}function K(e){var t,r,i;!1!==n.enabled&&(0===_.length&&(null==(t=n.domElement)||t.ownerDocument.addEventListener("pointermove",G),null==(r=n.domElement)||r.ownerDocument.addEventListener("pointerup",Q)),i=e,_.push(i),"touch"===e.pointerType?function(e){switch(en(e),_.length){case 1:switch(n.touches.ONE){case a.wtR.ROTATE:if(!1===n.enableRotate)return;U(),s=l.TOUCH_ROTATE;break;case a.wtR.PAN:if(!1===n.enablePan)return;$(),s=l.TOUCH_PAN;break;default:s=l.NONE}break;case 2:switch(n.touches.TWO){case a.wtR.DOLLY_PAN:if(!1===n.enableZoom&&!1===n.enablePan)return;n.enableZoom&&B(),n.enablePan&&$(),s=l.TOUCH_DOLLY_PAN;break;case a.wtR.DOLLY_ROTATE:if(!1===n.enableZoom&&!1===n.enableRotate)return;n.enableZoom&&B(),n.enableRotate&&U(),s=l.TOUCH_DOLLY_ROTATE;break;default:s=l.NONE}break;default:s=l.NONE}s!==l.NONE&&n.dispatchEvent(o)}(e):function(e){let t;switch(e.button){case 0:t=n.mouseButtons.LEFT;break;case 1:t=n.mouseButtons.MIDDLE;break;case 2:t=n.mouseButtons.RIGHT;break;default:t=-1}switch(t){case a.kBv.DOLLY:if(!1===n.enableZoom)return;F(e),M.set(e.clientX,e.clientY),s=l.DOLLY;break;case a.kBv.ROTATE:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===n.enablePan)return;W(e),s=l.PAN}else{if(!1===n.enableRotate)return;q(e),s=l.ROTATE}break;case a.kBv.PAN:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===n.enableRotate)return;q(e),s=l.ROTATE}else{if(!1===n.enablePan)return;W(e),s=l.PAN}break;default:s=l.NONE}s!==l.NONE&&n.dispatchEvent(o)}(e))}function G(e){!1!==n.enabled&&("touch"===e.pointerType?function(e){switch(en(e),s){case l.TOUCH_ROTATE:if(!1===n.enableRotate)return;V(e),n.update();break;case l.TOUCH_PAN:if(!1===n.enablePan)return;X(e),n.update();break;case l.TOUCH_DOLLY_PAN:if(!1===n.enableZoom&&!1===n.enablePan)return;n.enableZoom&&Z(e),n.enablePan&&X(e),n.update();break;case l.TOUCH_DOLLY_ROTATE:if(!1===n.enableZoom&&!1===n.enableRotate)return;n.enableZoom&&Z(e),n.enableRotate&&V(e),n.update();break;default:s=l.NONE}}(e):function(e){if(!1!==n.enabled)switch(s){case l.ROTATE:if(!1===n.enableRotate)return;w.set(e.clientX,e.clientY),j.subVectors(w,g).multiplyScalar(n.rotateSpeed);let t=n.domElement;t&&(L(2*Math.PI*j.x/t.clientHeight),R(2*Math.PI*j.y/t.clientHeight)),g.copy(w),n.update();break;case l.DOLLY:var r,o;if(!1===n.enableZoom)return;(k.set(e.clientX,e.clientY),C.subVectors(k,M),C.y>0)?(r=I(),D(d/r)):C.y<0&&(o=I(),D(d*o)),M.copy(k),n.update();break;case l.PAN:if(!1===n.enablePan)return;x.set(e.clientX,e.clientY),P.subVectors(x,E).multiplyScalar(n.panSpeed),H(P.x,P.y),E.copy(x),n.update()}}(e))}function Q(e){var t,r,o;(function(e){delete S[e.pointerId];for(let t=0;t<_.length;t++)if(_[t].pointerId==e.pointerId)return void _.splice(t,1)})(e),0===_.length&&(null==(t=n.domElement)||t.releasePointerCapture(e.pointerId),null==(r=n.domElement)||r.ownerDocument.removeEventListener("pointermove",G),null==(o=n.domElement)||o.ownerDocument.removeEventListener("pointerup",Q)),n.dispatchEvent(i),s=l.NONE}function J(e){if(!1!==n.enabled&&!1!==n.enableZoom&&(s===l.NONE||s===l.ROTATE)){var t,r;e.preventDefault(),n.dispatchEvent(o),(F(e),e.deltaY<0)?(t=I(),D(d*t)):e.deltaY>0&&(r=I(),D(d/r)),n.update(),n.dispatchEvent(i)}}function ee(e){if(!1!==n.enabled&&!1!==n.enablePan){let t=!1;switch(e.code){case n.keys.UP:H(0,n.keyPanSpeed),t=!0;break;case n.keys.BOTTOM:H(0,-n.keyPanSpeed),t=!0;break;case n.keys.LEFT:H(n.keyPanSpeed,0),t=!0;break;case n.keys.RIGHT:H(-n.keyPanSpeed,0),t=!0}t&&(e.preventDefault(),n.update())}}function et(e){!1!==n.enabled&&e.preventDefault()}function en(e){let t=S[e.pointerId];void 0===t&&(t=new a.I9Y,S[e.pointerId]=t),t.set(e.pageX,e.pageY)}function er(e){return S[(e.pointerId===_[0].pointerId?_[1]:_[0]).pointerId]}this.dollyIn=(e=I())=>{D(d*e),n.update()},this.dollyOut=(e=I())=>{D(d/e),n.update()},this.getScale=()=>d,this.setScale=e=>{D(e),n.update()},this.getZoomScale=()=>I(),void 0!==t&&this.connect(t),this.update()}}let g=i.forwardRef(({makeDefault:e,camera:t,regress:n,domElement:a,enableDamping:l=!0,keyEvents:s=!1,onChange:c,onStart:u,onEnd:f,...d},p)=>{let h=(0,o.A)(e=>e.invalidate),m=(0,o.A)(e=>e.camera),v=(0,o.A)(e=>e.gl),b=(0,o.A)(e=>e.events),g=(0,o.A)(e=>e.setEvents),w=(0,o.A)(e=>e.set),j=(0,o.A)(e=>e.get),E=(0,o.A)(e=>e.performance),x=t||m,P=a||b.connected||v.domElement,M=i.useMemo(()=>new y(x),[x]);return(0,o.C)(()=>{M.enabled&&M.update()},-1),i.useEffect(()=>(s&&M.connect(!0===s?P:s),M.connect(P),()=>void M.dispose()),[s,P,n,M,h]),i.useEffect(()=>{let e=e=>{h(),n&&E.regress(),c&&c(e)},t=e=>{u&&u(e)},r=e=>{f&&f(e)};return M.addEventListener("change",e),M.addEventListener("start",t),M.addEventListener("end",r),()=>{M.removeEventListener("start",t),M.removeEventListener("end",r),M.removeEventListener("change",e)}},[c,u,f,M,h,g]),i.useEffect(()=>{if(e){let e=j().controls;return w({controls:M}),()=>w({controls:e})}},[e,M]),i.createElement("primitive",(0,r.A)({ref:p,object:M,enableDamping:l},d))})},5220:(e,t,n)=>{e.exports=n(1724)},5273:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},5643:(e,t,n)=>{e.exports=n(6115)},5690:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},6115:(e,t,n)=>{var r=n(2115),o=n(1414),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=a(e,(f=c(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&d.hasValue){var t=d.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,l=n)}var a,l,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,n,r,o]))[0],f[1]);return s(function(){d.hasValue=!0,d.value=p},[p]),u(p),p}},6354:(e,t,n)=>{n.d(t,{Af:()=>l,Nz:()=>o,u5:()=>s,y3:()=>f});var r=n(2115);function o(e,t,n){if(!e)return;if(!0===n(e))return e;let r=t?e.return:e.child;for(;r;){let e=o(r,t,n);if(e)return e;r=t?null:r.sibling}}function i(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}(()=>{var e,t;return"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative")})()?r.useLayoutEffect:r.useEffect;let a=i(r.createContext(null));class l extends r.Component{render(){return r.createElement(a.Provider,{value:this._reactInternals},this.props.children)}}function s(){let e=r.useContext(a);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=r.useId();return r.useMemo(()=>{for(let n of[e,null==e?void 0:e.alternate]){if(!n)continue;let e=o(n,!1,e=>{let n=e.memoizedState;for(;n;){if(n.memoizedState===t)return!0;n=n.next}});if(e)return e}},[e,t])}let c=Symbol.for("react.context"),u=e=>null!==e&&"object"==typeof e&&"$$typeof"in e&&e.$$typeof===c;function f(){let e=function(){let e=s(),[t]=r.useState(()=>new Map);t.clear();let n=e;for(;n;){let e=n.type;u(e)&&e!==a&&!t.has(e)&&t.set(e,r.use(i(e))),n=n.return}return t}();return r.useMemo(()=>Array.from(e.keys()).reduce((t,n)=>o=>r.createElement(t,null,r.createElement(n.Provider,{...o,value:e.get(n)})),e=>r.createElement(l,{...e})),[e])}},6500:(e,t)=>{t.ConcurrentRoot=1,t.ContinuousEventPriority=8,t.DefaultEventPriority=32,t.DiscreteEventPriority=2},7319:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],f=[],d=1,p=null,h=3,m=!1,v=!1,b=!1,y="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(f);null!==t;){if(null===t.callback)o(f);else if(t.startTime<=e)o(f),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(f)}}function E(e){if(b=!1,j(e),!v)if(null!==r(u))v=!0,_();else{var t=r(f);null!==t&&S(E,t.startTime-e)}}var x=!1,P=-1,M=5,k=-1;function C(){return!(t.unstable_now()-k<M)}function O(){if(x){var e=t.unstable_now();k=e;var n=!0;try{e:{v=!1,b&&(b=!1,g(P),P=-1),m=!0;var i=h;try{t:{for(j(e),p=r(u);null!==p&&!(p.expirationTime>e&&C());){var l=p.callback;if("function"==typeof l){p.callback=null,h=p.priorityLevel;var s=l(p.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){p.callback=s,j(e),n=!0;break t}p===r(u)&&o(u),j(e)}else o(u);p=r(u)}if(null!==p)n=!0;else{var c=r(f);null!==c&&S(E,c.startTime-e),n=!1}}break e}finally{p=null,h=i,m=!1}}}finally{n?a():x=!1}}}if("function"==typeof w)a=function(){w(O)};else if("undefined"!=typeof MessageChannel){var T=new MessageChannel,A=T.port2;T.port1.onmessage=O,a=function(){A.postMessage(null)}}else a=function(){y(O,0)};function _(){x||(x=!0,a())}function S(e,n){P=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){v||m||(v=!0,_())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:d++,callback:o,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>a?(e.sortIndex=i,n(f,e),null===r(u)&&e===r(f)&&(b?(g(P),P=-1):b=!0,S(E,i-a))):(e.sortIndex=l,n(u,e),v||m||(v=!0,_())),e},t.unstable_shouldYield=C,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},7558:(e,t,n)=>{n.d(t,{Hl:()=>d});var r=n(3816),o=n(2115),i=n(7431);function a(e,t){let n;return(...r)=>{window.clearTimeout(n),n=window.setTimeout(()=>e(...r),t)}}let l=["x","y","top","bottom","left","right","width","height"],s=(e,t)=>l.every(n=>e[n]===t[n]);var c=n(6354),u=n(5155);function f({ref:e,children:t,fallback:n,resize:l,style:c,gl:f,events:d=r.f,eventSource:p,eventPrefix:h,shadows:m,linear:v,flat:b,legacy:y,orthographic:g,frameloop:w,dpr:j,performance:E,raycaster:x,camera:P,scene:M,onPointerMissed:k,onCreated:C,...O}){o.useMemo(()=>(0,r.e)(i),[]);let T=(0,r.u)(),[A,_]=function({debounce:e,scroll:t,polyfill:n,offsetSize:r}={debounce:0,scroll:!1,offsetSize:!1}){var i,l,c;let u=n||("undefined"==typeof window?class{}:window.ResizeObserver);if(!u)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[f,d]=(0,o.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),p=(0,o.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:f,orientationHandler:null}),h=e?"number"==typeof e?e:e.scroll:null,m=e?"number"==typeof e?e:e.resize:null,v=(0,o.useRef)(!1);(0,o.useEffect)(()=>(v.current=!0,()=>void(v.current=!1)));let[b,y,g]=(0,o.useMemo)(()=>{let e=()=>{if(!p.current.element)return;let{left:e,top:t,width:n,height:o,bottom:i,right:a,x:l,y:c}=p.current.element.getBoundingClientRect(),u={left:e,top:t,width:n,height:o,bottom:i,right:a,x:l,y:c};p.current.element instanceof HTMLElement&&r&&(u.height=p.current.element.offsetHeight,u.width=p.current.element.offsetWidth),Object.freeze(u),v.current&&!s(p.current.lastBounds,u)&&d(p.current.lastBounds=u)};return[e,m?a(e,m):e,h?a(e,h):e]},[d,r,h,m]);function w(){p.current.scrollContainers&&(p.current.scrollContainers.forEach(e=>e.removeEventListener("scroll",g,!0)),p.current.scrollContainers=null),p.current.resizeObserver&&(p.current.resizeObserver.disconnect(),p.current.resizeObserver=null),p.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",p.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",p.current.orientationHandler))}function j(){p.current.element&&(p.current.resizeObserver=new u(g),p.current.resizeObserver.observe(p.current.element),t&&p.current.scrollContainers&&p.current.scrollContainers.forEach(e=>e.addEventListener("scroll",g,{capture:!0,passive:!0})),p.current.orientationHandler=()=>{g()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",p.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",p.current.orientationHandler))}return i=g,l=!!t,(0,o.useEffect)(()=>{if(l)return window.addEventListener("scroll",i,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",i,!0)},[i,l]),c=y,(0,o.useEffect)(()=>(window.addEventListener("resize",c),()=>void window.removeEventListener("resize",c)),[c]),(0,o.useEffect)(()=>{w(),j()},[t,g,y]),(0,o.useEffect)(()=>w,[]),[e=>{e&&e!==p.current.element&&(w(),p.current.element=e,p.current.scrollContainers=function e(t){let n=[];if(!t||t===document.body)return n;let{overflow:r,overflowX:o,overflowY:i}=window.getComputedStyle(t);return[r,o,i].some(e=>"auto"===e||"scroll"===e)&&n.push(t),[...n,...e(t.parentElement)]}(e),j())},f,b]}({scroll:!0,debounce:{scroll:50,resize:0},...l}),S=o.useRef(null),I=o.useRef(null);o.useImperativeHandle(e,()=>S.current);let L=(0,r.a)(k),[R,z]=o.useState(!1),[N,H]=o.useState(!1);if(R)throw R;if(N)throw N;let D=o.useRef(null);(0,r.b)(()=>{let e=S.current;_.width>0&&_.height>0&&e&&(D.current||(D.current=(0,r.c)(e)),async function(){await D.current.configure({gl:f,scene:M,events:d,shadows:m,linear:v,flat:b,legacy:y,orthographic:g,frameloop:w,dpr:j,performance:E,raycaster:x,camera:P,size:_,onPointerMissed:(...e)=>null==L.current?void 0:L.current(...e),onCreated:e=>{null==e.events.connect||e.events.connect(p?(0,r.i)(p)?p.current:p:I.current),h&&e.setEvents({compute:(e,t)=>{let n=e[h+"X"],r=e[h+"Y"];t.pointer.set(n/t.size.width*2-1,-(2*(r/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),null==C||C(e)}}),D.current.render((0,u.jsx)(T,{children:(0,u.jsx)(r.E,{set:H,children:(0,u.jsx)(o.Suspense,{fallback:(0,u.jsx)(r.B,{set:z}),children:null!=t?t:null})})}))}())}),o.useEffect(()=>{let e=S.current;if(e)return()=>(0,r.d)(e)},[]);let F=p?"none":"auto";return(0,u.jsx)("div",{ref:I,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:F,...c},...O,children:(0,u.jsx)("div",{ref:A,style:{width:"100%",height:"100%"},children:(0,u.jsx)("canvas",{ref:S,style:{display:"block"},children:n})})})}function d(e){return(0,u.jsx)(c.Af,{children:(0,u.jsx)(f,{...e})})}n(1933),n(5220),n(4342)},7918:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]])},8247:(e,t,n)=>{e.exports=n(620)},9630:(e,t,n)=>{n.d(t,{A:()=>r});function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}},9957:(e,t,n)=>{let r,o;n.d(t,{E:()=>g});var i=n(9630),a=n(2115),l=n(2669),s=n(3264),c=n(3816);let u=new s.Pq0,f=new s.Pq0,d=new s.Pq0,p=new s.I9Y;function h(e,t,n){let r=u.setFromMatrixPosition(e.matrixWorld);r.project(t);let o=n.width/2,i=n.height/2;return[r.x*o+o,-(r.y*i)+i]}let m=e=>1e-10>Math.abs(e)?0:e;function v(e,t,n=""){let r="matrix3d(";for(let n=0;16!==n;n++)r+=m(t[n]*e.elements[n])+(15!==n?",":")");return n+r}let b=(r=[1,-1,1,1,1,-1,1,1,1,-1,1,1,1,-1,1,1],e=>v(e,r)),y=(o=e=>[1/e,1/e,1/e,1,-1/e,-1/e,-1/e,-1,1/e,1/e,1/e,1,1,1,1,1],(e,t)=>v(e,o(t),"translate(-50%,-50%)")),g=a.forwardRef(({children:e,eps:t=.001,style:n,className:r,prepend:o,center:v,fullscreen:g,portal:w,distanceFactor:j,sprite:E=!1,transform:x=!1,occlude:P,onOcclude:M,castShadow:k,receiveShadow:C,material:O,geometry:T,zIndexRange:A=[0x1000037,0],calculatePosition:_=h,as:S="div",wrapperClass:I,pointerEvents:L="auto",...R},z)=>{let{gl:N,camera:H,scene:D,size:F,raycaster:Y,events:q,viewport:W}=(0,c.A)(),[U]=a.useState(()=>document.createElement(S)),$=a.useRef(null),B=a.useRef(null),V=a.useRef(0),X=a.useRef([0,0]),Z=a.useRef(null),K=a.useRef(null),G=(null==w?void 0:w.current)||q.connected||N.domElement.parentNode,Q=a.useRef(null),J=a.useRef(!1),ee=a.useMemo(()=>P&&"blending"!==P||Array.isArray(P)&&P.length&&function(e){return e&&"object"==typeof e&&"current"in e}(P[0]),[P]);a.useLayoutEffect(()=>{let e=N.domElement;P&&"blending"===P?(e.style.zIndex=`${Math.floor(A[0]/2)}`,e.style.position="absolute",e.style.pointerEvents="none"):(e.style.zIndex=null,e.style.position=null,e.style.pointerEvents=null)},[P]),a.useLayoutEffect(()=>{if(B.current){let e=$.current=l.createRoot(U);if(D.updateMatrixWorld(),x)U.style.cssText="position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;";else{let e=_(B.current,H,F);U.style.cssText=`position:absolute;top:0;left:0;transform:translate3d(${e[0]}px,${e[1]}px,0);transform-origin:0 0;`}return G&&(o?G.prepend(U):G.appendChild(U)),()=>{G&&G.removeChild(U),e.unmount()}}},[G,x]),a.useLayoutEffect(()=>{I&&(U.className=I)},[I]);let et=a.useMemo(()=>x?{position:"absolute",top:0,left:0,width:F.width,height:F.height,transformStyle:"preserve-3d",pointerEvents:"none"}:{position:"absolute",transform:v?"translate3d(-50%,-50%,0)":"none",...g&&{top:-F.height/2,left:-F.width/2,width:F.width,height:F.height},...n},[n,v,g,F,x]),en=a.useMemo(()=>({position:"absolute",pointerEvents:L}),[L]);a.useLayoutEffect(()=>{var t,o;J.current=!1,x?null==(t=$.current)||t.render(a.createElement("div",{ref:Z,style:et},a.createElement("div",{ref:K,style:en},a.createElement("div",{ref:z,className:r,style:n,children:e})))):null==(o=$.current)||o.render(a.createElement("div",{ref:z,style:et,className:r,children:e}))});let er=a.useRef(!0);(0,c.C)(e=>{if(B.current){H.updateMatrixWorld(),B.current.updateWorldMatrix(!0,!1);let e=x?X.current:_(B.current,H,F);if(x||Math.abs(V.current-H.zoom)>t||Math.abs(X.current[0]-e[0])>t||Math.abs(X.current[1]-e[1])>t){let t=function(e,t){let n=u.setFromMatrixPosition(e.matrixWorld),r=f.setFromMatrixPosition(t.matrixWorld),o=n.sub(r),i=t.getWorldDirection(d);return o.angleTo(i)>Math.PI/2}(B.current,H),n=!1;ee&&(Array.isArray(P)?n=P.map(e=>e.current):"blending"!==P&&(n=[D]));let r=er.current;n?er.current=function(e,t,n,r){let o=u.setFromMatrixPosition(e.matrixWorld),i=o.clone();i.project(t),p.set(i.x,i.y),n.setFromCamera(p,t);let a=n.intersectObjects(r,!0);if(a.length){let e=a[0].distance;return o.distanceTo(n.ray.origin)<e}return!0}(B.current,H,Y,n)&&!t:er.current=!t,r!==er.current&&(M?M(!er.current):U.style.display=er.current?"block":"none");let o=Math.floor(A[0]/2),i=P?ee?[A[0],o]:[o-1,0]:A;if(U.style.zIndex=`${function(e,t,n){if(t instanceof s.ubm||t instanceof s.qUd){let r=u.setFromMatrixPosition(e.matrixWorld),o=f.setFromMatrixPosition(t.matrixWorld),i=r.distanceTo(o),a=(n[1]-n[0])/(t.far-t.near),l=n[1]-a*t.far;return Math.round(a*i+l)}}(B.current,H,i)}`,x){let[e,t]=[F.width/2,F.height/2],n=H.projectionMatrix.elements[5]*t,{isOrthographicCamera:r,top:o,left:i,bottom:a,right:l}=H,s=b(H.matrixWorldInverse),c=r?`scale(${n})translate(${m(-(l+i)/2)}px,${m((o+a)/2)}px)`:`translateZ(${n}px)`,u=B.current.matrixWorld;E&&((u=H.matrixWorldInverse.clone().transpose().copyPosition(u).scale(B.current.scale)).elements[3]=u.elements[7]=u.elements[11]=0,u.elements[15]=1),U.style.width=F.width+"px",U.style.height=F.height+"px",U.style.perspective=r?"":`${n}px`,Z.current&&K.current&&(Z.current.style.transform=`${c}${s}translate(${e}px,${t}px)`,K.current.style.transform=y(u,1/((j||10)/400)))}else{let t=void 0===j?1:function(e,t){if(t instanceof s.qUd)return t.zoom;if(!(t instanceof s.ubm))return 1;{let n=u.setFromMatrixPosition(e.matrixWorld),r=f.setFromMatrixPosition(t.matrixWorld);return 1/(2*Math.tan(t.fov*Math.PI/180/2)*n.distanceTo(r))}}(B.current,H)*j;U.style.transform=`translate3d(${e[0]}px,${e[1]}px,0) scale(${t})`}X.current=e,V.current=H.zoom}}if(!ee&&Q.current&&!J.current)if(x){if(Z.current){let e=Z.current.children[0];if(null!=e&&e.clientWidth&&null!=e&&e.clientHeight){let{isOrthographicCamera:t}=H;if(t||T)R.scale&&(Array.isArray(R.scale)?R.scale instanceof s.Pq0?Q.current.scale.copy(R.scale.clone().divideScalar(1)):Q.current.scale.set(1/R.scale[0],1/R.scale[1],1/R.scale[2]):Q.current.scale.setScalar(1/R.scale));else{let t=(j||10)/400,n=e.clientWidth*t,r=e.clientHeight*t;Q.current.scale.set(n,r,1)}J.current=!0}}}else{let t=U.children[0];if(null!=t&&t.clientWidth&&null!=t&&t.clientHeight){let e=1/W.factor,n=t.clientWidth*e,r=t.clientHeight*e;Q.current.scale.set(n,r,1),J.current=!0}Q.current.lookAt(e.camera.position)}});let eo=a.useMemo(()=>({vertexShader:x?void 0:`
          /*
            This shader is from the THREE's SpriteMaterial.
            We need to turn the backing plane into a Sprite
            (make it always face the camera) if "transfrom"
            is false.
          */
          #include <common>

          void main() {
            vec2 center = vec2(0., 1.);
            float rotation = 0.0;

            // This is somewhat arbitrary, but it seems to work well
            // Need to figure out how to derive this dynamically if it even matters
            float size = 0.03;

            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );
            vec2 scale;
            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );
            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );

            bool isPerspective = isPerspectiveMatrix( projectionMatrix );
            if ( isPerspective ) scale *= - mvPosition.z;

            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;
            vec2 rotatedPosition;
            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;
            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;
            mvPosition.xy += rotatedPosition;

            gl_Position = projectionMatrix * mvPosition;
          }
      `,fragmentShader:`
        void main() {
          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);
        }
      `}),[x]);return a.createElement("group",(0,i.A)({},R,{ref:B}),P&&!ee&&a.createElement("mesh",{castShadow:k,receiveShadow:C,ref:Q},T||a.createElement("planeGeometry",null),O||a.createElement("shaderMaterial",{side:s.$EB,vertexShader:eo.vertexShader,fragmentShader:eo.fragmentShader})))})}}]);