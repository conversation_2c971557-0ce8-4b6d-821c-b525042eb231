{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Eye } from 'lucide-react';\n\nconst footerLinks = {\n  product: [\n    { href: '/tours', label: 'Tours' },\n    { href: '/pricing', label: 'Pricing' },\n    { href: '/features', label: 'Features' },\n    { href: '/integrations', label: 'Integrations' },\n  ],\n  company: [\n    { href: '/about', label: 'About' },\n    { href: '/contact', label: 'Contact' },\n    { href: '/careers', label: 'Careers' },\n    { href: '/blog', label: 'Blog' },\n  ],\n  resources: [\n    { href: '/help', label: 'Help Center' },\n    { href: '/docs', label: 'Documentation' },\n    { href: '/api', label: 'API Reference' },\n    { href: '/status', label: 'Status' },\n  ],\n  legal: [\n    { href: '/privacy', label: 'Privacy Policy' },\n    { href: '/terms', label: 'Terms of Service' },\n    { href: '/cookies', label: 'Cookie Policy' },\n    { href: '/gdpr', label: 'GDPR' },\n  ],\n};\n\nexport function Footer() {\n  return (\n    <footer className=\"border-t bg-background\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-5 gap-8\">\n          {/* Brand */}\n          <div className=\"md:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <Eye className=\"h-6 w-6 text-primary\" />\n              <span className=\"text-lg font-bold\">VirtualRealTour</span>\n            </Link>\n            <p className=\"text-sm text-muted-foreground mb-4\">\n              Premium 360° virtual tour platform designed for the Nigerian market.\n            </p>\n            <p className=\"text-xs text-muted-foreground\">\n              Made with ❤️ in Nigeria\n            </p>\n          </div>\n\n          {/* Product */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Resources */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Resources</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-sm text-muted-foreground\">\n            &copy; 2024 VirtualRealTour. All rights reserved.\n          </p>\n          <div className=\"flex items-center space-x-4 mt-4 md:mt-0\">\n            <Link\n              href=\"/privacy\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Privacy\n            </Link>\n            <Link\n              href=\"/terms\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Terms\n            </Link>\n            <Link\n              href=\"/cookies\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Cookies\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAiB,OAAO;QAAe;KAChD;IACD,SAAS;QACP;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAS,OAAO;QAAO;KAChC;IACD,WAAW;QACT;YAAE,MAAM;YAAS,OAAO;QAAc;QACtC;YAAE,MAAM;YAAS,OAAO;QAAgB;QACxC;YAAE,MAAM;YAAQ,OAAO;QAAgB;QACvC;YAAE,MAAM;YAAW,OAAO;QAAS;KACpC;IACD,OAAO;QACL;YAAE,MAAM;YAAY,OAAO;QAAiB;QAC5C;YAAE,MAAM;YAAU,OAAO;QAAmB;QAC5C;YAAE,MAAM;YAAY,OAAO;QAAgB;QAC3C;YAAE,MAAM;YAAS,OAAO;QAAO;KAChC;AACH;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAM/C,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAa1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/public-layout.tsx"], "sourcesContent": ["import { Header } from './header';\nimport { Footer } from './footer';\n\ninterface PublicLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function PublicLayout({ children }: PublicLayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header variant=\"public\" />\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;gBAAC,SAAQ;;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/app/page.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Eye, Globe, Smartphone, Zap } from \"lucide-react\";\nimport { PublicLayout } from \"@/components/layout/public-layout\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <PublicLayout>\n\n      {/* Hero Section */}\n      <section className=\"flex items-center justify-center py-20 px-4\">\n        <div className=\"container mx-auto text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold tracking-tight mb-6\">\n            Create Stunning{\" \"}\n            <span className=\"text-primary\">360° Virtual Tours</span>{\" \"}\n            for Nigeria\n          </h1>\n          <p className=\"text-xl text-muted-foreground mb-8 max-w-3xl mx-auto\">\n            Transform your real estate, education, hospitality, and retail spaces into immersive\n            virtual experiences. Built specifically for the Nigerian market with local payment\n            integration and WhatsApp connectivity.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button size=\"lg\" asChild>\n              <Link href=\"/auth/signup\">Start Creating Tours</Link>\n            </Button>\n            <Button size=\"lg\" variant=\"outline\" asChild>\n              <Link href=\"/tours\">Explore Tours</Link>\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-muted/50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Why Choose VirtualRealTour?\n            </h2>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              Premium features designed for the Nigerian market with enterprise-grade\n              performance and luxury brand-inspired design.\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            <Card>\n              <CardHeader>\n                <Eye className=\"h-12 w-12 text-primary mb-4\" />\n                <CardTitle>360° Immersive Views</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <CardDescription>\n                  Create stunning 360° virtual tours with high-quality image and video support.\n                </CardDescription>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <Smartphone className=\"h-12 w-12 text-primary mb-4\" />\n                <CardTitle>WhatsApp Integration</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <CardDescription>\n                  Connect directly with prospects through integrated WhatsApp Business API.\n                </CardDescription>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <Globe className=\"h-12 w-12 text-primary mb-4\" />\n                <CardTitle>Nigerian Payments</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <CardDescription>\n                  Accept payments through Paystack, Flutterwave, and international options.\n                </CardDescription>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <Zap className=\"h-12 w-12 text-primary mb-4\" />\n                <CardTitle>Lightning Fast</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <CardDescription>\n                  Optimized for fast loading with progressive enhancement and CDN delivery.\n                </CardDescription>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </section>\n    </PublicLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,gJAAA,CAAA,eAAY;;0BAGX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAqD;gCACjD;8CAChB,8OAAC;oCAAK,WAAU;8CAAe;;;;;;gCAA0B;gCAAI;;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAAuD;;;;;;sCAKpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAe;;;;;;;;;;;8CAE5B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,OAAO;8CACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAMjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjC", "debugId": null}}]}