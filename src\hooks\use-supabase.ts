import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { supabase, createClient } from '@/lib/supabase/client';
import { clientOperations } from '@/lib/supabase/operations';
import type { Database } from '@/types/supabase';

type User = Database['public']['Tables']['users']['Row'];
type Tour = Database['public']['Tables']['tours']['Row'];

// Authentication hook
export function useAuth() {
  const [user, setUser] = useState<any>(null);
  const [profile, setProfile] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const client = createClient();
      const { data: { session } } = await client.auth.getSession();
      setUser(session?.user ?? null);

      if (session?.user) {
        try {
          const userProfile = await clientOperations.users.getProfile();
          setProfile(userProfile);
        } catch (error) {
          console.error('Error fetching user profile:', error);
        }
      }

      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const client = createClient();
    const { data: { subscription } } = client.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);

        if (session?.user) {
          try {
            const userProfile = await clientOperations.users.getProfile();
            setProfile(userProfile);
          } catch (error) {
            console.error('Error fetching user profile:', error);
            setProfile(null);
          }
        } else {
          setProfile(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signOut = useCallback(async () => {
    const client = createClient();
    await client.auth.signOut();
    router.push('/');
  }, [router]);

  const updateProfile = useCallback(async (updates: Partial<User>) => {
    if (!user) throw new Error('Not authenticated');

    const updatedProfile = await clientOperations.users.updateProfile(updates);
    setProfile(updatedProfile);
    return updatedProfile;
  }, [user]);

  return {
    user,
    profile,
    loading,
    signOut,
    updateProfile,
    isAuthenticated: !!user,
  };
}

// Tours hook
export function useTours(options: {
  userId?: string;
  status?: string;
  featured?: boolean;
} = {}) {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTours = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      let data: Tour[];

      if (options.userId) {
        data = await clientOperations.tours.getUserTours(options.userId);
      } else {
        data = await clientOperations.tours.getPublished();
      }

      setTours(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tours');
    } finally {
      setLoading(false);
    }
  }, [options.userId, options.status, options.featured]);

  useEffect(() => {
    fetchTours();
  }, [fetchTours]);

  const createTour = useCallback(async (tourData: Partial<Tour>) => {
    try {
      const newTour = await clientOperations.tours.create(tourData);
      setTours(prev => [newTour, ...prev]);
      return newTour;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to create tour');
    }
  }, []);

  const updateTour = useCallback(async (id: string, updates: Partial<Tour>) => {
    try {
      const updatedTour = await clientOperations.tours.update(id, updates);
      setTours(prev => prev.map(tour => tour.id === id ? updatedTour : tour));
      return updatedTour;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to update tour');
    }
  }, []);

  const deleteTour = useCallback(async (id: string) => {
    try {
      await clientOperations.tours.delete(id);
      setTours(prev => prev.filter(tour => tour.id !== id));
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to delete tour');
    }
  }, []);

  return {
    tours,
    loading,
    error,
    refetch: fetchTours,
    createTour,
    updateTour,
    deleteTour,
  };
}

// Single tour hook
export function useTour(slug: string) {
  const [tour, setTour] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTour = async () => {
      try {
        setLoading(true);
        setError(null);

        const data = await clientOperations.tours.getBySlug(slug);
        setTour(data);

        // Track view
        await clientOperations.analytics.trackEvent(data.id, 'view');
        await clientOperations.tours.incrementViews(data.id);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch tour');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchTour();
    }
  }, [slug]);

  return { tour, loading, error };
}

// File upload hook
export function useFileUpload() {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const uploadFile = useCallback(async (
    file: File,
    options: {
      tourId?: string;
      bucket?: string;
      onProgress?: (progress: number) => void;
    } = {}
  ) => {
    try {
      setUploading(true);
      setProgress(0);

      const formData = new FormData();
      formData.append('file', file);
      if (options.tourId) formData.append('tourId', options.tourId);
      if (options.bucket) formData.append('bucket', options.bucket);

      const xhr = new XMLHttpRequest();

      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progressPercent = Math.round((event.loaded / event.total) * 100);
            setProgress(progressPercent);
            options.onProgress?.(progressPercent);
          }
        });

        xhr.addEventListener('load', () => {
          try {
            const response = JSON.parse(xhr.responseText);
            if (xhr.status >= 200 && xhr.status < 300) {
              resolve(response.data);
            } else {
              reject(new Error(response.error?.message || 'Upload failed'));
            }
          } catch (err) {
            reject(new Error('Invalid response format'));
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Network error'));
        });

        xhr.open('POST', '/api/upload');
        xhr.send(formData);
      });
    } catch (error) {
      throw error;
    } finally {
      setUploading(false);
      setProgress(0);
    }
  }, []);

  return {
    uploadFile,
    uploading,
    progress,
  };
}

// Real-time subscription hook
export function useRealtimeSubscription<T>(
  table: string,
  filter?: string,
  initialData: T[] = []
) {
  const [data, setData] = useState<T[]>(initialData);

  useEffect(() => {
    const channel = supabase
      .channel(`${table}_changes`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table,
          filter,
        },
        (payload) => {
          const { eventType, new: newRecord, old: oldRecord } = payload;

          setData(prev => {
            switch (eventType) {
              case 'INSERT':
                return [newRecord as T, ...prev];
              case 'UPDATE':
                return prev.map(item =>
                  (item as any).id === newRecord.id ? newRecord as T : item
                );
              case 'DELETE':
                return prev.filter(item => (item as any).id !== oldRecord.id);
              default:
                return prev;
            }
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [table, filter]);

  return data;
}

// Analytics hook
export function useAnalytics(tourId: string) {
  const [analytics, setAnalytics] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const data = await clientOperations.analytics.getTourStats(tourId);
        setAnalytics(data);
      } catch (error) {
        console.error('Error fetching analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    if (tourId) {
      fetchAnalytics();
    }
  }, [tourId]);

  const trackEvent = useCallback(async (eventType: string, eventData?: any) => {
    await clientOperations.analytics.trackEvent(tourId, eventType, eventData);
  }, [tourId]);

  return {
    analytics,
    loading,
    trackEvent,
  };
}
