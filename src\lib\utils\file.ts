/**
 * Convert file to base64 string
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = error => reject(error);
  });
}

/**
 * Convert file to array buffer
 */
export function fileToArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = () => {
      if (reader.result instanceof ArrayBuffer) {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to array buffer'));
      }
    };
    reader.onerror = error => reject(error);
  });
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
}

/**
 * Get file name without extension
 */
export function getFileNameWithoutExtension(filename: string): string {
  return filename.replace(/\.[^/.]+$/, '');
}

/**
 * Generate unique filename with timestamp
 */
export function generateUniqueFilename(originalFilename: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = getFileExtension(originalFilename);
  const nameWithoutExt = getFileNameWithoutExtension(originalFilename);
  
  return `${nameWithoutExt}_${timestamp}_${randomString}.${extension}`;
}

/**
 * Compress image file
 */
export function compressImage(
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        blob => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Create thumbnail from image file
 */
export function createThumbnail(
  file: File,
  size: number = 200
): Promise<File> {
  return compressImage(file, size, size, 0.7);
}

/**
 * Get image dimensions
 */
export function getImageDimensions(file: File): Promise<{
  width: number;
  height: number;
}> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Get video duration and dimensions
 */
export function getVideoMetadata(file: File): Promise<{
  duration: number;
  width: number;
  height: number;
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    
    video.onloadedmetadata = () => {
      resolve({
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
      });
    };
    
    video.onerror = () => reject(new Error('Failed to load video'));
    video.src = URL.createObjectURL(file);
  });
}

/**
 * Download file from URL
 */
export function downloadFile(url: string, filename: string): void {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Copy file to clipboard (for supported browsers)
 */
export async function copyFileToClipboard(file: File): Promise<void> {
  if (!navigator.clipboard || !navigator.clipboard.write) {
    throw new Error('Clipboard API not supported');
  }

  const clipboardItem = new ClipboardItem({
    [file.type]: file,
  });

  await navigator.clipboard.write([clipboardItem]);
}

/**
 * Validate file against multiple criteria
 */
export function validateFile(
  file: File,
  options: {
    maxSize?: number; // in MB
    allowedTypes?: string[];
    minWidth?: number;
    minHeight?: number;
    maxWidth?: number;
    maxHeight?: number;
  }
): Promise<{
  isValid: boolean;
  errors: string[];
}> {
  return new Promise(async (resolve) => {
    const errors: string[] = [];

    // Check file size
    if (options.maxSize) {
      const maxSizeInBytes = options.maxSize * 1024 * 1024;
      if (file.size > maxSizeInBytes) {
        errors.push(`File size must be less than ${options.maxSize}MB`);
      }
    }

    // Check file type
    if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
      errors.push(`File type ${file.type} is not allowed`);
    }

    // Check image dimensions if it's an image
    if (file.type.startsWith('image/')) {
      try {
        const dimensions = await getImageDimensions(file);
        
        if (options.minWidth && dimensions.width < options.minWidth) {
          errors.push(`Image width must be at least ${options.minWidth}px`);
        }
        
        if (options.minHeight && dimensions.height < options.minHeight) {
          errors.push(`Image height must be at least ${options.minHeight}px`);
        }
        
        if (options.maxWidth && dimensions.width > options.maxWidth) {
          errors.push(`Image width must be less than ${options.maxWidth}px`);
        }
        
        if (options.maxHeight && dimensions.height > options.maxHeight) {
          errors.push(`Image height must be less than ${options.maxHeight}px`);
        }
      } catch (error) {
        errors.push('Failed to read image dimensions');
      }
    }

    resolve({
      isValid: errors.length === 0,
      errors,
    });
  });
}
