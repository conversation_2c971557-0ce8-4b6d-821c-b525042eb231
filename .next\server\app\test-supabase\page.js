const CHUNK_PUBLIC_PATH = "server/app/test-supabase/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_0962b827._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/src_app_a4430781._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_4599dc2c._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__ce8d18af._.js");
runtime.loadChunk("server/chunks/ssr/_3a541864._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_b04db032._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@supabase_node-fetch_lib_index_a39ccda6.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_e90a23ad._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_tr46_1a859af0._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ws_34fa8cd4._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@supabase_auth-js_dist_module_f390e684._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_2ac74cf8._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__139d7e2a._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/test-supabase/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/test-supabase/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/test-supabase/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/test-supabase/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/test-supabase/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
