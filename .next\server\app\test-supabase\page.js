(()=>{var e={};e.id=192,e.ids=[192],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,s,r)=>{let{createProxy:t}=r(39844);e.exports=t("C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5531:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var t=r(65239),a=r(48088),n=r(88170),l=r.n(n),i=r(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(s,c);let d={children:["",{children:["test-supabase",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,12741)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\test-supabase\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\test-supabase\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-supabase/page",pathname:"/test-supabase",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12741:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(37413),a=r(61246),n=r(44999),l=r(78963),i=r(30084),c=r(97680),d=r(26373);let o=(0,d.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),x=(0,d.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),u=(0,d.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),m=(0,d.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var h=r(13627);async function p(){let e="disconnected",s="unauthenticated",r=0,d=null;try{let t=await (0,n.UL)(),l=(0,a.createServerClient)("https://maudhokdhyhspfpasnfm.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1hdWRob2tkaHloc3BmcGFzbmZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjgwMDQsImV4cCI6MjA2NDc0NDAwNH0.yaD0ot54tD5tj5Bz2SMZXgp6x6fzVXsAz2xjvB-bFFo",{cookies:{getAll:()=>t.getAll(),setAll(e){try{e.forEach(({name:e,value:s,options:r})=>t.set(e,s,r))}catch{}}}}),{data:i,error:c}=await l.from("users").select("count",{count:"exact",head:!0});if(c)throw Error(`Database error: ${c.message}`);e="connected",r=i?.length||0;let{data:{user:d},error:o}=await l.auth.getUser();o?console.warn("Auth check failed:",o.message):d&&(s="authenticated")}catch(e){d=e instanceof Error?e.message:"Unknown error",console.error("Supabase test failed:",e)}return(0,t.jsx)(c.s,{children:(0,t.jsx)("div",{className:"py-20",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 max-w-4xl",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Supabase Connection Test"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Testing the new Supabase SSR integration for VirtualRealTour platform"})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(o,{className:"h-5 w-5 mr-2"}),"Database Connection"]}),(0,t.jsx)(l.BT,{children:"Testing connection to Supabase database"})]}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsx)("div",{className:"flex items-center space-x-2 mb-4",children:"connected"===e?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(x,{className:"h-5 w-5 text-green-500"}),(0,t.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Connected"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u,{className:"h-5 w-5 text-red-500"}),(0,t.jsx)(i.E,{variant:"destructive",children:"Disconnected"})]})}),"connected"===e&&(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Status:"})," Successfully connected to database"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Users table:"})," Accessible"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"User count:"})," ",r]})]}),d&&(0,t.jsxs)("div",{className:"mt-4 p-3 bg-red-50 border border-red-200 rounded-md",children:[(0,t.jsx)("p",{className:"text-red-800 text-sm font-medium",children:"Error:"}),(0,t.jsx)("p",{className:"text-red-700 text-sm",children:d})]})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(m,{className:"h-5 w-5 mr-2"}),"Authentication"]}),(0,t.jsx)(l.BT,{children:"Testing Supabase Auth integration"})]}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsx)("div",{className:"flex items-center space-x-2 mb-4",children:"authenticated"===s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(x,{className:"h-5 w-5 text-green-500"}),(0,t.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Authenticated"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u,{className:"h-5 w-5 text-orange-500"}),(0,t.jsx)(i.E,{variant:"secondary",children:"Not Authenticated"})]})}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Status:"})," ","authenticated"===s?"User is signed in":"No user session found"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Auth system:"})," ","connected"===e?"Available":"Unavailable"]})]})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Environment"]}),(0,t.jsx)(l.BT,{children:"Environment configuration status"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Supabase URL:"}),(0,t.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Configured"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Anon Key:"}),(0,t.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Configured"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Service Role:"}),process.env.SUPABASE_SERVICE_ROLE_KEY?(0,t.jsx)(i.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Configured"}):(0,t.jsx)(i.E,{variant:"secondary",children:"Optional"})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"SSR Integration"}),(0,t.jsx)(l.BT,{children:"Server-Side Rendering with Supabase"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Server-side client working"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Cookie-based auth ready"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"Middleware integration active"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm",children:"API routes configured"})]})]})})]})]}),(0,t.jsxs)(l.Zp,{className:"mt-8",children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Next Steps"}),(0,t.jsx)(l.BT,{children:"Your Supabase SSR integration is ready for development"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"✅ Completed Setup:"}),(0,t.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1 ml-4",children:[(0,t.jsx)("li",{children:"• Supabase SSR client configuration"}),(0,t.jsx)("li",{children:"• Environment variables configured"}),(0,t.jsx)("li",{children:"• Server and client components ready"}),(0,t.jsx)("li",{children:"• Middleware authentication flow"}),(0,t.jsx)("li",{children:"• API routes with SSR support"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"\uD83D\uDE80 Ready for Development:"}),(0,t.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1 ml-4",children:[(0,t.jsx)("li",{children:"• Database schema implementation"}),(0,t.jsx)("li",{children:"• User authentication flows"}),(0,t.jsx)("li",{children:"• Tour management features"}),(0,t.jsx)("li",{children:"• File upload and storage"}),(0,t.jsx)("li",{children:"• Real-time subscriptions"})]})]})]})})]})]})})})}},13627:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30084:(e,s,r)=>{"use strict";r.d(s,{E:()=>i});var t=r(37413);r(61120);var a=r(50662),n=r(10974);let l=(0,a.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:s,...r}){return(0,t.jsx)("div",{className:(0,n.cn)(l({variant:s}),e),...r})}},33873:e=>{"use strict";e.exports=require("path")},34135:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,10590))},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},47990:()=>{},50662:(e,s,r)=>{"use strict";r.d(s,{F:()=>l});var t=r(75986);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=t.$,l=(e,s)=>r=>{var t;if((null==s?void 0:s.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=s,c=Object.keys(l).map(e=>{let s=null==r?void 0:r[e],t=null==i?void 0:i[e];if(null===s)return null;let n=a(s)||a(t);return l[e][n]}),d=r&&Object.entries(r).reduce((e,s)=>{let[r,t]=s;return void 0===t||(e[r]=t),e},{});return n(e,c,null==s||null==(t=s.compoundVariants)?void 0:t.reduce((e,s)=>{let{class:r,className:t,...a}=s;return Object.entries(a).every(e=>{let[s,r]=e;return Array.isArray(r)?r.includes({...i,...d}[s]):({...i,...d})[s]===r})?[...e,r,t]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},51906:e=>{function s(e){var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}s.keys=()=>[],s.resolve=s,s.id=51906,e.exports=s},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62159:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,22230))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97680:(e,s,r)=>{"use strict";r.d(s,{s:()=>o});var t=r(37413),a=r(10590),n=r(4536),l=r.n(n),i=r(1215);let c={product:[{href:"/tours",label:"Tours"},{href:"/pricing",label:"Pricing"},{href:"/features",label:"Features"},{href:"/integrations",label:"Integrations"}],company:[{href:"/about",label:"About"},{href:"/contact",label:"Contact"},{href:"/careers",label:"Careers"},{href:"/blog",label:"Blog"}],resources:[{href:"/help",label:"Help Center"},{href:"/docs",label:"Documentation"},{href:"/api",label:"API Reference"},{href:"/status",label:"Status"}],legal:[{href:"/privacy",label:"Privacy Policy"},{href:"/terms",label:"Terms of Service"},{href:"/cookies",label:"Cookie Policy"},{href:"/gdpr",label:"GDPR"}]};function d(){return(0,t.jsx)("footer",{className:"border-t bg-background",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-8",children:[(0,t.jsxs)("div",{className:"md:col-span-1",children:[(0,t.jsxs)(l(),{href:"/",className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(i.A,{className:"h-6 w-6 text-primary"}),(0,t.jsx)("span",{className:"text-lg font-bold",children:"VirtualRealTour"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Premium 360\xb0 virtual tour platform designed for the Nigerian market."}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Made with ❤️ in Nigeria"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-4",children:"Product"}),(0,t.jsx)("ul",{className:"space-y-2",children:c.product.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:e.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:e.label})},e.href))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-4",children:"Company"}),(0,t.jsx)("ul",{className:"space-y-2",children:c.company.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:e.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:e.label})},e.href))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-4",children:"Resources"}),(0,t.jsx)("ul",{className:"space-y-2",children:c.resources.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:e.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:e.label})},e.href))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-4",children:"Legal"}),(0,t.jsx)("ul",{className:"space-y-2",children:c.legal.map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:e.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:e.label})},e.href))})]})]}),(0,t.jsxs)("div",{className:"border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"\xa9 2024 VirtualRealTour. All rights reserved."}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:[(0,t.jsx)(l(),{href:"/privacy",className:"text-sm text-muted-foreground hover:text-foreground",children:"Privacy"}),(0,t.jsx)(l(),{href:"/terms",className:"text-sm text-muted-foreground hover:text-foreground",children:"Terms"}),(0,t.jsx)(l(),{href:"/cookies",className:"text-sm text-muted-foreground hover:text-foreground",children:"Cookies"})]})]})]})})}function o({children:e}){return(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,t.jsx)(a.Header,{variant:"public"}),(0,t.jsx)("main",{className:"flex-1",children:e}),(0,t.jsx)(d,{})]})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,739,72,198,958,215],()=>r(5531));module.exports=t})();