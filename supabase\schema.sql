-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('user', 'pro', 'enterprise', 'admin');
CREATE TYPE tour_status AS ENUM ('draft', 'published', 'archived');
CREATE TYPE subscription_status AS ENUM ('active', 'canceled', 'past_due', 'incomplete');
CREATE TYPE hotspot_type AS ENUM ('info', 'link', 'image', 'video', 'audio', 'scene_transition');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    phone TEXT,
    company TEXT,
    website TEXT,
    bio TEXT,
    role user_role DEFAULT 'user',
    is_admin BOOLEAN DEFAULT FALSE,
    subscription_status subscription_status DEFAULT 'incomplete',
    subscription_plan TEXT DEFAULT 'free',
    subscription_id TEXT,
    trial_ends_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tours table
CREATE TABLE public.tours (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    slug TEXT UNIQUE NOT NULL,
    status tour_status DEFAULT 'draft',
    thumbnail_url TEXT,
    cover_image_url TEXT,
    category TEXT,
    location TEXT,
    address TEXT,
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'NGN',
    is_featured BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    password_protected BOOLEAN DEFAULT FALSE,
    password_hash TEXT,
    views_count INTEGER DEFAULT 0,
    likes_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    settings JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Scenes table (360° images/videos in a tour)
CREATE TABLE public.scenes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    tour_id UUID REFERENCES public.tours(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    media_url TEXT NOT NULL,
    media_type TEXT NOT NULL, -- 'image' or 'video'
    thumbnail_url TEXT,
    is_starting_scene BOOLEAN DEFAULT FALSE,
    order_index INTEGER NOT NULL,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Hotspots table (interactive elements in scenes)
CREATE TABLE public.hotspots (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    scene_id UUID REFERENCES public.scenes(id) ON DELETE CASCADE NOT NULL,
    type hotspot_type NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    position_x DECIMAL(5,2) NOT NULL, -- Percentage position
    position_y DECIMAL(5,2) NOT NULL,
    position_z DECIMAL(5,2),
    target_scene_id UUID REFERENCES public.scenes(id),
    content JSONB DEFAULT '{}', -- Flexible content storage
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Media files table
CREATE TABLE public.media_files (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type TEXT NOT NULL,
    width INTEGER,
    height INTEGER,
    duration DECIMAL(10,2), -- For videos
    is_360 BOOLEAN DEFAULT FALSE,
    processing_status TEXT DEFAULT 'pending', -- pending, processing, completed, failed
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tour analytics table
CREATE TABLE public.tour_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    tour_id UUID REFERENCES public.tours(id) ON DELETE CASCADE NOT NULL,
    visitor_id TEXT, -- Anonymous visitor tracking
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    event_type TEXT NOT NULL, -- view, like, share, hotspot_click, etc.
    event_data JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    country TEXT,
    city TEXT,
    device_type TEXT,
    browser TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Subscriptions table
CREATE TABLE public.subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    plan_id TEXT NOT NULL,
    status subscription_status NOT NULL,
    current_period_start TIMESTAMPTZ NOT NULL,
    current_period_end TIMESTAMPTZ NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    canceled_at TIMESTAMPTZ,
    trial_start TIMESTAMPTZ,
    trial_end TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_tours_user_id ON public.tours(user_id);
CREATE INDEX idx_tours_status ON public.tours(status);
CREATE INDEX idx_tours_slug ON public.tours(slug);
CREATE INDEX idx_tours_featured ON public.tours(is_featured);
CREATE INDEX idx_scenes_tour_id ON public.scenes(tour_id);
CREATE INDEX idx_scenes_order ON public.scenes(tour_id, order_index);
CREATE INDEX idx_hotspots_scene_id ON public.hotspots(scene_id);
CREATE INDEX idx_media_files_user_id ON public.media_files(user_id);
CREATE INDEX idx_analytics_tour_id ON public.tour_analytics(tour_id);
CREATE INDEX idx_analytics_created_at ON public.tour_analytics(created_at);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tours_updated_at BEFORE UPDATE ON public.tours FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_scenes_updated_at BEFORE UPDATE ON public.scenes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_hotspots_updated_at BEFORE UPDATE ON public.hotspots FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_media_files_updated_at BEFORE UPDATE ON public.media_files FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tours ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scenes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hotspots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tour_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);

-- Tours policies
CREATE POLICY "Anyone can view published tours" ON public.tours FOR SELECT USING (status = 'published' AND is_public = true);
CREATE POLICY "Users can view own tours" ON public.tours FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create tours" ON public.tours FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own tours" ON public.tours FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own tours" ON public.tours FOR DELETE USING (auth.uid() = user_id);

-- Scenes policies
CREATE POLICY "Anyone can view scenes of published tours" ON public.scenes FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.tours 
        WHERE tours.id = scenes.tour_id 
        AND tours.status = 'published' 
        AND tours.is_public = true
    )
);
CREATE POLICY "Users can manage own tour scenes" ON public.scenes FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.tours 
        WHERE tours.id = scenes.tour_id 
        AND tours.user_id = auth.uid()
    )
);

-- Similar policies for hotspots and media_files
CREATE POLICY "Anyone can view hotspots of published tours" ON public.hotspots FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.scenes 
        JOIN public.tours ON tours.id = scenes.tour_id
        WHERE scenes.id = hotspots.scene_id 
        AND tours.status = 'published' 
        AND tours.is_public = true
    )
);
CREATE POLICY "Users can manage own hotspots" ON public.hotspots FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.scenes 
        JOIN public.tours ON tours.id = scenes.tour_id
        WHERE scenes.id = hotspots.scene_id 
        AND tours.user_id = auth.uid()
    )
);

CREATE POLICY "Users can manage own media files" ON public.media_files FOR ALL USING (auth.uid() = user_id);

-- Analytics policies (insert only for tracking)
CREATE POLICY "Anyone can insert analytics" ON public.tour_analytics FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can view own tour analytics" ON public.tour_analytics FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.tours 
        WHERE tours.id = tour_analytics.tour_id 
        AND tours.user_id = auth.uid()
    )
);

-- Subscription policies
CREATE POLICY "Users can view own subscriptions" ON public.subscriptions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own subscriptions" ON public.subscriptions FOR UPDATE USING (auth.uid() = user_id);
