import { z } from 'zod';
import { MEDIA_TYPES, FILE_UPLOAD_LIMITS } from '@/lib/constants';

// Media upload validation schema
export const mediaUploadSchema = z.object({
  files: z
    .array(z.instanceof(File))
    .min(1, 'At least one file is required')
    .max(FILE_UPLOAD_LIMITS.MAX_FILES_PER_UPLOAD, `Maximum ${FILE_UPLOAD_LIMITS.MAX_FILES_PER_UPLOAD} files allowed`)
    .refine(
      (files) => files.every(file => file.size <= FILE_UPLOAD_LIMITS.MAX_FILE_SIZE),
      `File size must be less than ${FILE_UPLOAD_LIMITS.MAX_FILE_SIZE / (1024 * 1024)}MB`
    )
    .refine(
      (files) => files.every(file => 
        [...FILE_UPLOAD_LIMITS.ALLOWED_IMAGE_TYPES, ...FILE_UPLOAD_LIMITS.ALLOWED_VIDEO_TYPES, ...FILE_UPLOAD_LIMITS.ALLOWED_AUDIO_TYPES].includes(file.type)
      ),
      'Invalid file type'
    ),
  mediaType: z
    .enum([MEDIA_TYPES.IMAGE_360, MEDIA_TYPES.VIDEO_360, MEDIA_TYPES.IMAGE, MEDIA_TYPES.VIDEO, MEDIA_TYPES.AUDIO])
    .optional(),
  tags: z
    .array(z.string())
    .max(10, 'Maximum 10 tags allowed')
    .optional(),
  altText: z
    .string()
    .max(200, 'Alt text must be less than 200 characters')
    .optional(),
});

// Media update validation schema
export const mediaUpdateSchema = z.object({
  filename: z
    .string()
    .min(1, 'Filename is required')
    .max(255, 'Filename must be less than 255 characters')
    .regex(/^[a-zA-Z0-9._-]+$/, 'Filename contains invalid characters'),
  tags: z
    .array(z.string())
    .max(10, 'Maximum 10 tags allowed')
    .optional(),
  altText: z
    .string()
    .max(200, 'Alt text must be less than 200 characters')
    .optional(),
  metadata: z
    .record(z.any())
    .optional(),
});

// Media search/filter validation schema
export const mediaSearchSchema = z.object({
  query: z
    .string()
    .max(100, 'Search query must be less than 100 characters')
    .optional(),
  mediaType: z
    .enum([MEDIA_TYPES.IMAGE_360, MEDIA_TYPES.VIDEO_360, MEDIA_TYPES.IMAGE, MEDIA_TYPES.VIDEO, MEDIA_TYPES.AUDIO])
    .optional(),
  tags: z
    .array(z.string())
    .optional(),
  processingStatus: z
    .enum(['pending', 'processing', 'completed', 'failed'])
    .optional(),
  sortBy: z
    .enum(['created_at', 'updated_at', 'filename', 'file_size'])
    .default('created_at'),
  sortOrder: z
    .enum(['asc', 'desc'])
    .default('desc'),
  page: z
    .number()
    .min(1, 'Page must be at least 1')
    .default(1),
  limit: z
    .number()
    .min(1, 'Limit must be at least 1')
    .max(50, 'Limit must be at most 50')
    .default(20),
});

// Media processing validation schema
export const mediaProcessingSchema = z.object({
  mediaId: z.string().min(1, 'Media ID is required'),
  operations: z.array(z.object({
    type: z.enum(['resize', 'compress', 'convert', 'thumbnail', 'optimize']),
    params: z.record(z.any()),
  })).min(1, 'At least one operation is required'),
  priority: z
    .enum(['low', 'normal', 'high'])
    .default('normal'),
});

// Bulk media operations validation schema
export const bulkMediaOperationSchema = z.object({
  operation: z.enum(['delete', 'tag', 'untag', 'process']),
  mediaIds: z
    .array(z.string())
    .min(1, 'At least one media item must be selected')
    .max(100, 'Maximum 100 media items can be processed at once'),
  params: z
    .record(z.any())
    .optional(),
});

// Media compression settings validation schema
export const mediaCompressionSchema = z.object({
  quality: z
    .number()
    .min(0.1, 'Quality must be at least 0.1')
    .max(1, 'Quality must be at most 1')
    .default(0.8),
  maxWidth: z
    .number()
    .min(100, 'Max width must be at least 100px')
    .max(8000, 'Max width must be at most 8000px')
    .default(1920),
  maxHeight: z
    .number()
    .min(100, 'Max height must be at least 100px')
    .max(8000, 'Max height must be at most 8000px')
    .default(1080),
  format: z
    .enum(['jpeg', 'png', 'webp'])
    .default('webp'),
  progressive: z
    .boolean()
    .default(true),
});

// 360° media validation schema
export const media360Schema = z.object({
  projection: z
    .enum(['equirectangular', 'cubemap', 'cylindrical'])
    .default('equirectangular'),
  stereo: z
    .enum(['mono', 'top-bottom', 'left-right'])
    .default('mono'),
  fov: z
    .number()
    .min(60, 'Field of view must be at least 60 degrees')
    .max(360, 'Field of view must be at most 360 degrees')
    .default(360),
  initialView: z.object({
    yaw: z.number().min(-180).max(180).default(0),
    pitch: z.number().min(-90).max(90).default(0),
    fov: z.number().min(30).max(120).default(75),
  }).optional(),
});

// Media analytics validation schema
export const mediaAnalyticsSchema = z.object({
  mediaId: z.string().min(1, 'Media ID is required'),
  startDate: z
    .string()
    .datetime('Invalid start date format')
    .optional(),
  endDate: z
    .string()
    .datetime('Invalid end date format')
    .optional(),
  metrics: z
    .array(z.enum(['views', 'downloads', 'shares', 'processing_time']))
    .default(['views']),
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Start date must be before end date',
  path: ['endDate'],
});

// Export types
export type MediaUploadInput = z.infer<typeof mediaUploadSchema>;
export type MediaUpdateInput = z.infer<typeof mediaUpdateSchema>;
export type MediaSearchInput = z.infer<typeof mediaSearchSchema>;
export type MediaProcessingInput = z.infer<typeof mediaProcessingSchema>;
export type BulkMediaOperationInput = z.infer<typeof bulkMediaOperationSchema>;
export type MediaCompressionInput = z.infer<typeof mediaCompressionSchema>;
export type Media360Input = z.infer<typeof media360Schema>;
export type MediaAnalyticsInput = z.infer<typeof mediaAnalyticsSchema>;
