// This file will be generated by Supabase CLI
// For now, we'll create a basic type structure

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          phone: string | null;
          company: string | null;
          website: string | null;
          bio: string | null;
          location: string | null;
          subscription_tier: string;
          subscription_status: string;
          subscription_expires_at: string | null;
          total_tours: number;
          total_views: number;
          is_verified: boolean;
          is_admin: boolean;
          preferences: Record<string, any>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          company?: string | null;
          website?: string | null;
          bio?: string | null;
          location?: string | null;
          subscription_tier?: string;
          subscription_status?: string;
          subscription_expires_at?: string | null;
          total_tours?: number;
          total_views?: number;
          is_verified?: boolean;
          is_admin?: boolean;
          preferences?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          company?: string | null;
          website?: string | null;
          bio?: string | null;
          location?: string | null;
          subscription_tier?: string;
          subscription_status?: string;
          subscription_expires_at?: string | null;
          total_tours?: number;
          total_views?: number;
          is_verified?: boolean;
          is_admin?: boolean;
          preferences?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
      };
      tours: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          description: string | null;
          slug: string;
          thumbnail_url: string | null;
          category: string | null;
          tags: string[] | null;
          location: string | null;
          latitude: number | null;
          longitude: number | null;
          address: string | null;
          price: number | null;
          currency: string;
          status: string;
          visibility: string;
          featured: boolean;
          featured_until: string | null;
          total_scenes: number;
          total_views: number;
          total_likes: number;
          total_shares: number;
          settings: Record<string, any>;
          seo_title: string | null;
          seo_description: string | null;
          seo_keywords: string[] | null;
          published_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          description?: string | null;
          slug: string;
          thumbnail_url?: string | null;
          category?: string | null;
          tags?: string[] | null;
          location?: string | null;
          latitude?: number | null;
          longitude?: number | null;
          address?: string | null;
          price?: number | null;
          currency?: string;
          status?: string;
          visibility?: string;
          featured?: boolean;
          featured_until?: string | null;
          total_scenes?: number;
          total_views?: number;
          total_likes?: number;
          total_shares?: number;
          settings?: Record<string, any>;
          seo_title?: string | null;
          seo_description?: string | null;
          seo_keywords?: string[] | null;
          published_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          title?: string;
          description?: string | null;
          slug?: string;
          thumbnail_url?: string | null;
          category?: string | null;
          tags?: string[] | null;
          location?: string | null;
          latitude?: number | null;
          longitude?: number | null;
          address?: string | null;
          price?: number | null;
          currency?: string;
          status?: string;
          visibility?: string;
          featured?: boolean;
          featured_until?: string | null;
          total_scenes?: number;
          total_views?: number;
          total_likes?: number;
          total_shares?: number;
          settings?: Record<string, any>;
          seo_title?: string | null;
          seo_description?: string | null;
          seo_keywords?: string[] | null;
          published_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      scenes: {
        Row: {
          id: string;
          tour_id: string;
          title: string;
          description: string | null;
          media_id: string | null;
          order_index: number;
          is_starting_scene: boolean;
          position: Record<string, any> | null;
          rotation: Record<string, any> | null;
          settings: Record<string, any>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          tour_id: string;
          title: string;
          description?: string | null;
          media_id?: string | null;
          order_index: number;
          is_starting_scene?: boolean;
          position?: Record<string, any> | null;
          rotation?: Record<string, any> | null;
          settings?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          tour_id?: string;
          title?: string;
          description?: string | null;
          media_id?: string | null;
          order_index?: number;
          is_starting_scene?: boolean;
          position?: Record<string, any> | null;
          rotation?: Record<string, any> | null;
          settings?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
      };
      hotspots: {
        Row: {
          id: string;
          scene_id: string;
          type: string;
          title: string | null;
          description: string | null;
          position: Record<string, any>;
          rotation: Record<string, any> | null;
          scale: Record<string, any>;
          target_scene_id: string | null;
          target_url: string | null;
          media_id: string | null;
          whatsapp_number: string | null;
          whatsapp_message: string | null;
          product_data: Record<string, any> | null;
          style_config: Record<string, any>;
          animation_config: Record<string, any>;
          is_visible: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          scene_id: string;
          type: string;
          title?: string | null;
          description?: string | null;
          position: Record<string, any>;
          rotation?: Record<string, any> | null;
          scale?: Record<string, any>;
          target_scene_id?: string | null;
          target_url?: string | null;
          media_id?: string | null;
          whatsapp_number?: string | null;
          whatsapp_message?: string | null;
          product_data?: Record<string, any> | null;
          style_config?: Record<string, any>;
          animation_config?: Record<string, any>;
          is_visible?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          scene_id?: string;
          type?: string;
          title?: string | null;
          description?: string | null;
          position?: Record<string, any>;
          rotation?: Record<string, any> | null;
          scale?: Record<string, any>;
          target_scene_id?: string | null;
          target_url?: string | null;
          media_id?: string | null;
          whatsapp_number?: string | null;
          whatsapp_message?: string | null;
          product_data?: Record<string, any> | null;
          style_config?: Record<string, any>;
          animation_config?: Record<string, any>;
          is_visible?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      media: {
        Row: {
          id: string;
          user_id: string;
          filename: string;
          original_filename: string;
          file_path: string;
          file_size: number | null;
          mime_type: string | null;
          media_type: string;
          dimensions: Record<string, any> | null;
          duration: number | null;
          processing_status: string;
          processing_metadata: Record<string, any>;
          thumbnail_url: string | null;
          preview_url: string | null;
          optimized_url: string | null;
          metadata: Record<string, any>;
          tags: string[] | null;
          alt_text: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          filename: string;
          original_filename: string;
          file_path: string;
          file_size?: number | null;
          mime_type?: string | null;
          media_type: string;
          dimensions?: Record<string, any> | null;
          duration?: number | null;
          processing_status?: string;
          processing_metadata?: Record<string, any>;
          thumbnail_url?: string | null;
          preview_url?: string | null;
          optimized_url?: string | null;
          metadata?: Record<string, any>;
          tags?: string[] | null;
          alt_text?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          filename?: string;
          original_filename?: string;
          file_path?: string;
          file_size?: number | null;
          mime_type?: string | null;
          media_type?: string;
          dimensions?: Record<string, any> | null;
          duration?: number | null;
          processing_status?: string;
          processing_metadata?: Record<string, any>;
          thumbnail_url?: string | null;
          preview_url?: string | null;
          optimized_url?: string | null;
          metadata?: Record<string, any>;
          tags?: string[] | null;
          alt_text?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      subscriptions: {
        Row: {
          id: string;
          user_id: string;
          plan_id: string;
          plan_name: string;
          status: string;
          current_period_start: string;
          current_period_end: string;
          cancel_at_period_end: boolean;
          canceled_at: string | null;
          stripe_subscription_id: string | null;
          stripe_customer_id: string | null;
          paystack_subscription_code: string | null;
          paystack_customer_code: string | null;
          payment_method: string | null;
          amount: number | null;
          currency: string;
          trial_start: string | null;
          trial_end: string | null;
          metadata: Record<string, any>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          plan_id: string;
          plan_name: string;
          status: string;
          current_period_start: string;
          current_period_end: string;
          cancel_at_period_end?: boolean;
          canceled_at?: string | null;
          stripe_subscription_id?: string | null;
          stripe_customer_id?: string | null;
          paystack_subscription_code?: string | null;
          paystack_customer_code?: string | null;
          payment_method?: string | null;
          amount?: number | null;
          currency?: string;
          trial_start?: string | null;
          trial_end?: string | null;
          metadata?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          plan_id?: string;
          plan_name?: string;
          status?: string;
          current_period_start?: string;
          current_period_end?: string;
          cancel_at_period_end?: boolean;
          canceled_at?: string | null;
          stripe_subscription_id?: string | null;
          stripe_customer_id?: string | null;
          paystack_subscription_code?: string | null;
          paystack_customer_code?: string | null;
          payment_method?: string | null;
          amount?: number | null;
          currency?: string;
          trial_start?: string | null;
          trial_end?: string | null;
          metadata?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
      };
      analytics: {
        Row: {
          id: string;
          tour_id: string | null;
          scene_id: string | null;
          hotspot_id: string | null;
          user_id: string | null;
          session_id: string | null;
          event_type: string;
          event_data: Record<string, any>;
          ip_address: string | null;
          user_agent: string | null;
          referrer: string | null;
          country: string | null;
          city: string | null;
          device_type: string | null;
          browser: string | null;
          os: string | null;
          timestamp: string;
        };
        Insert: {
          id?: string;
          tour_id?: string | null;
          scene_id?: string | null;
          hotspot_id?: string | null;
          user_id?: string | null;
          session_id?: string | null;
          event_type: string;
          event_data?: Record<string, any>;
          ip_address?: string | null;
          user_agent?: string | null;
          referrer?: string | null;
          country?: string | null;
          city?: string | null;
          device_type?: string | null;
          browser?: string | null;
          os?: string | null;
          timestamp?: string;
        };
        Update: {
          id?: string;
          tour_id?: string | null;
          scene_id?: string | null;
          hotspot_id?: string | null;
          user_id?: string | null;
          session_id?: string | null;
          event_type?: string;
          event_data?: Record<string, any>;
          ip_address?: string | null;
          user_agent?: string | null;
          referrer?: string | null;
          country?: string | null;
          city?: string | null;
          device_type?: string | null;
          browser?: string | null;
          os?: string | null;
          timestamp?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      increment_tour_views: {
        Args: {
          tour_id: string;
        };
        Returns: void;
      };
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
