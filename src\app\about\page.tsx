import { <PERSON>ada<PERSON> } from "next";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PublicLayout } from "@/components/layout/public-layout";
import { Eye, Users, Globe, Award, Heart, Zap } from "lucide-react";

export const metadata: Metadata = {
  title: "About Us",
  description: "Learn about VirtualRealTour's mission to revolutionize virtual experiences in Nigeria and beyond.",
};

const stats = [
  { label: "Tours Created", value: "10,000+", icon: Eye },
  { label: "Happy Customers", value: "2,500+", icon: Users },
  { label: "Countries Served", value: "15+", icon: Globe },
  { label: "Awards Won", value: "8", icon: Award },
];

const team = [
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "CEO & Founder",
    bio: "Former tech lead at major Nigerian fintech. Passionate about immersive technologies.",
    image: "/team/ceo.jpg",
  },
  {
    name: "<PERSON><PERSON>",
    role: "<PERSON><PERSON>",
    bio: "Expert in 3D graphics and WebXR. Previously at international VR companies.",
    image: "/team/cto.jpg",
  },
  {
    name: "<PERSON>",
    role: "Head of Design",
    bio: "Award-winning UX designer focused on creating intuitive virtual experiences.",
    image: "/team/design.jpg",
  },
];

const values = [
  {
    icon: Heart,
    title: "Customer First",
    description: "Every decision we make is guided by what's best for our customers and their success.",
  },
  {
    icon: Zap,
    title: "Innovation",
    description: "We constantly push the boundaries of what's possible in virtual tour technology.",
  },
  {
    icon: Users,
    title: "Accessibility",
    description: "Making advanced virtual tour technology accessible to businesses of all sizes.",
  },
  {
    icon: Globe,
    title: "Local Focus",
    description: "Built specifically for the Nigerian market with local payment and communication integrations.",
  },
];

export default function AboutPage() {
  return (
    <PublicLayout>
      <div className="py-20">
        <div className="container mx-auto px-4">
          {/* Hero Section */}
          <div className="text-center mb-20">
            <Badge variant="secondary" className="mb-4">About VirtualRealTour</Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Revolutionizing Virtual Experiences in Nigeria
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              We're on a mission to make immersive 360° virtual tours accessible to every 
              business in Nigeria, from small local shops to large enterprises.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20">
            {stats.map((stat) => (
              <Card key={stat.label} className="text-center">
                <CardContent className="pt-6">
                  <stat.icon className="h-8 w-8 text-primary mx-auto mb-2" />
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Story Section */}
          <div className="grid md:grid-cols-2 gap-12 items-center mb-20">
            <div>
              <h2 className="text-3xl font-bold mb-6">Our Story</h2>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  VirtualRealTour was born from a simple observation: Nigerian businesses 
                  needed better ways to showcase their spaces online, but existing solutions 
                  were either too expensive or not designed for the local market.
                </p>
                <p>
                  Founded in 2024 by a team of Nigerian tech entrepreneurs, we set out to 
                  create a platform that would democratize access to professional virtual 
                  tour technology while addressing the unique needs of African businesses.
                </p>
                <p>
                  Today, we're proud to serve thousands of businesses across Nigeria and 
                  beyond, from real estate agencies in Lagos to educational institutions 
                  in Abuja, helping them create immersive experiences that drive engagement 
                  and sales.
                </p>
              </div>
            </div>
            <div className="bg-muted rounded-lg p-8 text-center">
              <Eye className="h-16 w-16 text-primary mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Our Vision</h3>
              <p className="text-muted-foreground">
                To become Africa's leading virtual tour platform, empowering businesses 
                to create immersive digital experiences that connect with customers 
                across the continent.
              </p>
            </div>
          </div>

          {/* Values */}
          <div className="mb-20">
            <h2 className="text-3xl font-bold text-center mb-12">Our Values</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value) => (
                <Card key={value.title}>
                  <CardHeader className="text-center">
                    <value.icon className="h-12 w-12 text-primary mx-auto mb-4" />
                    <CardTitle className="text-lg">{value.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center">
                      {value.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Team Section */}
          <div className="mb-20">
            <h2 className="text-3xl font-bold text-center mb-12">Meet Our Team</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {team.map((member) => (
                <Card key={member.name}>
                  <CardHeader className="text-center">
                    <div className="w-24 h-24 bg-muted rounded-full mx-auto mb-4 flex items-center justify-center">
                      <Users className="h-12 w-12 text-muted-foreground" />
                    </div>
                    <CardTitle>{member.name}</CardTitle>
                    <CardDescription>{member.role}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground text-center">
                      {member.bio}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Technology Section */}
          <Card className="mb-20">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Built for Nigeria</CardTitle>
              <CardDescription>
                Our platform is specifically designed for the Nigerian market
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-8 text-center">
                <div>
                  <h3 className="font-semibold mb-2">Local Payments</h3>
                  <p className="text-sm text-muted-foreground">
                    Integrated with Paystack, Flutterwave, and other Nigerian payment providers
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">WhatsApp Integration</h3>
                  <p className="text-sm text-muted-foreground">
                    Direct integration with WhatsApp Business for seamless customer communication
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Mobile-First</h3>
                  <p className="text-sm text-muted-foreground">
                    Optimized for mobile devices, considering Nigeria's mobile-first internet usage
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact CTA */}
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">Want to Learn More?</h2>
            <p className="text-muted-foreground mb-8">
              We'd love to hear from you and answer any questions about our platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Card className="p-4">
                <h3 className="font-semibold mb-2">General Inquiries</h3>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </Card>
              <Card className="p-4">
                <h3 className="font-semibold mb-2">Support</h3>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </Card>
              <Card className="p-4">
                <h3 className="font-semibold mb-2">Sales</h3>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
}
