'use client';

import { useEffect, useState } from 'react';
import { Battery, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AppLoadingProps {
  message?: string;
  className?: string;
}

export function AppLoading({ 
  message = "Personalising your experience", 
  className 
}: AppLoadingProps) {
  const [batteryLevel, setBatteryLevel] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setBatteryLevel(prev => {
        if (prev >= 100) {
          return 0; // Reset animation
        }
        return prev + 2;
      });
    }, 50);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={cn(
      "fixed inset-0 bg-background/95 backdrop-blur-sm flex items-center justify-center z-50",
      className
    )}>
      <div className="text-center space-y-6 max-w-sm mx-auto px-4">
        {/* Battery Icon with Charging Animation */}
        <div className="relative mx-auto w-20 h-12">
          {/* Battery Body */}
          <div className="w-16 h-10 border-2 border-primary rounded-sm relative bg-background">
            {/* Battery Fill */}
            <div 
              className="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-sm transition-all duration-100 ease-out"
              style={{ width: `${batteryLevel}%` }}
            />
            
            {/* Battery Percentage */}
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-bold text-foreground mix-blend-difference">
                {Math.round(batteryLevel)}%
              </span>
            </div>
          </div>
          
          {/* Battery Terminal */}
          <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 w-1 h-4 bg-primary rounded-r-sm" />
          
          {/* Charging Bolt */}
          <div className="absolute -top-1 -right-1">
            <Zap className="h-4 w-4 text-yellow-500 animate-pulse" />
          </div>
        </div>

        {/* Loading Message */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-foreground">
            {message}
          </h3>
          <div className="flex items-center justify-center space-x-1">
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-muted rounded-full h-1">
          <div 
            className="bg-primary h-1 rounded-full transition-all duration-100 ease-out"
            style={{ width: `${batteryLevel}%` }}
          />
        </div>

        {/* Loading Tips */}
        <p className="text-sm text-muted-foreground">
          Optimizing for your device...
        </p>
      </div>
    </div>
  );
}

// Simpler loading component for quick use
export function SimpleLoading({ message = "Loading..." }: { message?: string }) {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="text-center space-y-4">
        <div className="relative">
          <Battery className="h-8 w-8 text-muted-foreground mx-auto" />
          <Zap className="h-4 w-4 text-yellow-500 absolute -top-1 -right-1 animate-pulse" />
        </div>
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  );
}
