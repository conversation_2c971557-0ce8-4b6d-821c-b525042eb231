import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Eye, Globe, Smartphone, Zap } from "lucide-react";
import { PublicLayout } from "@/components/layout/public-layout";
import Link from "next/link";

export default function Home() {
  return (
    <PublicLayout>

      {/* Hero Section */}
      <section className="flex items-center justify-center py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-6">
            Create Stunning{" "}
            <span className="text-primary">360° Virtual Tours</span>{" "}
            for Nigeria
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Transform your real estate, education, hospitality, and retail spaces into immersive
            virtual experiences. Built specifically for the Nigerian market with local payment
            integration and WhatsApp connectivity.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/auth/signup">Start Creating Tours</Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/tours">Explore Tours</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Why Choose VirtualRealTour?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Premium features designed for the Nigerian market with enterprise-grade
              performance and luxury brand-inspired design.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card>
              <CardHeader>
                <Eye className="h-12 w-12 text-primary mb-4" />
                <CardTitle>360° Immersive Views</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Create stunning 360° virtual tours with high-quality image and video support.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Smartphone className="h-12 w-12 text-primary mb-4" />
                <CardTitle>WhatsApp Integration</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Connect directly with prospects through integrated WhatsApp Business API.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Globe className="h-12 w-12 text-primary mb-4" />
                <CardTitle>Nigerian Payments</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Accept payments through Paystack, Flutterwave, and international options.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Zap className="h-12 w-12 text-primary mb-4" />
                <CardTitle>Lightning Fast</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Optimized for fast loading with progressive enhancement and CDN delivery.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Featured Tours Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Experience Virtual Tours
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Explore these amazing virtual tours created by our users across Nigeria.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Luxury Apartment in Victoria Island",
                location: "Lagos, Nigeria",
                category: "Real Estate",
                views: "2,847",
                image: "/tours/apartment.jpg",
              },
              {
                title: "University of Lagos Campus Tour",
                location: "Lagos, Nigeria",
                category: "Education",
                views: "5,234",
                image: "/tours/university.jpg",
              },
              {
                title: "Transcorp Hilton Hotel",
                location: "Abuja, Nigeria",
                category: "Hospitality",
                views: "3,156",
                image: "/tours/hotel.jpg",
              },
              {
                title: "Shoprite Ikeja Mall",
                location: "Lagos, Nigeria",
                category: "Retail",
                views: "1,892",
                image: "/tours/mall.jpg",
              },
              {
                title: "National Theatre Lagos",
                location: "Lagos, Nigeria",
                category: "Entertainment",
                views: "4,567",
                image: "/tours/theatre.jpg",
              },
              {
                title: "Eko Atlantic City",
                location: "Lagos, Nigeria",
                category: "Real Estate",
                views: "6,789",
                image: "/tours/eko-atlantic.jpg",
              },
            ].map((tour, index) => (
              <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div className="aspect-video bg-muted relative">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Eye className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <div className="absolute top-4 left-4">
                    <span className="bg-primary text-primary-foreground px-2 py-1 rounded-full text-xs font-medium">
                      {tour.category}
                    </span>
                  </div>
                  <div className="absolute bottom-4 right-4">
                    <Button size="sm" className="bg-black/50 hover:bg-black/70 text-white">
                      <Eye className="h-4 w-4 mr-1" />
                      View Tour
                    </Button>
                  </div>
                </div>

                <CardHeader>
                  <CardTitle className="line-clamp-2 text-lg">
                    {tour.title}
                  </CardTitle>
                  <CardDescription className="flex items-center text-sm">
                    <span>{tour.location}</span>
                    <span className="mx-2">•</span>
                    <span>{tour.views} views</span>
                  </CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button size="lg" variant="outline" asChild>
              <Link href="/tours">
                Explore All Tours
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </PublicLayout>
  );
}
