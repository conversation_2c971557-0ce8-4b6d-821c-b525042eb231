# Vercel Separate Account Setup Guide

## 🎯 **Goal**: Deploy to Different Vercel Account + Auto-Deploy from GitHub

### 📋 **Strategy Overview**
1. **Use Vercel Dashboard** (not CLI) to avoid account conflicts
2. **Set up GitHub integration** for automatic deployments
3. **Configure branch-based deployments**:
   - `main/master` → Production deployment
   - `dev` → Preview deployments
   - Feature branches → Preview deployments

## 🚀 **Step-by-Step Setup**

### **Step 1: Prepare GitHub Repository**

#### Fix GitHub Connectivity First:
```bash
# Option A: Use SSH (Recommended)
git remote set-<NAME_EMAIL>:iwalk-jo/virtualrealtour_platform.git

# Option B: Try HTTPS with different settings
git config --global http.postBuffer *********
git config --global http.timeout 300

# Test connection
git remote -v
```

#### Create Branch Structure:
```bash
# Create and switch to dev branch
git checkout -b dev

# Push current state to main
git checkout main
git add .
git commit -m "VirtualRealTour platform ready for deployment"
git push origin main

# Push dev branch
git checkout dev
git push origin dev
```

### **Step 2: Vercel Dashboard Setup (No CLI Needed)**

#### Go to New Vercel Account:
1. **Sign up/Login** to the different Vercel account
2. **Click "New Project"**
3. **Import Git Repository**
4. **Connect GitHub account** (if not connected)
5. **Select Repository**: `iwalk-jo/virtualrealtour_platform`

#### Configure Project Settings:
```
Project Name: virtualrealtour-platform
Framework: Next.js
Root Directory: ./
Build Command: npm run build
Output Directory: .next
Install Command: npm install
Development Command: npm run dev
```

#### Environment Variables:
```
NEXT_PUBLIC_SUPABASE_URL=https://maudhokdhyhspfpasnfm.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### **Step 3: Configure Auto-Deploy Settings**

#### Production Branch:
- **Branch**: `main` or `master`
- **Auto-deploy**: ✅ Enabled
- **Domain**: `your-app.vercel.app`

#### Preview Branches:
- **Branch**: `dev` and all other branches
- **Auto-deploy**: ✅ Enabled
- **Domain**: `branch-name.your-app.vercel.app`

### **Step 4: Development Workflow**

#### Daily Development:
```bash
# Work on dev branch
git checkout dev

# Make changes
# ... code changes ...

# Commit and push (triggers preview deployment)
git add .
git commit -m "Feature: Add new functionality"
git push origin dev
```

#### Deploy to Production:
```bash
# When feature is ready
git checkout main
git merge dev
git push origin main  # Triggers production deployment
```

#### Hotfix Workflow:
```bash
# Create hotfix branch
git checkout main
git checkout -b hotfix/urgent-fix

# Make fix
# ... fix code ...

# Deploy hotfix
git add .
git commit -m "Hotfix: Critical bug fix"
git push origin hotfix/urgent-fix  # Creates preview

# Merge to main when tested
git checkout main
git merge hotfix/urgent-fix
git push origin main  # Deploys to production
```

## 🔧 **Alternative: Manual Vercel Setup**

### **If GitHub Connection Fails:**

#### Option 1: Vercel CLI with Different Account
```bash
# Install Vercel CLI globally
npm install -g vercel

# Login to different account
vercel login

# Deploy from project directory
vercel --prod

# Link to specific account/team
vercel link
```

#### Option 2: ZIP Upload Method
1. **Create deployment ZIP**:
```bash
# Create clean build
npm run build

# Create ZIP of project (exclude node_modules, .git)
# Upload ZIP to Vercel dashboard
```

#### Option 3: GitHub Actions (Advanced)
Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to Vercel
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 📊 **Deployment URLs Structure**

### **Production**:
- **URL**: `https://virtualrealtour-platform.vercel.app`
- **Branch**: `main`
- **Trigger**: Push to main branch

### **Development Preview**:
- **URL**: `https://dev.virtualrealtour-platform.vercel.app`
- **Branch**: `dev`
- **Trigger**: Push to dev branch

### **Feature Previews**:
- **URL**: `https://feature-branch.virtualrealtour-platform.vercel.app`
- **Branch**: Any feature branch
- **Trigger**: Push to any branch

## 🎯 **Recommended Workflow**

### **For You (Developer)**:
1. **Work on `dev` branch** for all development
2. **Push to `dev`** to see preview deployments
3. **Merge to `main`** only when ready for production
4. **Use feature branches** for experimental features

### **For Client/Testing**:
- **Production**: `https://your-app.vercel.app`
- **Latest Development**: `https://dev.your-app.vercel.app`
- **Specific Features**: `https://feature-name.your-app.vercel.app`

## 🔒 **Account Isolation**

### **Your Existing Vercel Account**:
- **Remains untouched**
- **No CLI conflicts**
- **Separate login sessions**

### **New Project Account**:
- **Completely separate**
- **Own environment variables**
- **Own deployment settings**
- **Own domain management**

## 🚨 **Troubleshooting**

### **If GitHub Connection Fails**:
1. **Try SSH method** first
2. **Use Vercel CLI** with account switching
3. **Upload ZIP** as last resort
4. **Check network/firewall** settings

### **If Build Fails on Vercel**:
1. **Check environment variables**
2. **Verify build command**: `npm run build`
3. **Check Node.js version** (use 18.x or 20.x)
4. **Review build logs** in Vercel dashboard

## ✅ **Success Checklist**

- [ ] GitHub repository accessible
- [ ] Vercel account connected to GitHub
- [ ] Environment variables configured
- [ ] Auto-deploy enabled for main branch
- [ ] Preview deployments working for dev branch
- [ ] Production URL accessible
- [ ] All pages loading correctly
- [ ] Supabase connection working

Your VirtualRealTour platform will be automatically deployed! 🚀
