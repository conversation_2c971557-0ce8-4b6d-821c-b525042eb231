(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__dc15d093._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$auth$2d$helpers$2d$nextjs$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/auth-helpers-nextjs/dist/index.js [middleware-edge] (ecmascript)");
(()=>{
    const e = new Error("Cannot find module '@/utils/supabase/middleware'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
;
;
// Define protected routes that require authentication
const protectedRoutes = [
    '/dashboard',
    '/dashboard/:path*',
    '/profile',
    '/settings'
];
// Define auth routes that should redirect to dashboard if already authenticated
const authRoutes = [
    '/auth/signin',
    '/auth/signup',
    '/auth/forgot-password',
    '/auth/reset-password'
];
// Define admin routes that require admin privileges
const adminRoutes = [
    '/admin',
    '/admin/:path*'
];
// Define API routes that need authentication
const protectedApiRoutes = [
    '/api/tours',
    '/api/media',
    '/api/users/profile',
    '/api/analytics',
    '/api/subscriptions'
];
function isRouteMatch(pathname, routes) {
    return routes.some((route)=>{
        if (route.includes(':path*')) {
            const baseRoute = route.replace('/:path*', '');
            return pathname.startsWith(baseRoute);
        }
        return pathname === route;
    });
}
async function middleware(request) {
    // Check if Supabase environment variables are available
    const hasSupabaseConfig = ("TURBOPACK compile-time value", "https://maudhokdhyhspfpasnfm.supabase.co") && ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1hdWRob2tkaHloc3BmcGFzbmZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjgwMDQsImV4cCI6MjA2NDc0NDAwNH0.yaD0ot54tD5tj5Bz2SMZXgp6x6fzVXsAz2xjvB-bFFo");
    let session = null;
    let response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    if ("TURBOPACK compile-time truthy", 1) {
        try {
            // Use new SSR middleware client
            const { supabase: supabase1, response: supabaseResponse } = createClient(request);
            response = supabaseResponse;
            const { data } = await supabase1.auth.getSession();
            session = data.session;
        } catch (error) {
            console.error('Error getting session in middleware:', error);
            // Fallback to legacy middleware client
            try {
                const supabase1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$auth$2d$helpers$2d$nextjs$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createMiddlewareClient"])({
                    req: request,
                    res: response
                });
                const { data } = await supabase1.auth.getSession();
                session = data.session;
            } catch (fallbackError) {
                console.error('Fallback middleware client also failed:', fallbackError);
            }
        }
    }
    const { pathname } = request.nextUrl;
    // Handle API routes
    if (pathname.startsWith('/api/')) {
        // Check if API route requires authentication
        if (isRouteMatch(pathname, protectedApiRoutes)) {
            if (!session) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Authentication required'
                }, {
                    status: 401
                });
            }
        }
        // Check for admin API routes
        if (pathname.startsWith('/api/admin/')) {
            if (!session) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Authentication required'
                }, {
                    status: 401
                });
            }
            // Check if user is admin
            const { data: user } = await supabase.from('users').select('is_admin').eq('id', session.user.id).single();
            if (!user?.is_admin) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Admin access required'
                }, {
                    status: 403
                });
            }
        }
        return response;
    }
    // Handle auth routes - redirect to dashboard if already authenticated
    if (isRouteMatch(pathname, authRoutes)) {
        if (hasSupabaseConfig && session) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
        }
        return response;
    }
    // Handle protected routes - redirect to signin if not authenticated
    if (isRouteMatch(pathname, protectedRoutes)) {
        if (hasSupabaseConfig && !session) {
            const redirectUrl = new URL('/auth/signin', request.url);
            redirectUrl.searchParams.set('redirectTo', pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(redirectUrl);
        }
        return response;
    }
    // Handle admin routes
    if (isRouteMatch(pathname, adminRoutes)) {
        if (!hasSupabaseConfig || !session) {
            const redirectUrl = new URL('/auth/signin', request.url);
            redirectUrl.searchParams.set('redirectTo', pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(redirectUrl);
        }
        // Check if user is admin
        try {
            const supabase1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$auth$2d$helpers$2d$nextjs$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createMiddlewareClient"])({
                req: request,
                res: response
            });
            const { data: user } = await supabase1.from('users').select('is_admin').eq('id', session.user.id).single();
            if (!user?.is_admin) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
            }
        } catch (error) {
            console.error('Error checking admin status:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
        }
        return response;
    }
    // Handle subscription-based access
    if (pathname.startsWith('/dashboard/') && session) {
        // Get user's subscription status
        const { data: subscription } = await supabase.from('subscriptions').select('status, plan_name').eq('user_id', session.user.id).single();
        // Check if user has access to premium features
        const premiumRoutes = [
            '/dashboard/analytics',
            '/dashboard/integrations',
            '/dashboard/api'
        ];
        if (isRouteMatch(pathname, premiumRoutes)) {
            const hasActivePremiumPlan = subscription?.status === 'active' && [
                'pro',
                'enterprise'
            ].includes(subscription.plan_name?.toLowerCase() || '');
            if (!hasActivePremiumPlan) {
                const redirectUrl = new URL('/dashboard/upgrade', request.url);
                redirectUrl.searchParams.set('feature', pathname.split('/').pop() || '');
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(redirectUrl);
            }
        }
    }
    // Add security headers
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-pathname', pathname);
    // Set CSP headers for security
    const cspHeader = `
    default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.app *.supabase.co *.stripe.com *.paystack.co;
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: blob: *.supabase.co *.stripe.com;
    font-src 'self';
    connect-src 'self' *.supabase.co *.stripe.com *.paystack.co *.flutterwave.com wss:;
    media-src 'self' *.supabase.co;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
  `.replace(/\s{2,}/g, ' ').trim();
    response.headers.set('Content-Security-Policy', cspHeader);
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    return response;
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */ '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__dc15d093._.js.map