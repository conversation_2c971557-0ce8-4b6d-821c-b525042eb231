{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<\"textarea\">\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Eye } from 'lucide-react';\n\nconst footerLinks = {\n  product: [\n    { href: '/tours', label: 'Tours' },\n    { href: '/pricing', label: 'Pricing' },\n    { href: '/features', label: 'Features' },\n    { href: '/integrations', label: 'Integrations' },\n  ],\n  company: [\n    { href: '/about', label: 'About' },\n    { href: '/contact', label: 'Contact' },\n    { href: '/careers', label: 'Careers' },\n    { href: '/blog', label: 'Blog' },\n  ],\n  resources: [\n    { href: '/help', label: 'Help Center' },\n    { href: '/docs', label: 'Documentation' },\n    { href: '/api', label: 'API Reference' },\n    { href: '/status', label: 'Status' },\n  ],\n  legal: [\n    { href: '/privacy', label: 'Privacy Policy' },\n    { href: '/terms', label: 'Terms of Service' },\n    { href: '/cookies', label: 'Cookie Policy' },\n    { href: '/gdpr', label: 'GDPR' },\n  ],\n};\n\nexport function Footer() {\n  return (\n    <footer className=\"border-t bg-background\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-5 gap-8\">\n          {/* Brand */}\n          <div className=\"md:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <Eye className=\"h-6 w-6 text-primary\" />\n              <span className=\"text-lg font-bold\">VirtualRealTour</span>\n            </Link>\n            <p className=\"text-sm text-muted-foreground mb-4\">\n              Premium 360° virtual tour platform designed for the Nigerian market.\n            </p>\n            <p className=\"text-xs text-muted-foreground\">\n              Made with ❤️ in Nigeria\n            </p>\n          </div>\n\n          {/* Product */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Resources */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Resources</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-sm text-muted-foreground\">\n            &copy; 2024 VirtualRealTour. All rights reserved.\n          </p>\n          <div className=\"flex items-center space-x-4 mt-4 md:mt-0\">\n            <Link\n              href=\"/privacy\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Privacy\n            </Link>\n            <Link\n              href=\"/terms\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Terms\n            </Link>\n            <Link\n              href=\"/cookies\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Cookies\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAiB,OAAO;QAAe;KAChD;IACD,SAAS;QACP;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAS,OAAO;QAAO;KAChC;IACD,WAAW;QACT;YAAE,MAAM;YAAS,OAAO;QAAc;QACtC;YAAE,MAAM;YAAS,OAAO;QAAgB;QACxC;YAAE,MAAM;YAAQ,OAAO;QAAgB;QACvC;YAAE,MAAM;YAAW,OAAO;QAAS;KACpC;IACD,OAAO;QACL;YAAE,MAAM;YAAY,OAAO;QAAiB;QAC5C;YAAE,MAAM;YAAU,OAAO;QAAmB;QAC5C;YAAE,MAAM;YAAY,OAAO;QAAgB;QAC3C;YAAE,MAAM;YAAS,OAAO;QAAO;KAChC;AACH;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAM/C,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAa1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/public-layout.tsx"], "sourcesContent": ["import { Header } from './header';\nimport { Footer } from './footer';\n\ninterface PublicLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function PublicLayout({ children }: PublicLayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header variant=\"public\" />\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;gBAAC,SAAQ;;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/app/contact/page.tsx"], "sourcesContent": ["import { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { PublicLayout } from \"@/components/layout/public-layout\";\nimport { Mail, Phone, MapPin, MessageCircle, Clock, Users } from \"lucide-react\";\n\nexport const metadata: Metadata = {\n  title: \"Contact Us\",\n  description: \"Get in touch with VirtualRealTour. We're here to help with your virtual tour needs.\",\n};\n\nconst contactMethods = [\n  {\n    icon: Mail,\n    title: \"Email Support\",\n    description: \"Get help via email\",\n    contact: \"<EMAIL>\",\n    availability: \"24/7 response within 2 hours\",\n  },\n  {\n    icon: Phone,\n    title: \"Phone Support\",\n    description: \"Speak with our team\",\n    contact: \"+234 (0) ************\",\n    availability: \"Mon-Fri, 9AM-6PM WAT\",\n  },\n  {\n    icon: MessageCircle,\n    title: \"WhatsApp\",\n    description: \"Chat with us instantly\",\n    contact: \"+234 (0) ************\",\n    availability: \"Mon-Fri, 9AM-6PM WAT\",\n  },\n];\n\nconst offices = [\n  {\n    city: \"Lagos\",\n    address: \"123 Victoria Island, Lagos State, Nigeria\",\n    phone: \"+234 (0) ************\",\n    email: \"<EMAIL>\",\n  },\n  {\n    city: \"Abuja\",\n    address: \"456 Central Business District, Abuja, FCT, Nigeria\",\n    phone: \"+234 (0) ************\",\n    email: \"<EMAIL>\",\n  },\n];\n\nexport default function ContactPage() {\n  return (\n    <PublicLayout>\n      <div className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          {/* Header */}\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">\n              Get in Touch\n            </h1>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              Have questions about VirtualRealTour? We're here to help you create \n              amazing virtual experiences for your business.\n            </p>\n          </div>\n\n          <div className=\"grid lg:grid-cols-3 gap-12\">\n            {/* Contact Form */}\n            <div className=\"lg:col-span-2\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Send us a Message</CardTitle>\n                  <CardDescription>\n                    Fill out the form below and we'll get back to you as soon as possible.\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <form className=\"space-y-6\">\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      <div className=\"space-y-2\">\n                        <Label htmlFor=\"firstName\">First Name</Label>\n                        <Input id=\"firstName\" placeholder=\"Enter your first name\" />\n                      </div>\n                      <div className=\"space-y-2\">\n                        <Label htmlFor=\"lastName\">Last Name</Label>\n                        <Input id=\"lastName\" placeholder=\"Enter your last name\" />\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"email\">Email</Label>\n                      <Input id=\"email\" type=\"email\" placeholder=\"Enter your email\" />\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"phone\">Phone Number</Label>\n                      <Input id=\"phone\" type=\"tel\" placeholder=\"Enter your phone number\" />\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"company\">Company (Optional)</Label>\n                      <Input id=\"company\" placeholder=\"Enter your company name\" />\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"subject\">Subject</Label>\n                      <Input id=\"subject\" placeholder=\"What can we help you with?\" />\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"message\">Message</Label>\n                      <Textarea \n                        id=\"message\" \n                        placeholder=\"Tell us more about your needs...\"\n                        rows={6}\n                      />\n                    </div>\n\n                    <Button type=\"submit\" className=\"w-full\">\n                      Send Message\n                    </Button>\n                  </form>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Contact Information */}\n            <div className=\"space-y-6\">\n              {/* Contact Methods */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center\">\n                    <Users className=\"h-5 w-5 mr-2\" />\n                    Contact Methods\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  {contactMethods.map((method) => (\n                    <div key={method.title} className=\"flex items-start space-x-3\">\n                      <method.icon className=\"h-5 w-5 text-primary mt-1\" />\n                      <div>\n                        <h3 className=\"font-medium\">{method.title}</h3>\n                        <p className=\"text-sm text-muted-foreground\">{method.description}</p>\n                        <p className=\"text-sm font-medium\">{method.contact}</p>\n                        <p className=\"text-xs text-muted-foreground\">{method.availability}</p>\n                      </div>\n                    </div>\n                  ))}\n                </CardContent>\n              </Card>\n\n              {/* Office Locations */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center\">\n                    <MapPin className=\"h-5 w-5 mr-2\" />\n                    Our Offices\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  {offices.map((office) => (\n                    <div key={office.city}>\n                      <h3 className=\"font-medium\">{office.city}</h3>\n                      <p className=\"text-sm text-muted-foreground\">{office.address}</p>\n                      <p className=\"text-sm\">{office.phone}</p>\n                      <p className=\"text-sm\">{office.email}</p>\n                    </div>\n                  ))}\n                </CardContent>\n              </Card>\n\n              {/* Business Hours */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center\">\n                    <Clock className=\"h-5 w-5 mr-2\" />\n                    Business Hours\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span>Monday - Friday</span>\n                      <span>9:00 AM - 6:00 PM</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Saturday</span>\n                      <span>10:00 AM - 4:00 PM</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Sunday</span>\n                      <span>Closed</span>\n                    </div>\n                    <div className=\"pt-2 border-t\">\n                      <p className=\"text-muted-foreground\">\n                        All times are West Africa Time (WAT)\n                      </p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n\n          {/* FAQ Section */}\n          <div className=\"mt-20\">\n            <h2 className=\"text-3xl font-bold text-center mb-12\">\n              Frequently Asked Questions\n            </h2>\n            <div className=\"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">How quickly can I get started?</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-muted-foreground\">\n                    You can start creating virtual tours immediately after signing up. \n                    Our platform is designed for quick onboarding with guided tutorials.\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Do you offer training?</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-muted-foreground\">\n                    Yes! We provide comprehensive training materials, video tutorials, \n                    and one-on-one onboarding sessions for Pro and Enterprise customers.\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">What equipment do I need?</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-muted-foreground\">\n                    You can start with any 360° camera or even a smartphone. We support \n                    all major 360° camera brands and provide equipment recommendations.\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Can you help with custom development?</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-muted-foreground\">\n                    Absolutely! Our Enterprise plan includes custom development services \n                    to meet your specific business requirements.\n                  </p>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </PublicLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEA,MAAM,iBAAiB;IACrB;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,SAAS;QACT,cAAc;IAChB;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,SAAS;QACT,cAAc;IAChB;IACA;QACE,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,SAAS;QACT,cAAc;IAChB;CACD;AAED,MAAM,UAAU;IACd;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,gJAAA,CAAA,eAAY;kBACX,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAMjE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAY;;;;;;kFAC3B,8OAAC,iIAAA,CAAA,QAAK;wEAAC,IAAG;wEAAY,aAAY;;;;;;;;;;;;0EAEpC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAW;;;;;;kFAC1B,8OAAC,iIAAA,CAAA,QAAK;wEAAC,IAAG;wEAAW,aAAY;;;;;;;;;;;;;;;;;;kEAIrC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAQ,MAAK;gEAAQ,aAAY;;;;;;;;;;;;kEAG7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAQ,MAAK;gEAAM,aAAY;;;;;;;;;;;;kEAG3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAU,aAAY;;;;;;;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAU,aAAY;;;;;;;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,aAAY;gEACZ,MAAM;;;;;;;;;;;;kEAIV,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,WAAU;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASjD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAItC,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACpB,eAAe,GAAG,CAAC,CAAC,uBACnB,8OAAC;wDAAuB,WAAU;;0EAChC,8OAAC,OAAO,IAAI;gEAAC,WAAU;;;;;;0EACvB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAe,OAAO,KAAK;;;;;;kFACzC,8OAAC;wEAAE,WAAU;kFAAiC,OAAO,WAAW;;;;;;kFAChE,8OAAC;wEAAE,WAAU;kFAAuB,OAAO,OAAO;;;;;;kFAClD,8OAAC;wEAAE,WAAU;kFAAiC,OAAO,YAAY;;;;;;;;;;;;;uDAN3D,OAAO,KAAK;;;;;;;;;;;;;;;;kDAc5B,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIvC,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACpB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAe,OAAO,IAAI;;;;;;0EACxC,8OAAC;gEAAE,WAAU;0EAAiC,OAAO,OAAO;;;;;;0EAC5D,8OAAC;gEAAE,WAAU;0EAAW,OAAO,KAAK;;;;;;0EACpC,8OAAC;gEAAE,WAAU;0EAAW,OAAO,KAAK;;;;;;;uDAJ5B,OAAO,IAAI;;;;;;;;;;;;;;;;kDAW3B,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAItC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAGrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAOzC,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAOzC,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAOzC,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvD", "debugId": null}}]}