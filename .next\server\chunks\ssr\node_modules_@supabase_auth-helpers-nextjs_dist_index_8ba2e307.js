module.exports = {

"[project]/node_modules/@supabase/auth-helpers-nextjs/dist/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@supabase_node-fetch_lib_index_e3a40958.js",
  "server/chunks/ssr/node_modules_52c775bf._.js",
  "server/chunks/ssr/[root-of-the-server]__a3765a21._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@supabase/auth-helpers-nextjs/dist/index.js [app-ssr] (ecmascript)");
    });
});
}}),

};