{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Eye } from 'lucide-react';\n\nconst footerLinks = {\n  product: [\n    { href: '/tours', label: 'Tours' },\n    { href: '/pricing', label: 'Pricing' },\n    { href: '/features', label: 'Features' },\n    { href: '/integrations', label: 'Integrations' },\n  ],\n  company: [\n    { href: '/about', label: 'About' },\n    { href: '/contact', label: 'Contact' },\n    { href: '/careers', label: 'Careers' },\n    { href: '/blog', label: 'Blog' },\n  ],\n  resources: [\n    { href: '/help', label: 'Help Center' },\n    { href: '/docs', label: 'Documentation' },\n    { href: '/api', label: 'API Reference' },\n    { href: '/status', label: 'Status' },\n  ],\n  legal: [\n    { href: '/privacy', label: 'Privacy Policy' },\n    { href: '/terms', label: 'Terms of Service' },\n    { href: '/cookies', label: 'Cookie Policy' },\n    { href: '/gdpr', label: 'GDPR' },\n  ],\n};\n\nexport function Footer() {\n  return (\n    <footer className=\"border-t bg-background\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-5 gap-8\">\n          {/* Brand */}\n          <div className=\"md:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <Eye className=\"h-6 w-6 text-primary\" />\n              <span className=\"text-lg font-bold\">VirtualRealTour</span>\n            </Link>\n            <p className=\"text-sm text-muted-foreground mb-4\">\n              Premium 360° virtual tour platform designed for the Nigerian market.\n            </p>\n            <p className=\"text-xs text-muted-foreground\">\n              Made with ❤️ in Nigeria\n            </p>\n          </div>\n\n          {/* Product */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Resources */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Resources</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-sm text-muted-foreground\">\n            &copy; 2024 VirtualRealTour. All rights reserved.\n          </p>\n          <div className=\"flex items-center space-x-4 mt-4 md:mt-0\">\n            <Link\n              href=\"/privacy\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Privacy\n            </Link>\n            <Link\n              href=\"/terms\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Terms\n            </Link>\n            <Link\n              href=\"/cookies\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Cookies\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAiB,OAAO;QAAe;KAChD;IACD,SAAS;QACP;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAS,OAAO;QAAO;KAChC;IACD,WAAW;QACT;YAAE,MAAM;YAAS,OAAO;QAAc;QACtC;YAAE,MAAM;YAAS,OAAO;QAAgB;QACxC;YAAE,MAAM;YAAQ,OAAO;QAAgB;QACvC;YAAE,MAAM;YAAW,OAAO;QAAS;KACpC;IACD,OAAO;QACL;YAAE,MAAM;YAAY,OAAO;QAAiB;QAC5C;YAAE,MAAM;YAAU,OAAO;QAAmB;QAC5C;YAAE,MAAM;YAAY,OAAO;QAAgB;QAC3C;YAAE,MAAM;YAAS,OAAO;QAAO;KAChC;AACH;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAM/C,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAa1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/public-layout.tsx"], "sourcesContent": ["import { Header } from './header';\nimport { Footer } from './footer';\n\ninterface PublicLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function PublicLayout({ children }: PublicLayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header variant=\"public\" />\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;gBAAC,SAAQ;;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/app/test-supabase/page.tsx"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr';\nimport { cookies } from 'next/headers';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { PublicLayout } from '@/components/layout/public-layout';\nimport { CheckCircle, XCircle, Database, Key, Globe } from 'lucide-react';\n\nexport default async function TestSupabasePage() {\n  let connectionStatus = 'disconnected';\n  let authStatus = 'unauthenticated';\n  let userCount = 0;\n  let error: string | null = null;\n\n  try {\n    // Test Supabase connection\n    const cookieStore = await cookies();\n    const supabase = createServerClient(\n      process.env.NEXT_PUBLIC_SUPABASE_URL!,\n      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n      {\n        cookies: {\n          getAll() {\n            return cookieStore.getAll();\n          },\n          setAll(cookiesToSet) {\n            try {\n              cookiesToSet.forEach(({ name, value, options }) =>\n                cookieStore.set(name, value, options)\n              );\n            } catch {\n              // The `setAll` method was called from a Server Component.\n              // This can be ignored if you have middleware refreshing\n              // user sessions.\n            }\n          },\n        },\n      }\n    );\n\n    // Test database connection\n    const { data, error: dbError } = await supabase\n      .from('users')\n      .select('count', { count: 'exact', head: true });\n\n    if (dbError) {\n      throw new Error(`Database error: ${dbError.message}`);\n    }\n\n    connectionStatus = 'connected';\n    userCount = data?.length || 0;\n\n    // Test authentication\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n\n    if (authError) {\n      console.warn('Auth check failed:', authError.message);\n    } else if (user) {\n      authStatus = 'authenticated';\n    }\n\n  } catch (err) {\n    error = err instanceof Error ? err.message : 'Unknown error';\n    console.error('Supabase test failed:', err);\n  }\n\n  return (\n    <PublicLayout>\n      <div className=\"py-20\">\n        <div className=\"container mx-auto px-4 max-w-4xl\">\n          {/* Header */}\n          <div className=\"text-center mb-12\">\n            <h1 className=\"text-4xl font-bold mb-4\">Supabase Connection Test</h1>\n            <p className=\"text-muted-foreground\">\n              Testing the new Supabase SSR integration for VirtualRealTour platform\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {/* Connection Status */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Database className=\"h-5 w-5 mr-2\" />\n                  Database Connection\n                </CardTitle>\n                <CardDescription>\n                  Testing connection to Supabase database\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex items-center space-x-2 mb-4\">\n                  {connectionStatus === 'connected' ? (\n                    <>\n                      <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                      <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\n                        Connected\n                      </Badge>\n                    </>\n                  ) : (\n                    <>\n                      <XCircle className=\"h-5 w-5 text-red-500\" />\n                      <Badge variant=\"destructive\">Disconnected</Badge>\n                    </>\n                  )}\n                </div>\n\n                {connectionStatus === 'connected' && (\n                  <div className=\"space-y-2 text-sm\">\n                    <p><strong>Status:</strong> Successfully connected to database</p>\n                    <p><strong>Users table:</strong> Accessible</p>\n                    <p><strong>User count:</strong> {userCount}</p>\n                  </div>\n                )}\n\n                {error && (\n                  <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n                    <p className=\"text-red-800 text-sm font-medium\">Error:</p>\n                    <p className=\"text-red-700 text-sm\">{error}</p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Authentication Status */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Key className=\"h-5 w-5 mr-2\" />\n                  Authentication\n                </CardTitle>\n                <CardDescription>\n                  Testing Supabase Auth integration\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex items-center space-x-2 mb-4\">\n                  {authStatus === 'authenticated' ? (\n                    <>\n                      <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                      <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\n                        Authenticated\n                      </Badge>\n                    </>\n                  ) : (\n                    <>\n                      <XCircle className=\"h-5 w-5 text-orange-500\" />\n                      <Badge variant=\"secondary\">Not Authenticated</Badge>\n                    </>\n                  )}\n                </div>\n\n                <div className=\"space-y-2 text-sm\">\n                  <p><strong>Status:</strong> {authStatus === 'authenticated' ? 'User is signed in' : 'No user session found'}</p>\n                  <p><strong>Auth system:</strong> {connectionStatus === 'connected' ? 'Available' : 'Unavailable'}</p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Environment Status */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Globe className=\"h-5 w-5 mr-2\" />\n                  Environment\n                </CardTitle>\n                <CardDescription>\n                  Environment configuration status\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm\">Supabase URL:</span>\n                    {process.env.NEXT_PUBLIC_SUPABASE_URL ? (\n                      <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\n                        Configured\n                      </Badge>\n                    ) : (\n                      <Badge variant=\"destructive\">Missing</Badge>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm\">Anon Key:</span>\n                    {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? (\n                      <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\n                        Configured\n                      </Badge>\n                    ) : (\n                      <Badge variant=\"destructive\">Missing</Badge>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm\">Service Role:</span>\n                    {process.env.SUPABASE_SERVICE_ROLE_KEY ? (\n                      <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\n                        Configured\n                      </Badge>\n                    ) : (\n                      <Badge variant=\"secondary\">Optional</Badge>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* SSR Integration */}\n            <Card>\n              <CardHeader>\n                <CardTitle>SSR Integration</CardTitle>\n                <CardDescription>\n                  Server-Side Rendering with Supabase\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                    <span className=\"text-sm\">Server-side client working</span>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                    <span className=\"text-sm\">Cookie-based auth ready</span>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                    <span className=\"text-sm\">Middleware integration active</span>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                    <span className=\"text-sm\">API routes configured</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Next Steps */}\n          <Card className=\"mt-8\">\n            <CardHeader>\n              <CardTitle>Next Steps</CardTitle>\n              <CardDescription>\n                Your Supabase SSR integration is ready for development\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div>\n                  <h4 className=\"font-medium mb-2\">✅ Completed Setup:</h4>\n                  <ul className=\"text-sm text-muted-foreground space-y-1 ml-4\">\n                    <li>• Supabase SSR client configuration</li>\n                    <li>• Environment variables configured</li>\n                    <li>• Server and client components ready</li>\n                    <li>• Middleware authentication flow</li>\n                    <li>• API routes with SSR support</li>\n                  </ul>\n                </div>\n\n                <div>\n                  <h4 className=\"font-medium mb-2\">🚀 Ready for Development:</h4>\n                  <ul className=\"text-sm text-muted-foreground space-y-1 ml-4\">\n                    <li>• Database schema implementation</li>\n                    <li>• User authentication flows</li>\n                    <li>• Tour management features</li>\n                    <li>• File upload and storage</li>\n                    <li>• Real-time subscriptions</li>\n                  </ul>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </PublicLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAEe,eAAe;IAC5B,IAAI,mBAAmB;IACvB,IAAI,aAAa;IACjB,IAAI,YAAY;IAChB,IAAI,QAAuB;IAE3B,IAAI;QACF,2BAA2B;QAC3B,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,MAAM,WAAW,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGhC;YACE,SAAS;gBACP;oBACE,OAAO,YAAY,MAAM;gBAC3B;gBACA,QAAO,YAAY;oBACjB,IAAI;wBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;oBAEjC,EAAE,OAAM;oBACN,0DAA0D;oBAC1D,wDAAwD;oBACxD,iBAAiB;oBACnB;gBACF;YACF;QACF;QAGF,2BAA2B;QAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,SACL,MAAM,CAAC,SAAS;YAAE,OAAO;YAAS,MAAM;QAAK;QAEhD,IAAI,SAAS;YACX,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ,OAAO,EAAE;QACtD;QAEA,mBAAmB;QACnB,YAAY,MAAM,UAAU;QAE5B,sBAAsB;QACtB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,WAAW;YACb,QAAQ,IAAI,CAAC,sBAAsB,UAAU,OAAO;QACtD,OAAO,IAAI,MAAM;YACf,aAAa;QACf;IAEF,EAAE,OAAO,KAAK;QACZ,QAAQ,eAAe,QAAQ,IAAI,OAAO,GAAG;QAC7C,QAAQ,KAAK,CAAC,yBAAyB;IACzC;IAEA,qBACE,8OAAC,gJAAA,CAAA,eAAY;kBACX,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACZ,qBAAqB,4BACpB;;sEACE,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAA8B;;;;;;;iFAKnE;;sEACE,8OAAC,4MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAc;;;;;;;;;;;;;4CAKlC,qBAAqB,6BACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAgB;;;;;;;kEAC3B,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAqB;;;;;;;kEAChC,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAoB;4DAAE;;;;;;;;;;;;;4CAIpC,uBACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAO7C,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACZ,eAAe,gCACd;;sEACE,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAA8B;;;;;;;iFAKnE;;sEACE,8OAAC,4MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAY;;;;;;;;;;;;;0DAKjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAgB;4DAAE,eAAe,kBAAkB,sBAAsB;;;;;;;kEACpF,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAqB;4DAAE,qBAAqB,cAAc,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzF,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAU;;;;;;wDACzB,qDACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAA8B;;;;;;;;;;;;8DAQrE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAU;;;;;;wDACzB,qDACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAA8B;;;;;;;;;;;;8DAQrE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAU;;;;;;wDACzB,QAAQ,GAAG,CAAC,yBAAyB,iBACpC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAA8B;;;;;iFAIjE,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQrC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAG5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAG5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAG5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;sDAIR,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB", "debugId": null}}]}