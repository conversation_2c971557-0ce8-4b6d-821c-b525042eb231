"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[663],{381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1071:(e,t,n)=>{n.d(t,{H_:()=>rF,UC:()=>rD,YJ:()=>rN,q7:()=>rI,VF:()=>rW,JU:()=>rO,ZL:()=>rP,z6:()=>r_,hN:()=>rK,bL:()=>rj,wv:()=>rH,Pb:()=>rG,G5:()=>rV,ZP:()=>rB,l9:()=>rL});var r,o,i=n(2115),a=n.t(i,2),l=n(5185),u=n(6101),c=n(6081),s=n(5845),d=n(3655),f=n(7328),p=n(5155),h=i.createContext(void 0);function m(e){let t=i.useContext(h);return e||t||"ltr"}var v=n(9178),g=0;function y(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var w=n(9033),x="focusScope.autoFocusOnMount",b="focusScope.autoFocusOnUnmount",E={bubbles:!1,cancelable:!0},R=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...l}=e,[c,s]=i.useState(null),f=(0,w.c)(o),h=(0,w.c)(a),m=i.useRef(null),v=(0,u.s)(t,e=>s(e)),g=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(g.paused||!c)return;let t=e.target;c.contains(t)?m.current=t:S(m.current,{select:!0})},t=function(e){if(g.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||S(m.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&S(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,g.paused]),i.useEffect(()=>{if(c){M.add(g);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(x,E);c.addEventListener(x,f),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(S(r,{select:t}),document.activeElement!==n)return}(C(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&S(c))}return()=>{c.removeEventListener(x,f),setTimeout(()=>{let t=new CustomEvent(b,E);c.addEventListener(b,h),c.dispatchEvent(t),t.defaultPrevented||S(null!=e?e:document.body,{select:!0}),c.removeEventListener(b,h),M.remove(g)},0)}}},[c,f,h,g]);let y=i.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=C(e);return[A(t,e),A(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&S(i,{select:!0})):(e.preventDefault(),n&&S(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,p.jsx)(d.sG.div,{tabIndex:-1,...l,ref:v,onKeyDown:y})});function C(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function A(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function S(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}R.displayName="FocusScope";var M=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=k(e,t)).unshift(t)},remove(t){var n;null==(n=(e=k(e,t))[0])||n.resume()}}}();function k(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var T=n(2712),j=a[" useId ".trim().toString()]||(()=>void 0),L=0;function P(e){let[t,n]=i.useState(j());return(0,T.N)(()=>{e||n(e=>e??String(L++))},[e]),e||(t?`radix-${t}`:"")}let D=["top","right","bottom","left"],N=Math.min,O=Math.max,I=Math.round,F=Math.floor,_=e=>({x:e,y:e}),K={left:"right",right:"left",bottom:"top",top:"bottom"},W={start:"end",end:"start"};function H(e,t){return"function"==typeof e?e(t):e}function G(e){return e.split("-")[0]}function B(e){return e.split("-")[1]}function V(e){return"x"===e?"y":"x"}function z(e){return"y"===e?"height":"width"}function U(e){return["top","bottom"].includes(G(e))?"y":"x"}function X(e){return e.replace(/start|end/g,e=>W[e])}function Y(e){return e.replace(/left|right|bottom|top/g,e=>K[e])}function q(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function Z(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function $(e,t,n){let r,{reference:o,floating:i}=e,a=U(t),l=V(U(t)),u=z(l),c=G(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(B(t)){case"start":r[l]-=p*(n&&s?-1:1);break;case"end":r[l]+=p*(n&&s?-1:1)}return r}let J=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=$(c,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=$(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function Q(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=H(t,e),h=q(p),m=l[f?"floating"===d?"reference":"floating":d],v=Z(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=Z(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function ee(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function et(e){return D.some(t=>e[t]>=0)}async function en(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=G(n),l=B(n),u="y"===U(n),c=["left","top"].includes(a)?-1:1,s=i&&u?-1:1,d=H(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function er(){return"undefined"!=typeof window}function eo(e){return el(e)?(e.nodeName||"").toLowerCase():"#document"}function ei(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ea(e){var t;return null==(t=(el(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function el(e){return!!er()&&(e instanceof Node||e instanceof ei(e).Node)}function eu(e){return!!er()&&(e instanceof Element||e instanceof ei(e).Element)}function ec(e){return!!er()&&(e instanceof HTMLElement||e instanceof ei(e).HTMLElement)}function es(e){return!!er()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ei(e).ShadowRoot)}function ed(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ev(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ef(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function ep(e){let t=eh(),n=eu(e)?ev(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function eh(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function em(e){return["html","body","#document"].includes(eo(e))}function ev(e){return ei(e).getComputedStyle(e)}function eg(e){return eu(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ey(e){if("html"===eo(e))return e;let t=e.assignedSlot||e.parentNode||es(e)&&e.host||ea(e);return es(t)?t.host:t}function ew(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ey(t);return em(n)?t.ownerDocument?t.ownerDocument.body:t.body:ec(n)&&ed(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=ei(o);if(i){let e=ex(a);return t.concat(a,a.visualViewport||[],ed(o)?o:[],e&&n?ew(e):[])}return t.concat(o,ew(o,[],n))}function ex(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eb(e){let t=ev(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=ec(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=I(n)!==i||I(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function eE(e){return eu(e)?e:e.contextElement}function eR(e){let t=eE(e);if(!ec(t))return _(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eb(t),a=(i?I(n.width):n.width)/r,l=(i?I(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let eC=_(0);function eA(e){let t=ei(e);return eh()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eC}function eS(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=eE(e),l=_(1);t&&(r?eu(r)&&(l=eR(r)):l=eR(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ei(a))&&o)?eA(a):_(0),c=(i.left+u.x)/l.x,s=(i.top+u.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=ei(a),t=r&&eu(r)?ei(r):r,n=e,o=ex(n);for(;o&&r&&t!==n;){let e=eR(o),t=o.getBoundingClientRect(),r=ev(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=a,o=ex(n=ei(o))}}return Z({width:d,height:f,x:c,y:s})}function eM(e,t){let n=eg(e).scrollLeft;return t?t.left+n:eS(ea(e)).left+n}function ek(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eM(e,r)),y:r.top+t.scrollTop}}function eT(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ei(e),r=ea(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=eh();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=ea(e),n=eg(e),r=e.ownerDocument.body,o=O(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=O(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+eM(e),l=-n.scrollTop;return"rtl"===ev(r).direction&&(a+=O(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(ea(e));else if(eu(t))r=function(e,t){let n=eS(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ec(e)?eR(e):_(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=eA(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return Z(r)}function ej(e){return"static"===ev(e).position}function eL(e,t){if(!ec(e)||"fixed"===ev(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ea(e)===n&&(n=n.ownerDocument.body),n}function eP(e,t){let n=ei(e);if(ef(e))return n;if(!ec(e)){let t=ey(e);for(;t&&!em(t);){if(eu(t)&&!ej(t))return t;t=ey(t)}return n}let r=eL(e,t);for(;r&&["table","td","th"].includes(eo(r))&&ej(r);)r=eL(r,t);return r&&em(r)&&ej(r)&&!ep(r)?n:r||function(e){let t=ey(e);for(;ec(t)&&!em(t);){if(ep(t))return t;if(ef(t))break;t=ey(t)}return null}(e)||n}let eD=async function(e){let t=this.getOffsetParent||eP,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=ec(t),o=ea(t),i="fixed"===n,a=eS(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=_(0);if(r||!r&&!i)if(("body"!==eo(t)||ed(o))&&(l=eg(t)),r){let e=eS(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eM(o));i&&!r&&o&&(u.x=eM(o));let c=!o||r||i?_(0):ek(o,l);return{x:a.left+l.scrollLeft-u.x-c.x,y:a.top+l.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eN={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=ea(r),l=!!t&&ef(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},c=_(1),s=_(0),d=ec(r);if((d||!d&&!i)&&(("body"!==eo(r)||ed(a))&&(u=eg(r)),ec(r))){let e=eS(r);c=eR(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!a||d||i?_(0):ek(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:ea,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ef(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=ew(e,[],!1).filter(e=>eu(e)&&"body"!==eo(e)),o=null,i="fixed"===ev(e).position,a=i?ey(e):e;for(;eu(a)&&!em(a);){let t=ev(a),n=ep(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||ed(a)&&!n&&function e(t,n){let r=ey(t);return!(r===n||!eu(r)||em(r))&&("fixed"===ev(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=ey(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eT(t,n,o);return e.top=O(r.top,e.top),e.right=N(r.right,e.right),e.bottom=N(r.bottom,e.bottom),e.left=O(r.left,e.left),e},eT(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eP,getElementRects:eD,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eb(e);return{width:t,height:n}},getScale:eR,isElement:eu,isRTL:function(e){return"rtl"===ev(e).direction}};function eO(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eI=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:u}=t,{element:c,padding:s=0}=H(e,t)||{};if(null==c)return{};let d=q(s),f={x:n,y:r},p=V(U(o)),h=z(p),m=await a.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],x=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),b=x?x[g]:0;b&&await (null==a.isElement?void 0:a.isElement(x))||(b=l.floating[g]||i.floating[h]);let E=b/2-m[h]/2-1,R=N(d[v?"top":"left"],E),C=N(d[v?"bottom":"right"],E),A=b-m[h]-C,S=b/2-m[h]/2+(y/2-w/2),M=O(R,N(S,A)),k=!u.arrow&&null!=B(o)&&S!==M&&i.reference[h]/2-(S<R?R:C)-m[h]/2<0,T=k?S<R?S-R:S-A:0;return{[p]:f[p]+T,data:{[p]:M,centerOffset:S-M-T,...k&&{alignmentOffset:T}},reset:k}}}),eF=(e,t,n)=>{let r=new Map,o={platform:eN,...n},i={...o.platform,_c:r};return J(e,t,{...o,platform:i})};var e_=n(7650),eK="undefined"!=typeof document?i.useLayoutEffect:function(){};function eW(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eW(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eW(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eH(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eG(e,t){let n=eH(e);return Math.round(t*n)/n}function eB(e){let t=i.useRef(e);return eK(()=>{t.current=e}),t}let eV=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eI({element:n.current,padding:r}).fn(t):{}:n?eI({element:n,padding:r}).fn(t):{}}}),ez=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await en(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eU=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=H(e,t),c={x:n,y:r},s=await Q(t,u),d=U(G(o)),f=V(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=O(n,N(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=O(n,N(h,r))}let m=l.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}),eX=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=H(e,t),s={x:n,y:r},d=U(o),f=V(d),p=s[f],h=s[d],m=H(l,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(G(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),eY=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=H(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=G(l),b=U(s),E=G(s)===s,R=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=m||(E||!y?[Y(s)]:function(e){let t=Y(e);return[X(e),t,X(t)]}(s)),A="none"!==g;!m&&A&&C.push(...function(e,t,n,r){let o=B(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(G(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(X)))),i}(s,y,g,R));let S=[s,...C],M=await Q(t,w),k=[],T=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&k.push(M[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=B(e),o=V(U(e)),i=z(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Y(a)),[a,Y(a)]}(l,c,R);k.push(M[e[0]],M[e[1]])}if(T=[...T,{placement:l,overflows:k}],!k.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=S[e];if(t&&("alignment"!==h||b===U(t)||T.every(e=>e.overflows[0]>0&&U(e.placement)===b)))return{data:{index:e,overflows:T},reset:{placement:t}};let n=null==(i=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=T.filter(e=>{if(A){let t=U(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eq=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:a,rects:l,platform:u,elements:c}=t,{apply:s=()=>{},...d}=H(e,t),f=await Q(t,d),p=G(a),h=B(a),m="y"===U(a),{width:v,height:g}=l.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=v-f.left-f.right,x=N(g-f[o],y),b=N(v-f[i],w),E=!t.middlewareData.shift,R=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=y),E&&!h){let e=O(f.left,0),t=O(f.right,0),n=O(f.top,0),r=O(f.bottom,0);m?C=v-2*(0!==e||0!==t?e+t:O(f.left,f.right)):R=g-2*(0!==n||0!==r?n+r:O(f.top,f.bottom))}await s({...t,availableWidth:C,availableHeight:R});let A=await u.getDimensions(c.floating);return v!==A.width||g!==A.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eZ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=H(e,t);switch(r){case"referenceHidden":{let e=ee(await Q(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:et(e)}}}case"escaped":{let e=ee(await Q(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:et(e)}}}default:return{}}}}}(e),options:[e,t]}),e$=(e,t)=>({...eV(e),options:[e,t]});var eJ=i.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,p.jsx)(d.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,p.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eJ.displayName="Arrow";var eQ="Popper",[e0,e1]=(0,c.A)(eQ),[e2,e5]=e0(eQ),e3=e=>{let{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return(0,p.jsx)(e2,{scope:t,anchor:r,onAnchorChange:o,children:n})};e3.displayName=eQ;var e7="PopperAnchor",e4=i.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,a=e5(e7,n),l=i.useRef(null),c=(0,u.s)(t,l);return i.useEffect(()=>{a.onAnchorChange((null==r?void 0:r.current)||l.current)}),r?null:(0,p.jsx)(d.sG.div,{...o,ref:c})});e4.displayName=e7;var e9="PopperContent",[e6,e8]=e0(e9),te=i.forwardRef((e,t)=>{var n,r,o,a,l,c,s,f;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:x=0,avoidCollisions:b=!0,collisionBoundary:E=[],collisionPadding:R=0,sticky:C="partial",hideWhenDetached:A=!1,updatePositionStrategy:S="optimized",onPlaced:M,...k}=e,j=e5(e9,h),[L,P]=i.useState(null),D=(0,u.s)(t,e=>P(e)),[I,_]=i.useState(null),K=function(e){let[t,n]=i.useState(void 0);return(0,T.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(I),W=null!=(s=null==K?void 0:K.width)?s:0,H=null!=(f=null==K?void 0:K.height)?f:0,G="number"==typeof R?R:{top:0,right:0,bottom:0,left:0,...R},B=Array.isArray(E)?E:[E],V=B.length>0,z={padding:G,boundary:B.filter(to),altBoundary:V},{refs:U,floatingStyles:X,placement:Y,isPositioned:q,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=i.useState(r);eW(p,r)||h(r);let[m,v]=i.useState(null),[g,y]=i.useState(null),w=i.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),x=i.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),b=a||m,E=l||g,R=i.useRef(null),C=i.useRef(null),A=i.useRef(d),S=null!=c,M=eB(c),k=eB(o),T=eB(s),j=i.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};k.current&&(e.platform=k.current),eF(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};L.current&&!eW(A.current,t)&&(A.current=t,e_.flushSync(()=>{f(t)}))})},[p,t,n,k,T]);eK(()=>{!1===s&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let L=i.useRef(!1);eK(()=>(L.current=!0,()=>{L.current=!1}),[]),eK(()=>{if(b&&(R.current=b),E&&(C.current=E),b&&E){if(M.current)return M.current(b,E,j);j()}},[b,E,j,M,S]);let P=i.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:x}),[w,x]),D=i.useMemo(()=>({reference:b,floating:E}),[b,E]),N=i.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=eG(D.floating,d.x),r=eG(D.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eH(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,d.x,d.y]);return i.useMemo(()=>({...d,update:j,refs:P,elements:D,floatingStyles:N}),[d,j,P,D,N])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eE(e),d=i||a?[...s?ew(s):[],...ew(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=ea(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,u){void 0===l&&(l=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(l||t(),!f||!p)return;let h=F(d),m=F(o.clientWidth-(s+f)),v={rootMargin:-h+"px "+-m+"px "+-F(o.clientHeight-(d+p))+"px "+-F(s)+"px",threshold:O(0,N(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eO(c,e.getBoundingClientRect())||a(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?eS(e):null;return c&&function t(){let r=eS(e);m&&!eO(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===S})},elements:{reference:j.anchor},middleware:[ez({mainAxis:v+H,alignmentAxis:y}),b&&eU({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?eX():void 0,...z}),b&&eY({...z}),eq({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),I&&e$({element:I,padding:x}),ti({arrowWidth:W,arrowHeight:H}),A&&eZ({strategy:"referenceHidden",...z})]}),[$,J]=ta(Y),Q=(0,w.c)(M);(0,T.N)(()=>{q&&(null==Q||Q())},[q,Q]);let ee=null==(n=Z.arrow)?void 0:n.x,et=null==(r=Z.arrow)?void 0:r.y,en=(null==(o=Z.arrow)?void 0:o.centerOffset)!==0,[er,eo]=i.useState();return(0,T.N)(()=>{L&&eo(window.getComputedStyle(L).zIndex)},[L]),(0,p.jsx)("div",{ref:U.setFloating,"data-radix-popper-content-wrapper":"",style:{...X,transform:q?X.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null==(a=Z.transformOrigin)?void 0:a.x,null==(l=Z.transformOrigin)?void 0:l.y].join(" "),...(null==(c=Z.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,p.jsx)(e6,{scope:h,placedSide:$,onArrowChange:_,arrowX:ee,arrowY:et,shouldHideArrow:en,children:(0,p.jsx)(d.sG.div,{"data-side":$,"data-align":J,...k,ref:D,style:{...k.style,animation:q?void 0:"none"}})})})});te.displayName=e9;var tt="PopperArrow",tn={top:"bottom",right:"left",bottom:"top",left:"right"},tr=i.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e8(tt,n),i=tn[o.placedSide];return(0,p.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,p.jsx)(eJ,{...r,ref:t,style:{...r.style,display:"block"}})})});function to(e){return null!==e}tr.displayName=tt;var ti=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=ta(l),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(a=null==(o=c.arrow)?void 0:o.y)?a:0)+f/2,y="",w="";return"bottom"===p?(y=s?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(y=s?m:"".concat(v,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function ta(e){let[t,n="center"]=e.split("-");return[t,n]}var tl=n(4378),tu=n(8905),tc="rovingFocusGroup.onEntryFocus",ts={bubbles:!1,cancelable:!0},td="RovingFocusGroup",[tf,tp,th]=(0,f.N)(td),[tm,tv]=(0,c.A)(td,[th]),[tg,ty]=tm(td),tw=i.forwardRef((e,t)=>(0,p.jsx)(tf.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(tf.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(tx,{...e,ref:t})})}));tw.displayName=td;var tx=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:a,currentTabStopId:c,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:h,onEntryFocus:v,preventScrollOnEntryFocus:g=!1,...y}=e,x=i.useRef(null),b=(0,u.s)(t,x),E=m(a),[R,C]=(0,s.i)({prop:c,defaultProp:null!=f?f:null,onChange:h,caller:td}),[A,S]=i.useState(!1),M=(0,w.c)(v),k=tp(n),T=i.useRef(!1),[j,L]=i.useState(0);return i.useEffect(()=>{let e=x.current;if(e)return e.addEventListener(tc,M),()=>e.removeEventListener(tc,M)},[M]),(0,p.jsx)(tg,{scope:n,orientation:r,dir:E,loop:o,currentTabStopId:R,onItemFocus:i.useCallback(e=>C(e),[C]),onItemShiftTab:i.useCallback(()=>S(!0),[]),onFocusableItemAdd:i.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:i.useCallback(()=>L(e=>e-1),[]),children:(0,p.jsx)(d.sG.div,{tabIndex:A||0===j?-1:0,"data-orientation":r,...y,ref:b,style:{outline:"none",...e.style},onMouseDown:(0,l.m)(e.onMouseDown,()=>{T.current=!0}),onFocus:(0,l.m)(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(tc,ts);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);tC([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),g)}}T.current=!1}),onBlur:(0,l.m)(e.onBlur,()=>S(!1))})})}),tb="RovingFocusGroupItem",tE=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:a,children:u,...c}=e,s=P(),f=a||s,h=ty(tb,n),m=h.currentTabStopId===f,v=tp(n),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:w}=h;return i.useEffect(()=>{if(r)return g(),()=>y()},[r,g,y]),(0,p.jsx)(tf.ItemSlot,{scope:n,id:f,focusable:r,active:o,children:(0,p.jsx)(d.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...c,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{r?h.onItemFocus(f):e.preventDefault()}),onFocus:(0,l.m)(e.onFocus,()=>h.onItemFocus(f)),onKeyDown:(0,l.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tR[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>tC(n))}}),children:"function"==typeof u?u({isCurrentTabStop:m,hasTabStop:null!=w}):u})})});tE.displayName=tb;var tR={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tC(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var tA=n(9708),tS=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tM=new WeakMap,tk=new WeakMap,tT={},tj=0,tL=function(e){return e&&(e.host||tL(e.parentNode))},tP=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tL(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tT[n]||(tT[n]=new WeakMap);var i=tT[n],a=[],l=new Set,u=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tM.get(e)||0)+1,c=(i.get(e)||0)+1;tM.set(e,u),i.set(e,c),a.push(e),1===u&&o&&tk.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),l.clear(),tj++,function(){a.forEach(function(e){var t=tM.get(e)-1,o=i.get(e)-1;tM.set(e,t),i.set(e,o),t||(tk.has(e)||e.removeAttribute(r),tk.delete(e)),o||e.removeAttribute(n)}),--tj||(tM=new WeakMap,tM=new WeakMap,tk=new WeakMap,tT={})}},tD=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||tS(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tP(r,o,n,"aria-hidden")):function(){return null}},tN=function(){return(tN=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tO(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tI=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tF="width-before-scroll-bar";function t_(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tK="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,tW=new WeakMap;function tH(e){return e}var tG=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=tH),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return i.options=tN({async:!0,ssr:!1},e),i}(),tB=function(){},tV=i.forwardRef(function(e,t){var n,r,o,a,l=i.useRef(null),u=i.useState({onScrollCapture:tB,onWheelCapture:tB,onTouchMoveCapture:tB}),c=u[0],s=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,x=e.inert,b=e.allowPinchZoom,E=e.as,R=e.gapMode,C=tO(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),A=(n=[l,t],r=function(e){return n.forEach(function(t){return t_(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,tK(function(){var e=tW.get(a);if(e){var t=new Set(e),r=new Set(n),o=a.current;t.forEach(function(e){r.has(e)||t_(e,null)}),r.forEach(function(e){t.has(e)||t_(e,o)})}tW.set(a,n)},[n]),a),S=tN(tN({},C),c);return i.createElement(i.Fragment,null,m&&i.createElement(g,{sideCar:tG,removeScrollBar:h,shards:v,noRelative:y,noIsolation:w,inert:x,setCallbacks:s,allowPinchZoom:!!b,lockRef:l,gapMode:R}),d?i.cloneElement(i.Children.only(f),tN(tN({},S),{ref:A})):i.createElement(void 0===E?"div":E,tN({},S,{className:p,ref:A}),f))});tV.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tV.classNames={fullWidth:tF,zeroRight:tI};var tz=function(e){var t=e.sideCar,n=tO(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,tN({},n))};tz.isSideCarExport=!0;var tU=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tX=function(){var e=tU();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tY=function(){var e=tX();return function(t){return e(t.styles,t.dynamic),null}},tq={left:0,top:0,right:0,gap:0},tZ=function(e){return parseInt(e||"",10)||0},t$=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tZ(n),tZ(r),tZ(o)]},tJ=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tq;var t=t$(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tQ=tY(),t0="data-scroll-locked",t1=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(t0,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tI," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tF," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tI," .").concat(tI," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tF," .").concat(tF," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t0,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},t2=function(){var e=parseInt(document.body.getAttribute(t0)||"0",10);return isFinite(e)?e:0},t5=function(){i.useEffect(function(){return document.body.setAttribute(t0,(t2()+1).toString()),function(){var e=t2()-1;e<=0?document.body.removeAttribute(t0):document.body.setAttribute(t0,e.toString())}},[])},t3=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t5();var a=i.useMemo(function(){return tJ(o)},[o]);return i.createElement(tQ,{styles:t1(a,!t,o,n?"":"!important")})},t7=!1;if("undefined"!=typeof window)try{var t4=Object.defineProperty({},"passive",{get:function(){return t7=!0,!0}});window.addEventListener("test",t4,t4),window.removeEventListener("test",t4,t4)}catch(e){t7=!1}var t9=!!t7&&{passive:!1},t6=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},t8=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ne(e,r)){var o=nt(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ne=function(e,t){return"v"===e?t6(t,"overflowY"):t6(t,"overflowX")},nt=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nn=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var h=nt(e,u),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&ne(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},nr=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},no=function(e){return[e.deltaX,e.deltaY]},ni=function(e){return e&&"current"in e?e.current:e},na=0,nl=[];let nu=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(na++)[0],a=i.useState(tY)[0],l=i.useRef(e);i.useEffect(function(){l.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ni),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=nr(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=t8(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=t8(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nn(p,t,e,"h"===p?u:c,!0)},[]),c=i.useCallback(function(e){if(nl.length&&nl[nl.length-1]===a){var n="deltaY"in e?no(e):nr(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(ni).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=i.useCallback(function(e){n.current=nr(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,no(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,nr(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return nl.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,t9),document.addEventListener("touchmove",c,t9),document.addEventListener("touchstart",d,t9),function(){nl=nl.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,t9),document.removeEventListener("touchmove",c,t9),document.removeEventListener("touchstart",d,t9)}},[]);var h=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?i.createElement(t3,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tG.useMedium(r),tz);var nc=i.forwardRef(function(e,t){return i.createElement(tV,tN({},e,{ref:t,sideCar:nu}))});nc.classNames=tV.classNames;var ns=["Enter"," "],nd=["ArrowUp","PageDown","End"],nf=["ArrowDown","PageUp","Home",...nd],np={ltr:[...ns,"ArrowRight"],rtl:[...ns,"ArrowLeft"]},nh={ltr:["ArrowLeft"],rtl:["ArrowRight"]},nm="Menu",[nv,ng,ny]=(0,f.N)(nm),[nw,nx]=(0,c.A)(nm,[ny,e1,tv]),nb=e1(),nE=tv(),[nR,nC]=nw(nm),[nA,nS]=nw(nm),nM=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:a,modal:l=!0}=e,u=nb(t),[c,s]=i.useState(null),d=i.useRef(!1),f=(0,w.c)(a),h=m(o);return i.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,p.jsx)(e3,{...u,children:(0,p.jsx)(nR,{scope:t,open:n,onOpenChange:f,content:c,onContentChange:s,children:(0,p.jsx)(nA,{scope:t,onClose:i.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:d,dir:h,modal:l,children:r})})})};nM.displayName=nm;var nk=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nb(n);return(0,p.jsx)(e4,{...o,...r,ref:t})});nk.displayName="MenuAnchor";var nT="MenuPortal",[nj,nL]=nw(nT,{forceMount:void 0}),nP=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=nC(nT,t);return(0,p.jsx)(nj,{scope:t,forceMount:n,children:(0,p.jsx)(tu.C,{present:n||i.open,children:(0,p.jsx)(tl.Z,{asChild:!0,container:o,children:r})})})};nP.displayName=nT;var nD="MenuContent",[nN,nO]=nw(nD),nI=i.forwardRef((e,t)=>{let n=nL(nD,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nC(nD,e.__scopeMenu),a=nS(nD,e.__scopeMenu);return(0,p.jsx)(nv.Provider,{scope:e.__scopeMenu,children:(0,p.jsx)(tu.C,{present:r||i.open,children:(0,p.jsx)(nv.Slot,{scope:e.__scopeMenu,children:a.modal?(0,p.jsx)(nF,{...o,ref:t}):(0,p.jsx)(n_,{...o,ref:t})})})})}),nF=i.forwardRef((e,t)=>{let n=nC(nD,e.__scopeMenu),r=i.useRef(null),o=(0,u.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return tD(e)},[]),(0,p.jsx)(nW,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),n_=i.forwardRef((e,t)=>{let n=nC(nD,e.__scopeMenu);return(0,p.jsx)(nW,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),nK=(0,tA.TL)("MenuContent.ScrollLock"),nW=i.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:d,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:m,onInteractOutside:w,onDismiss:x,disableOutsideScroll:b,...E}=e,C=nC(nD,n),A=nS(nD,n),S=nb(n),M=nE(n),k=ng(n),[T,j]=i.useState(null),L=i.useRef(null),P=(0,u.s)(t,L,C.onContentChange),D=i.useRef(0),N=i.useRef(""),O=i.useRef(0),I=i.useRef(null),F=i.useRef("right"),_=i.useRef(0),K=b?nc:i.Fragment,W=e=>{var t,n;let r=N.current+e,o=k().filter(e=>!e.disabled),i=document.activeElement,a=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,a),u=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){N.current=t,window.clearTimeout(D.current),""!==t&&(D.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};i.useEffect(()=>()=>window.clearTimeout(D.current),[]),i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:y()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:y()),g++,()=>{1===g&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),g--}},[]);let H=i.useCallback(e=>{var t,n;return F.current===(null==(t=I.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=I.current)?void 0:n.area)},[]);return(0,p.jsx)(nN,{scope:n,searchRef:N,onItemEnter:i.useCallback(e=>{H(e)&&e.preventDefault()},[H]),onItemLeave:i.useCallback(e=>{var t;H(e)||(null==(t=L.current)||t.focus(),j(null))},[H]),onTriggerLeave:i.useCallback(e=>{H(e)&&e.preventDefault()},[H]),pointerGraceTimerRef:O,onPointerGraceIntentChange:i.useCallback(e=>{I.current=e},[]),children:(0,p.jsx)(K,{...b?{as:nK,allowPinchZoom:!0}:void 0,children:(0,p.jsx)(R,{asChild:!0,trapped:o,onMountAutoFocus:(0,l.m)(a,e=>{var t;e.preventDefault(),null==(t=L.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,p.jsx)(v.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:m,onInteractOutside:w,onDismiss:x,children:(0,p.jsx)(tw,{asChild:!0,...M,dir:A.dir,orientation:"vertical",loop:r,currentTabStopId:T,onCurrentTabStopIdChange:j,onEntryFocus:(0,l.m)(d,e=>{A.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,p.jsx)(te,{role:"menu","aria-orientation":"vertical","data-state":ro(C.open),"data-radix-menu-content":"",dir:A.dir,...S,...E,ref:P,style:{outline:"none",...E.style},onKeyDown:(0,l.m)(E.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&W(e.key));let o=L.current;if(e.target!==o||!nf.includes(e.key))return;e.preventDefault();let i=k().filter(e=>!e.disabled).map(e=>e.ref.current);nd.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,l.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(D.current),N.current="")}),onPointerMove:(0,l.m)(e.onPointerMove,rl(e=>{let t=e.target,n=_.current!==e.clientX;e.currentTarget.contains(t)&&n&&(F.current=e.clientX>_.current?"right":"left",_.current=e.clientX)}))})})})})})})});nI.displayName=nD;var nH=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(d.sG.div,{role:"group",...r,ref:t})});nH.displayName="MenuGroup";var nG=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(d.sG.div,{...r,ref:t})});nG.displayName="MenuLabel";var nB="MenuItem",nV="menu.itemSelect",nz=i.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,a=i.useRef(null),c=nS(nB,e.__scopeMenu),s=nO(nB,e.__scopeMenu),f=(0,u.s)(t,a),h=i.useRef(!1);return(0,p.jsx)(nU,{...o,ref:f,disabled:n,onClick:(0,l.m)(e.onClick,()=>{let e=a.current;if(!n&&e){let t=new CustomEvent(nV,{bubbles:!0,cancelable:!0});e.addEventListener(nV,e=>null==r?void 0:r(e),{once:!0}),(0,d.hO)(e,t),t.defaultPrevented?h.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),h.current=!0},onPointerUp:(0,l.m)(e.onPointerUp,e=>{var t;h.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{let t=""!==s.searchRef.current;n||t&&" "===e.key||ns.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});nz.displayName=nB;var nU=i.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...a}=e,c=nO(nB,n),s=nE(n),f=i.useRef(null),h=(0,u.s)(t,f),[m,v]=i.useState(!1),[g,y]=i.useState("");return i.useEffect(()=>{let e=f.current;if(e){var t;y((null!=(t=e.textContent)?t:"").trim())}},[a.children]),(0,p.jsx)(nv.ItemSlot,{scope:n,disabled:r,textValue:null!=o?o:g,children:(0,p.jsx)(tE,{asChild:!0,...s,focusable:!r,children:(0,p.jsx)(d.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...a,ref:h,onPointerMove:(0,l.m)(e.onPointerMove,rl(e=>{r?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,l.m)(e.onPointerLeave,rl(e=>c.onItemLeave(e))),onFocus:(0,l.m)(e.onFocus,()=>v(!0)),onBlur:(0,l.m)(e.onBlur,()=>v(!1))})})})}),nX=i.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,p.jsx)(n1,{scope:e.__scopeMenu,checked:n,children:(0,p.jsx)(nz,{role:"menuitemcheckbox","aria-checked":ri(n)?"mixed":n,...o,ref:t,"data-state":ra(n),onSelect:(0,l.m)(o.onSelect,()=>null==r?void 0:r(!!ri(n)||!n),{checkForDefaultPrevented:!1})})})});nX.displayName="MenuCheckboxItem";var nY="MenuRadioGroup",[nq,nZ]=nw(nY,{value:void 0,onValueChange:()=>{}}),n$=i.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,w.c)(r);return(0,p.jsx)(nq,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,p.jsx)(nH,{...o,ref:t})})});n$.displayName=nY;var nJ="MenuRadioItem",nQ=i.forwardRef((e,t)=>{let{value:n,...r}=e,o=nZ(nJ,e.__scopeMenu),i=n===o.value;return(0,p.jsx)(n1,{scope:e.__scopeMenu,checked:i,children:(0,p.jsx)(nz,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":ra(i),onSelect:(0,l.m)(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});nQ.displayName=nJ;var n0="MenuItemIndicator",[n1,n2]=nw(n0,{checked:!1}),n5=i.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=n2(n0,n);return(0,p.jsx)(tu.C,{present:r||ri(i.checked)||!0===i.checked,children:(0,p.jsx)(d.sG.span,{...o,ref:t,"data-state":ra(i.checked)})})});n5.displayName=n0;var n3=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(d.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});n3.displayName="MenuSeparator";var n7=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nb(n);return(0,p.jsx)(tr,{...o,...r,ref:t})});n7.displayName="MenuArrow";var n4="MenuSub",[n9,n6]=nw(n4),n8=e=>{let{__scopeMenu:t,children:n,open:r=!1,onOpenChange:o}=e,a=nC(n4,t),l=nb(t),[u,c]=i.useState(null),[s,d]=i.useState(null),f=(0,w.c)(o);return i.useEffect(()=>(!1===a.open&&f(!1),()=>f(!1)),[a.open,f]),(0,p.jsx)(e3,{...l,children:(0,p.jsx)(nR,{scope:t,open:r,onOpenChange:f,content:s,onContentChange:d,children:(0,p.jsx)(n9,{scope:t,contentId:P(),triggerId:P(),trigger:u,onTriggerChange:c,children:n})})})};n8.displayName=n4;var re="MenuSubTrigger",rt=i.forwardRef((e,t)=>{let n=nC(re,e.__scopeMenu),r=nS(re,e.__scopeMenu),o=n6(re,e.__scopeMenu),a=nO(re,e.__scopeMenu),c=i.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=a,f={__scopeMenu:e.__scopeMenu},h=i.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return i.useEffect(()=>h,[h]),i.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,p.jsx)(nk,{asChild:!0,...f,children:(0,p.jsx)(nU,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":ro(n.open),...e,ref:(0,u.t)(t,o.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,l.m)(e.onPointerMove,rl(t=>{a.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||c.current||(a.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),h()},100)))})),onPointerLeave:(0,l.m)(e.onPointerLeave,rl(e=>{var t,r;h();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,i="right"===t,l=o[i?"left":"right"],u=o[i?"right":"left"];a.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(e),e.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:(0,l.m)(e.onKeyDown,t=>{let o=""!==a.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&np[r.dir].includes(t.key)){var i;n.onOpenChange(!0),null==(i=n.content)||i.focus(),t.preventDefault()}})})})});rt.displayName=re;var rn="MenuSubContent",rr=i.forwardRef((e,t)=>{let n=nL(nD,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=nC(nD,e.__scopeMenu),c=nS(nD,e.__scopeMenu),s=n6(rn,e.__scopeMenu),d=i.useRef(null),f=(0,u.s)(t,d);return(0,p.jsx)(nv.Provider,{scope:e.__scopeMenu,children:(0,p.jsx)(tu.C,{present:r||a.open,children:(0,p.jsx)(nv.Slot,{scope:e.__scopeMenu,children:(0,p.jsx)(nW,{id:s.contentId,"aria-labelledby":s.triggerId,...o,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:(0,l.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=nh[c.dir].includes(e.key);if(t&&n){var r;a.onOpenChange(!1),null==(r=s.trigger)||r.focus(),e.preventDefault()}})})})})})});function ro(e){return e?"open":"closed"}function ri(e){return"indeterminate"===e}function ra(e){return ri(e)?"indeterminate":e?"checked":"unchecked"}function rl(e){return t=>"mouse"===t.pointerType?e(t):void 0}rr.displayName=rn;var ru="DropdownMenu",[rc,rs]=(0,c.A)(ru,[nx]),rd=nx(),[rf,rp]=rc(ru),rh=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,c=rd(t),d=i.useRef(null),[f,h]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:ru});return(0,p.jsx)(rf,{scope:t,triggerId:P(),triggerRef:d,contentId:P(),open:f,onOpenChange:h,onOpenToggle:i.useCallback(()=>h(e=>!e),[h]),modal:u,children:(0,p.jsx)(nM,{...c,open:f,onOpenChange:h,dir:r,modal:u,children:n})})};rh.displayName=ru;var rm="DropdownMenuTrigger",rv=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=rp(rm,n),a=rd(n);return(0,p.jsx)(nk,{asChild:!0,...a,children:(0,p.jsx)(d.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,u.t)(t,i.triggerRef),onPointerDown:(0,l.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});rv.displayName=rm;var rg=e=>{let{__scopeDropdownMenu:t,...n}=e,r=rd(t);return(0,p.jsx)(nP,{...r,...n})};rg.displayName="DropdownMenuPortal";var ry="DropdownMenuContent",rw=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rp(ry,n),a=rd(n),u=i.useRef(!1);return(0,p.jsx)(nI,{id:o.contentId,"aria-labelledby":o.triggerId,...a,...r,ref:t,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=o.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,l.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rw.displayName=ry;var rx=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(nH,{...o,...r,ref:t})});rx.displayName="DropdownMenuGroup";var rb=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(nG,{...o,...r,ref:t})});rb.displayName="DropdownMenuLabel";var rE=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(nz,{...o,...r,ref:t})});rE.displayName="DropdownMenuItem";var rR=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(nX,{...o,...r,ref:t})});rR.displayName="DropdownMenuCheckboxItem";var rC=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(n$,{...o,...r,ref:t})});rC.displayName="DropdownMenuRadioGroup";var rA=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(nQ,{...o,...r,ref:t})});rA.displayName="DropdownMenuRadioItem";var rS=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(n5,{...o,...r,ref:t})});rS.displayName="DropdownMenuItemIndicator";var rM=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(n3,{...o,...r,ref:t})});rM.displayName="DropdownMenuSeparator",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(n7,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var rk=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(rt,{...o,...r,ref:t})});rk.displayName="DropdownMenuSubTrigger";var rT=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rd(n);return(0,p.jsx)(rr,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rT.displayName="DropdownMenuSubContent";var rj=rh,rL=rv,rP=rg,rD=rw,rN=rx,rO=rb,rI=rE,rF=rR,r_=rC,rK=rA,rW=rS,rH=rM,rG=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,a=rd(t),[l,u]=(0,s.i)({prop:r,defaultProp:null!=i&&i,onChange:o,caller:"DropdownMenuSub"});return(0,p.jsx)(n8,{...a,open:l,onOpenChange:u,children:n})},rB=rk,rV=rT},1414:(e,t,n)=>{e.exports=n(2436)},2436:(e,t,n)=>{var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,u=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return l(function(){o.value=n,o.getSnapshot=t,c(o)&&s({inst:o})},[e,n,t]),a(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:s},2657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4011:(e,t,n)=>{n.d(t,{Fallback:()=>C,Image:()=>R,Root:()=>E});var r=n(2115),o=n(6081),i=n(9033),a=n(2712),l=n(3655),u=n(1414);function c(){return()=>{}}var s=n(5155),d="Avatar",[f,p]=(0,o.A)(d),[h,m]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,s.jsx)(h,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,s.jsx)(l.sG.span,{...o,ref:t})})});v.displayName=d;var g="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(g,n),h=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,i=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),l=r.useRef(null),s=i?(l.current||(l.current=new window.Image),l.current):null,[d,f]=r.useState(()=>b(s,e));return(0,a.N)(()=>{f(b(s,e))},[s,e]),(0,a.N)(()=>{let e=e=>()=>{f(e)};if(!s)return;let t=e("loaded"),r=e("error");return s.addEventListener("load",t),s.addEventListener("error",r),n&&(s.referrerPolicy=n),"string"==typeof o&&(s.crossOrigin=o),()=>{s.removeEventListener("load",t),s.removeEventListener("error",r)}},[s,o,n]),d}(o,f),v=(0,i.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,s.jsx)(l.sG.img,{...f,ref:t,src:o}):null});y.displayName=g;var w="AvatarFallback",x=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=m(w,n),[u,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(l.sG.span,{...i,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=w;var E=v,R=y,C=x},4616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4835:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5695:(e,t,n)=>{var r=n(8999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}})},9428:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])}}]);