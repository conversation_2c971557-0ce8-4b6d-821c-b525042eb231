import { env } from '@/lib/env';
import type { APIResponse } from '@/types';

export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

interface RequestOptions extends RequestInit {
  timeout?: number;
}

class APIClient {
  private baseURL: string;
  private defaultTimeout: number = 30000; // 30 seconds

  constructor(baseURL?: string) {
    this.baseURL = baseURL || env.NEXT_PUBLIC_API_URL || '/api';
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<APIResponse<T>> {
    const { timeout = this.defaultTimeout, ...fetchOptions } = options;

    const url = `${this.baseURL}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...fetchOptions.headers,
        },
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (!response.ok) {
        throw new APIError(
          data.error?.message || 'Request failed',
          response.status,
          data.error?.code,
          data.error?.details
        );
      }

      return data;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof APIError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new APIError('Request timeout', 408);
        }
        throw new APIError(error.message, 0);
      }

      throw new APIError('Unknown error occurred', 0);
    }
  }

  async get<T = any>(endpoint: string, options?: RequestOptions): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T = any>(
    endpoint: string,
    data?: any,
    options?: RequestOptions
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T = any>(
    endpoint: string,
    data?: any,
    options?: RequestOptions
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T = any>(
    endpoint: string,
    data?: any,
    options?: RequestOptions
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T = any>(endpoint: string, options?: RequestOptions): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  // File upload method
  async upload<T = any>(
    endpoint: string,
    formData: FormData,
    options?: Omit<RequestOptions, 'body'> & {
      onProgress?: (progress: number) => void;
    }
  ): Promise<APIResponse<T>> {
    const { onProgress, ...fetchOptions } = options || {};

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        try {
          const data = JSON.parse(xhr.responseText);
          
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(data);
          } else {
            reject(new APIError(
              data.error?.message || 'Upload failed',
              xhr.status,
              data.error?.code,
              data.error?.details
            ));
          }
        } catch (error) {
          reject(new APIError('Invalid response format', xhr.status));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new APIError('Network error', 0));
      });

      xhr.addEventListener('timeout', () => {
        reject(new APIError('Upload timeout', 408));
      });

      const url = `${this.baseURL}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;
      xhr.open('POST', url);
      
      // Set timeout
      xhr.timeout = fetchOptions.timeout || this.defaultTimeout;

      // Set headers (except Content-Type for FormData)
      if (fetchOptions.headers) {
        Object.entries(fetchOptions.headers).forEach(([key, value]) => {
          if (key.toLowerCase() !== 'content-type') {
            xhr.setRequestHeader(key, value as string);
          }
        });
      }

      xhr.send(formData);
    });
  }
}

// Create default API client instance
export const apiClient = new APIClient();

// Export the class for custom instances
export { APIClient };
