(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)": (() => {{

throw new Error("An error occurred while generating the chunk item [project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)\n\nCaused by:\n- Expected visited target node to be module\n\nDebug info:\n- An error occurred while generating the chunk item [project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)\n- Execution of <AsyncLoaderChunkItem as EcmascriptChunkItem>::content failed\n- Execution of AsyncLoaderChunkItem::chunks_data failed\n- Execution of AsyncLoaderChunkItem::chunks failed\n- Execution of ModuleGraph::module_batches failed\n- Execution of ModuleGraph::chunk_group_info failed\n- Expected visited target node to be module");

}}),
}]);