# 🚀 Development Standards & AI Instructions

> **Comprehensive Development Guidelines for AI Agents & Human Developers**
> *Project-Agnostic Standards for Consistent, High-Quality Code Delivery*

---

## 📋 **Table of Contents**

1. [🎯 Core Principles](#-core-principles)
2. [🔍 Pre-Development Analysis](#-pre-development-analysis)
3. [📐 Planning & Architecture](#-planning--architecture)
4. [💻 Code Implementation Standards](#-code-implementation-standards)
5. [🔧 Package Management](#-package-management)
6. [🧪 Testing & Quality Assurance](#-testing--quality-assurance)
7. [🐛 Debugging & Problem Resolution](#-debugging--problem-resolution)
8. [📁 File & Directory Management](#-file--directory-management)
9. [🎨 UI/UX Standards](#-uiux-standards)
10. [🔐 Security & Authentication](#-security--authentication)
11. [📊 Performance & Optimization](#-performance--optimization)
12. [📝 Documentation Standards](#-documentation-standards)

---

## 🎯 **Core Principles**

### **Development Philosophy**
- **Incremental Excellence**: Build systematically, test continuously, iterate intelligently
- **Context-First Development**: Always understand before implementing
- **Conservative Enhancement**: Enhance existing systems rather than rebuilding
- **User-Centric Design**: Every decision serves the end user experience
- **Future-Proof Architecture**: Build for scalability and maintainability

### **Current Project Context**
*These standards are applied to the VirtualRealTour platform - a 360° virtual tour application built with Next.js, TypeScript, Tailwind CSS, and Supabase. The project is in active development focusing on core functionality before implementing advanced styling and branding.*

### **AI Agent Behavior Standards**
```markdown
✅ DO:
- Break complex tasks into smaller, manageable steps
- Gather comprehensive context before making changes
- Ask for clarification when requirements are ambiguous
- Provide detailed explanations for technical decisions
- Test changes incrementally and verify success

❌ DON'T:
- Make assumptions about project requirements
- Implement large changes without user approval
- Skip testing and verification steps
- Ignore existing code patterns and conventions
- Proceed without understanding the full context
```

---

## 🔍 **Pre-Development Analysis**

### **Context Gathering Protocol**
1. **Codebase Indexing**: Always start with comprehensive codebase analysis
2. **Architecture Understanding**: Identify existing patterns, frameworks, and conventions
3. **Dependency Mapping**: Understand package management and dependency relationships
4. **User Requirements**: Clarify scope, constraints, and success criteria
5. **Risk Assessment**: Identify potential breaking changes and mitigation strategies

### **Information Gathering Checklist**
```markdown
□ Project structure and architecture
□ Existing code patterns and conventions
□ Package management system in use
□ Testing framework and coverage
□ Build and deployment processes
□ Security requirements and constraints
□ Performance requirements
□ Browser/platform compatibility needs
```

---

## 📐 **Planning & Architecture**

### **Strategic Planning Process**
1. **Requirements Analysis**: Break down user requests into specific, actionable tasks
2. **Impact Assessment**: Identify all files, components, and systems affected
3. **Implementation Strategy**: Define step-by-step execution plan
4. **Risk Mitigation**: Plan for potential issues and rollback strategies
5. **Success Metrics**: Define clear criteria for completion

### **Architecture Decision Framework**
```markdown
🏗️ ARCHITECTURE PRIORITIES:
1. Maintainability > Cleverness
2. Consistency > Innovation
3. Security > Convenience
4. Performance > Features
5. User Experience > Developer Experience
```

---

## 💻 **Code Implementation Standards**

### **Code Quality Standards**
- **Consistency**: Follow existing project patterns and conventions
- **Readability**: Write self-documenting code with clear variable names
- **Modularity**: Create reusable, single-responsibility components
- **Error Handling**: Implement comprehensive error handling and validation
- **Type Safety**: Use TypeScript effectively with proper type definitions

### **Implementation Best Practices**
```typescript
// ✅ GOOD: Clear, typed, documented
interface PaymentGateway {
  id: string;
  name: string;
  isEnabled: boolean;
  processingFee: number;
}

const processPayment = async (
  gateway: PaymentGateway,
  amount: number,
  currency: string = 'NGN'
): Promise<PaymentResult> => {
  // Implementation with proper error handling
};

// ❌ BAD: Unclear, untyped, undocumented
const doPayment = (g: any, amt: any) => {
  // Implementation without proper structure
};
```

### **File Editing Protocol**
1. **Always use str-replace-editor**: Never recreate files from scratch
2. **Gather context first**: Use codebase-retrieval before making changes
3. **Make incremental changes**: Small, focused edits with clear purposes
4. **Verify changes**: Check syntax, imports, and functionality after edits
5. **Test immediately**: Run builds and tests after significant changes

---

## 🔧 **Package Management**

### **Dependency Management Rules**
```markdown
🚫 NEVER manually edit package files (package.json, requirements.txt, etc.)
✅ ALWAYS use appropriate package managers:

JavaScript/Node.js:
- npm install/uninstall
- yarn add/remove
- pnpm add/remove

Python:
- pip install/uninstall
- poetry add/remove
- conda install/remove

Other Languages:
- cargo add/remove (Rust)
- go get/mod tidy (Go)
- composer require/remove (PHP)
- dotnet add/remove package (.NET)
```

### **Dependency Strategy**
- **Minimize Dependencies**: Only add what's truly necessary
- **Version Pinning**: Use specific versions for critical dependencies
- **Security Auditing**: Regularly check for vulnerabilities
- **License Compliance**: Ensure all dependencies have compatible licenses

---

## 🧪 **Testing & Quality Assurance**

### **Testing Hierarchy**
1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test component interactions
3. **End-to-End Tests**: Test complete user workflows
4. **Performance Tests**: Validate speed and resource usage
5. **Security Tests**: Verify authentication and authorization

### **Quality Gates**
```markdown
🎯 MINIMUM REQUIREMENTS:
- Build passes without errors
- All tests pass
- Code coverage > 70%
- No critical security vulnerabilities
- Performance benchmarks met
- Accessibility standards compliance
```

---

## 🐛 **Debugging & Problem Resolution**

### **Systematic Debugging Process**
1. **Reproduce the Issue**: Create minimal reproduction case
2. **Isolate the Problem**: Identify the specific component or function
3. **Analyze the Context**: Check logs, error messages, and stack traces
4. **Form Hypothesis**: Develop theories about the root cause
5. **Test Solutions**: Implement fixes incrementally
6. **Verify Resolution**: Ensure the fix doesn't introduce new issues

### **Common Issue Patterns & Solutions**
```markdown
🔧 IMPORT/EXPORT ISSUES:
- Check file paths and extensions
- Verify named vs default exports
- Ensure proper TypeScript configurations

🔧 BUILD FAILURES:
- Resolve dependency conflicts
- Fix TypeScript type errors
- Address missing environment variables

🔧 RUNTIME ERRORS:
- Check async/await patterns
- Validate API responses
- Ensure proper error boundaries

🔧 STYLING ISSUES:
- Verify CSS class names and imports
- Check responsive design breakpoints
- Validate accessibility attributes
```

---

## 📁 **File & Directory Management**

### **Organization Standards**
```
project-root/
├── src/                    # Source code
│   ├── components/         # Reusable UI components
│   ├── pages/             # Page components
│   ├── hooks/             # Custom React hooks
│   ├── utils/             # Utility functions
│   ├── types/             # TypeScript type definitions
│   └── styles/            # Styling files
├── public/                # Static assets
│   ├── images/            # Image assets
│   └── icons/             # Icon files
├── tests/                 # Test files
├── docs/                  # Documentation
└── config/                # Configuration files
```

### **Naming Conventions**
- **Files**: kebab-case for files, PascalCase for components
- **Variables**: camelCase for variables and functions
- **Constants**: UPPER_SNAKE_CASE for constants
- **Types**: PascalCase for interfaces and types
- **CSS Classes**: kebab-case or BEM methodology

---

## 🎨 **UI/UX Standards**

### **Design Principles**
- **Consistency**: Maintain uniform design patterns
- **Accessibility**: WCAG 2.1 AA compliance minimum
- **Responsiveness**: Mobile-first design approach
- **Performance**: Optimize for fast loading and smooth interactions
- **User Feedback**: Clear loading states and error messages

### **Component Standards**
```tsx
// ✅ GOOD: Accessible, typed, documented component
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
  onClick?: () => void;
  'aria-label'?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  disabled = false,
  onClick,
  'aria-label': ariaLabel,
}) => {
  return (
    <button
      className={`btn btn-${variant}`}
      disabled={disabled}
      onClick={onClick}
      aria-label={ariaLabel}
    >
      {children}
    </button>
  );
};
```

---

## 🔐 **Security & Authentication**

### **Security Checklist**
```markdown
□ Input validation and sanitization
□ SQL injection prevention
□ XSS protection
□ CSRF token implementation
□ Secure session management
□ Environment variable protection
□ API rate limiting
□ Proper error handling (no sensitive data exposure)
```

### **Authentication Standards**
- **JWT Tokens**: Secure token generation and validation
- **Session Management**: Proper session lifecycle management
- **Role-Based Access**: Implement granular permission systems
- **Password Security**: Strong hashing and validation requirements

---

## 📊 **Performance & Optimization**

### **Performance Targets**
```markdown
🎯 PERFORMANCE GOALS:
- First Contentful Paint: < 1.5s
- Largest Contentful Paint: < 2.5s
- Cumulative Layout Shift: < 0.1
- First Input Delay: < 100ms
- Bundle Size: Minimize and optimize
```

### **Optimization Strategies**
- **Code Splitting**: Lazy load components and routes
- **Image Optimization**: Use appropriate formats and sizes
- **Caching**: Implement effective caching strategies
- **Minification**: Compress CSS, JS, and HTML
- **CDN Usage**: Leverage content delivery networks

---

## 🔍 **Diagnostic & Analysis Protocol**

### **Pre-Implementation Diagnostics**
```markdown
🔍 DIAGNOSTIC CHECKLIST:
□ Run IDE diagnostics to identify existing issues
□ Check build status and resolve any errors
□ Verify all imports and dependencies
□ Validate TypeScript configurations
□ Review test coverage and failing tests
□ Analyze performance bottlenecks
□ Check for security vulnerabilities
□ Validate accessibility compliance
```

### **Systematic Issue Resolution**
1. **Categorize Issues**: Group by type (syntax, logic, performance, security)
2. **Prioritize Fixes**: Critical errors → Warnings → Optimizations
3. **Fix Incrementally**: Address one category at a time
4. **Verify Each Fix**: Test after each resolution
5. **Document Solutions**: Record fixes for future reference

### **Common Diagnostic Patterns**
```typescript
// ✅ DIAGNOSTIC COMMAND EXAMPLES:
// TypeScript errors
npx tsc --noEmit

// ESLint issues
npx eslint . --ext .ts,.tsx,.js,.jsx

// Build verification
npm run build

// Test coverage
npm run test -- --coverage

// Security audit
npm audit

// Performance analysis
npm run analyze
```

---

## 🚀 **Advanced Development Practices**

### **Git Workflow Standards**
```markdown
🌿 BRANCH NAMING:
- feature/description-of-feature
- bugfix/description-of-bug
- hotfix/critical-issue-fix
- refactor/component-or-system-name

📝 COMMIT MESSAGES:
- feat: add new payment gateway integration
- fix: resolve authentication token expiration
- refactor: optimize database query performance
- docs: update API documentation
- test: add unit tests for user service
```

### **Code Review Checklist**
```markdown
□ Code follows project conventions
□ All tests pass and coverage maintained
□ No security vulnerabilities introduced
□ Performance impact assessed
□ Documentation updated
□ Accessibility requirements met
□ Error handling implemented
□ Edge cases considered
```

### **Deployment Readiness**
```markdown
🚀 PRE-DEPLOYMENT CHECKLIST:
□ All tests passing
□ Build successful
□ Environment variables configured
□ Database migrations ready
□ Backup procedures in place
□ Rollback plan prepared
□ Monitoring and logging configured
□ Performance benchmarks met
```

---

## 📝 **Documentation Standards**

### **Documentation Requirements**
1. **README Files**: Clear setup and usage instructions
2. **API Documentation**: Comprehensive endpoint documentation
3. **Code Comments**: Explain complex logic and business rules
4. **Architecture Docs**: High-level system design documentation
5. **Deployment Guides**: Step-by-step deployment instructions

### **Documentation Format**
```markdown
# Component/Function Name

## Purpose
Brief description of what this does

## Usage
```typescript
// Example usage code
```

## Parameters
- `param1` (type): Description
- `param2` (type): Description

## Returns
Description of return value

## Examples
Practical usage examples
```

---

## 🎨 **Visual Design Standards**

### **Color System Guidelines**
```css
/* Neutral Colors (Project Agnostic) */
--neutral-50: #f9fafb;
--neutral-100: #f3f4f6;
--neutral-200: #e5e7eb;
--neutral-500: #6b7280;
--neutral-900: #111827;

/* Status Colors (Universal) */
--success: #10b981;
--warning: #f59e0b;
--error: #ef4444;
--info: #3b82f6;

/* Note: Project-specific brand colors should be defined in project theme files */
```

### **Typography Standards**
```css
/* Font Hierarchy */
h1 { font-size: 2.5rem; font-weight: 700; }
h2 { font-size: 2rem; font-weight: 600; }
h3 { font-size: 1.5rem; font-weight: 600; }
body { font-size: 1rem; line-height: 1.6; }
small { font-size: 0.875rem; }
```

### **Spacing System**
```css
/* Consistent Spacing Scale */
--space-xs: 0.25rem;   /* 4px */
--space-sm: 0.5rem;    /* 8px */
--space-md: 1rem;      /* 16px */
--space-lg: 1.5rem;    /* 24px */
--space-xl: 2rem;      /* 32px */
--space-2xl: 3rem;     /* 48px */
```

---

## 🤖 **AI Agent Specific Instructions**

### **Context Management**
```markdown
🧠 AI BEHAVIOR PROTOCOLS:
1. Always index codebase before making changes
2. Use codebase-retrieval for comprehensive context
3. Break complex tasks into smaller steps
4. Explain reasoning behind technical decisions
5. Ask for clarification when requirements are unclear
6. Verify changes with builds and tests
7. Provide progress updates for long operations
```

### **Communication Standards**
- **Be Explicit**: Clearly state what you're doing and why
- **Show Progress**: Break down multi-step processes
- **Explain Decisions**: Justify technical choices
- **Ask Questions**: Clarify ambiguous requirements
- **Provide Options**: Present alternatives when applicable

### **Error Recovery Protocol**
```markdown
🔄 WHEN STUCK:
1. Stop and analyze the current situation
2. Identify what information is missing
3. Ask the user for clarification or guidance
4. Suggest alternative approaches
5. Break the problem into smaller parts
6. Document lessons learned for future reference
```

---

## 🛠️ **Troubleshooting Guide**

### **Common Issues & Solutions**

#### **Build Failures**
```bash
# TypeScript errors
npx tsc --noEmit --skipLibCheck

# Dependency conflicts
rm -rf node_modules package-lock.json
npm install

# Environment issues
cp .env.example .env.local
# Configure required variables
```

#### **Import/Export Issues**
```typescript
// ✅ CORRECT: Named exports
export const MyComponent = () => { ... };
import { MyComponent } from './MyComponent';

// ✅ CORRECT: Default exports
export default MyComponent;
import MyComponent from './MyComponent';

// ❌ INCORRECT: Mixed patterns
export default const MyComponent = () => { ... };
```

#### **Styling Problems**
```css
/* ✅ CORRECT: Consistent naming */
.btn-primary { ... }
.btn-secondary { ... }

/* ✅ CORRECT: Responsive design */
@media (min-width: 768px) {
  .container { max-width: 1200px; }
}

/* ❌ INCORRECT: Inconsistent patterns */
.btnPrimary { ... }
.button_secondary { ... }
```

#### **Performance Issues**
```typescript
// ✅ CORRECT: Lazy loading
const LazyComponent = lazy(() => import('./HeavyComponent'));

// ✅ CORRECT: Memoization
const MemoizedComponent = memo(({ data }) => {
  return <ExpensiveComponent data={data} />;
});

// ✅ CORRECT: Efficient state updates
const [state, setState] = useState(initialState);
const updateState = useCallback((newValue) => {
  setState(prev => ({ ...prev, value: newValue }));
}, []);
```

---

## 📊 **Quality Metrics & KPIs**

### **Code Quality Metrics**
```markdown
📈 TARGET METRICS:
- Code Coverage: > 80%
- TypeScript Strict Mode: Enabled
- ESLint Errors: 0
- Security Vulnerabilities: 0
- Performance Score: > 90
- Accessibility Score: > 95
- Bundle Size: Optimized
```

### **Development Velocity**
- **Feature Delivery**: Consistent sprint completion
- **Bug Resolution**: < 24 hours for critical issues
- **Code Review**: < 2 hours response time
- **Deployment**: Automated and reliable
- **Documentation**: Up-to-date and comprehensive

---

## 🎯 **Success Metrics**

### **Project Completion Criteria**
- ✅ All requirements implemented and tested
- ✅ Build passes without errors or warnings
- ✅ Test coverage meets minimum thresholds
- ✅ Performance benchmarks achieved
- ✅ Security audit passed
- ✅ Documentation complete and accurate
- ✅ User acceptance testing completed

### **Long-term Success Indicators**
- **Maintainability**: Easy to modify and extend
- **Scalability**: Handles increased load gracefully
- **Developer Experience**: Efficient development workflow
- **User Satisfaction**: Positive user feedback and adoption
- **Business Value**: Meets business objectives and ROI

---

## 📚 **Learning & Improvement**

### **Continuous Learning Protocol**
1. **Post-Project Reviews**: Analyze what worked and what didn't
2. **Technology Updates**: Stay current with framework updates
3. **Best Practice Evolution**: Adapt standards based on experience
4. **Team Knowledge Sharing**: Document and share learnings
5. **Industry Trends**: Monitor and evaluate new technologies

### **Knowledge Base Maintenance**
- **Regular Updates**: Review and update standards quarterly
- **Feedback Integration**: Incorporate team and user feedback
- **Version Control**: Track changes and rationale
- **Training Materials**: Create and maintain training resources

---

## 📞 **Support & Contact**

**Development Team**
📧 Email: [Contact information to be added per project]
🌐 Website: [Project website to be added]
📱 Support: [Support channels to be defined per project]

---

## 📄 **Document Information**

*This document is a living standard that evolves with our development practices and lessons learned.*

**Version**: 1.0
**Last Updated**: 2025-01-27
**Next Review**: 2025-04-27
**Document Type**: Development Standards & AI Instructions
**Scope**: Project-Agnostic Guidelines
**Audience**: AI Agents, Developers, Project Managers
