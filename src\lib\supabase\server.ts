import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { createClient as createBrowserClient } from '@supabase/supabase-js';
import { createClient as createSSRServerClient } from '@/utils/supabase/server';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';

// Server-side Supabase client for use in Server Components (legacy)
export function createServerSupabaseClient() {
  const cookieStore = cookies();
  return createServerComponentClient<Database>({ cookies: () => cookieStore });
}

// New SSR-compatible server client (recommended)
export const createClient = createSSRServerClient;

// Service role client for admin operations
export function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !serviceRoleKey) {
    throw new Error('Missing Supabase service role environment variables');
  }

  return createBrowserClient<Database>(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}

// Server-side auth helpers
export const serverAuth = {
  /**
   * Get user from server component
   */
  async getUser() {
    const supabase = createServerSupabaseClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      console.error('Error getting user:', error);
      return null;
    }

    return user;
  },

  /**
   * Get session from server component
   */
  async getSession() {
    const supabase = createServerSupabaseClient();
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Error getting session:', error);
      return null;
    }

    return session;
  },

  /**
   * Require authentication (throws if not authenticated)
   */
  async requireAuth() {
    const user = await this.getUser();

    if (!user) {
      throw new Error('Authentication required');
    }

    return user;
  },

  /**
   * Check if user has specific role
   */
  async hasRole(role: string) {
    const user = await this.getUser();

    if (!user) return false;

    // Check user metadata for role
    return user.user_metadata?.role === role ||
      user.app_metadata?.role === role;
  },

  /**
   * Require specific role (throws if not authorized)
   */
  async requireRole(role: string) {
    const user = await this.requireAuth();
    const hasRequiredRole = await this.hasRole(role);

    if (!hasRequiredRole) {
      throw new Error(`Role '${role}' required`);
    }

    return user;
  },
};

// Server-side database helpers
export const serverDb = {
  /**
   * Get user profile with error handling
   */
  async getUserProfile(userId: string) {
    const supabase = createServerSupabaseClient();

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error getting user profile:', error);
      return null;
    }

    return data;
  },

  /**
   * Get tours with pagination
   */
  async getTours(options: {
    page?: number;
    limit?: number;
    userId?: string;
    status?: string;
    featured?: boolean;
  } = {}) {
    const supabase = createServerSupabaseClient();
    const { page = 1, limit = 10, userId, status, featured } = options;

    let query = supabase
      .from('tours')
      .select(`
        *,
        users:user_id (
          id,
          full_name,
          avatar_url
        )
      `)
      .order('created_at', { ascending: false });

    if (userId) {
      query = query.eq('user_id', userId);
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (featured !== undefined) {
      query = query.eq('featured', featured);
    }

    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error getting tours:', error);
      return { tours: [], total: 0 };
    }

    return {
      tours: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit),
    };
  },

  /**
   * Get tour by slug with scenes and hotspots
   */
  async getTourBySlug(slug: string) {
    const supabase = createServerSupabaseClient();

    const { data, error } = await supabase
      .from('tours')
      .select(`
        *,
        users:user_id (
          id,
          full_name,
          avatar_url
        ),
        scenes (
          *,
          media:media_id (
            id,
            file_path,
            media_type,
            thumbnail_url
          ),
          hotspots (
            *
          )
        )
      `)
      .eq('slug', slug)
      .eq('status', 'published')
      .single();

    if (error) {
      console.error('Error getting tour by slug:', error);
      return null;
    }

    return data;
  },

  /**
   * Get tour analytics
   */
  async getTourAnalytics(tourId: string, dateRange?: {
    startDate: Date;
    endDate: Date;
  }) {
    const supabase = createServerSupabaseClient();

    let query = supabase
      .from('analytics')
      .select('*')
      .eq('tour_id', tourId);

    if (dateRange) {
      query = query
        .gte('timestamp', dateRange.startDate.toISOString())
        .lte('timestamp', dateRange.endDate.toISOString());
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error getting tour analytics:', error);
      return [];
    }

    return data || [];
  },

  /**
   * Increment tour view count
   */
  async incrementTourViews(tourId: string) {
    const supabase = createServiceRoleClient();

    const { error } = await supabase.rpc('increment_tour_views', {
      tour_id: tourId,
    });

    if (error) {
      console.error('Error incrementing tour views:', error);
    }
  },

  /**
   * Get user's subscription
   */
  async getUserSubscription(userId: string) {
    const supabase = createServerSupabaseClient();

    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error getting user subscription:', error);
      return null;
    }

    return data;
  },

  /**
   * Check if user can create more tours
   */
  async canCreateTour(userId: string) {
    const supabase = createServerSupabaseClient();

    // Get user's subscription
    const subscription = await this.getUserSubscription(userId);

    // Get current tour count
    const { count } = await supabase
      .from('tours')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    const currentTourCount = count || 0;

    // Check limits based on subscription tier
    const limits = {
      free: 3,
      pro: 50,
      enterprise: Infinity,
    };

    const tier = subscription?.plan_name?.toLowerCase() || 'free';
    const limit = limits[tier as keyof typeof limits] || limits.free;

    return currentTourCount < limit;
  },
};

// Server-side storage helpers
export const serverStorage = {
  /**
   * Upload file with service role permissions
   */
  async uploadFile(
    bucket: string,
    path: string,
    file: File | Buffer,
    options?: {
      contentType?: string;
      cacheControl?: string;
      upsert?: boolean;
    }
  ) {
    const supabase = createServiceRoleClient();

    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        contentType: options?.contentType,
        cacheControl: options?.cacheControl || '3600',
        upsert: options?.upsert || false,
      });

    if (error) {
      throw new Error(`Upload failed: ${error.message}`);
    }

    return data;
  },

  /**
   * Delete file with service role permissions
   */
  async deleteFile(bucket: string, path: string) {
    const supabase = createServiceRoleClient();

    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) {
      throw new Error(`Delete failed: ${error.message}`);
    }
  },

  /**
   * Get file metadata
   */
  async getFileMetadata(bucket: string, path: string) {
    const supabase = createServiceRoleClient();

    const { data, error } = await supabase.storage
      .from(bucket)
      .list(path.split('/').slice(0, -1).join('/'), {
        search: path.split('/').pop(),
      });

    if (error) {
      throw new Error(`Failed to get file metadata: ${error.message}`);
    }

    return data?.[0] || null;
  },
};
