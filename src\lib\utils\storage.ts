/**
 * Local storage utilities with error handling and type safety
 */

/**
 * Set item in localStorage with JSON serialization
 */
export function setLocalStorage<T>(key: string, value: T): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    const serializedValue = JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
    return true;
  } catch (error) {
    console.error('Error setting localStorage:', error);
    return false;
  }
}

/**
 * Get item from localStorage with JSON deserialization
 */
export function getLocalStorage<T>(key: string, defaultValue?: T): T | null {
  try {
    if (typeof window === 'undefined') return defaultValue || null;
    
    const item = localStorage.getItem(key);
    if (item === null) return defaultValue || null;
    
    return JSON.parse(item) as T;
  } catch (error) {
    console.error('Error getting localStorage:', error);
    return defaultValue || null;
  }
}

/**
 * Remove item from localStorage
 */
export function removeLocalStorage(key: string): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Error removing localStorage:', error);
    return false;
  }
}

/**
 * Clear all localStorage
 */
export function clearLocalStorage(): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    localStorage.clear();
    return true;
  } catch (error) {
    console.error('Error clearing localStorage:', error);
    return false;
  }
}

/**
 * Check if localStorage is available
 */
export function isLocalStorageAvailable(): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    const test = '__localStorage_test__';
    localStorage.setItem(test, 'test');
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

/**
 * Set item in sessionStorage with JSON serialization
 */
export function setSessionStorage<T>(key: string, value: T): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    const serializedValue = JSON.stringify(value);
    sessionStorage.setItem(key, serializedValue);
    return true;
  } catch (error) {
    console.error('Error setting sessionStorage:', error);
    return false;
  }
}

/**
 * Get item from sessionStorage with JSON deserialization
 */
export function getSessionStorage<T>(key: string, defaultValue?: T): T | null {
  try {
    if (typeof window === 'undefined') return defaultValue || null;
    
    const item = sessionStorage.getItem(key);
    if (item === null) return defaultValue || null;
    
    return JSON.parse(item) as T;
  } catch (error) {
    console.error('Error getting sessionStorage:', error);
    return defaultValue || null;
  }
}

/**
 * Remove item from sessionStorage
 */
export function removeSessionStorage(key: string): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    sessionStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Error removing sessionStorage:', error);
    return false;
  }
}

/**
 * Clear all sessionStorage
 */
export function clearSessionStorage(): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    sessionStorage.clear();
    return true;
  } catch (error) {
    console.error('Error clearing sessionStorage:', error);
    return false;
  }
}

/**
 * Check if sessionStorage is available
 */
export function isSessionStorageAvailable(): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    const test = '__sessionStorage_test__';
    sessionStorage.setItem(test, 'test');
    sessionStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get localStorage usage information
 */
export function getLocalStorageUsage(): {
  used: number;
  total: number;
  available: number;
  percentage: number;
} {
  if (!isLocalStorageAvailable()) {
    return { used: 0, total: 0, available: 0, percentage: 0 };
  }

  let used = 0;
  for (const key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      used += localStorage[key].length + key.length;
    }
  }

  // Most browsers have a 5-10MB limit for localStorage
  const total = 5 * 1024 * 1024; // 5MB in bytes
  const available = total - used;
  const percentage = (used / total) * 100;

  return { used, total, available, percentage };
}

/**
 * Storage event listener for cross-tab communication
 */
export function onStorageChange(
  callback: (event: StorageEvent) => void
): () => void {
  if (typeof window === 'undefined') {
    return () => {};
  }

  window.addEventListener('storage', callback);
  
  return () => {
    window.removeEventListener('storage', callback);
  };
}

/**
 * Persistent storage with expiration
 */
interface StorageItem<T> {
  value: T;
  expiry: number;
}

export function setStorageWithExpiry<T>(
  key: string,
  value: T,
  expiryInMinutes: number
): boolean {
  try {
    const now = new Date().getTime();
    const expiry = now + expiryInMinutes * 60 * 1000;
    
    const item: StorageItem<T> = {
      value,
      expiry,
    };
    
    return setLocalStorage(key, item);
  } catch (error) {
    console.error('Error setting storage with expiry:', error);
    return false;
  }
}

export function getStorageWithExpiry<T>(key: string): T | null {
  try {
    const item = getLocalStorage<StorageItem<T>>(key);
    
    if (!item) return null;
    
    const now = new Date().getTime();
    
    if (now > item.expiry) {
      removeLocalStorage(key);
      return null;
    }
    
    return item.value;
  } catch (error) {
    console.error('Error getting storage with expiry:', error);
    return null;
  }
}

/**
 * Compressed storage for large data
 */
export function setCompressedStorage<T>(key: string, value: T): boolean {
  try {
    const serialized = JSON.stringify(value);
    
    // Simple compression by removing whitespace and common patterns
    const compressed = serialized
      .replace(/\s+/g, '')
      .replace(/null/g, 'n')
      .replace(/true/g, 't')
      .replace(/false/g, 'f');
    
    return setLocalStorage(key, compressed);
  } catch (error) {
    console.error('Error setting compressed storage:', error);
    return false;
  }
}

export function getCompressedStorage<T>(key: string): T | null {
  try {
    const compressed = getLocalStorage<string>(key);
    
    if (!compressed) return null;
    
    // Decompress
    const decompressed = compressed
      .replace(/n/g, 'null')
      .replace(/t/g, 'true')
      .replace(/f/g, 'false');
    
    return JSON.parse(decompressed) as T;
  } catch (error) {
    console.error('Error getting compressed storage:', error);
    return null;
  }
}

/**
 * Storage keys constants for the application
 */
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'vrt_user_preferences',
  TOUR_DRAFT: 'vrt_tour_draft',
  RECENT_TOURS: 'vrt_recent_tours',
  SEARCH_HISTORY: 'vrt_search_history',
  THEME: 'vrt_theme',
  LANGUAGE: 'vrt_language',
  ONBOARDING_COMPLETED: 'vrt_onboarding_completed',
  ANALYTICS_CONSENT: 'vrt_analytics_consent',
  COOKIE_CONSENT: 'vrt_cookie_consent',
  LAST_SYNC: 'vrt_last_sync',
} as const;

/**
 * Type-safe storage helpers for specific app data
 */
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: boolean;
  autoplay: boolean;
  quality: 'low' | 'medium' | 'high';
}

export function setUserPreferences(preferences: UserPreferences): boolean {
  return setLocalStorage(STORAGE_KEYS.USER_PREFERENCES, preferences);
}

export function getUserPreferences(): UserPreferences | null {
  return getLocalStorage<UserPreferences>(STORAGE_KEYS.USER_PREFERENCES);
}

export interface TourDraft {
  id?: string;
  title: string;
  description: string;
  scenes: any[];
  lastModified: number;
}

export function setTourDraft(draft: TourDraft): boolean {
  return setLocalStorage(STORAGE_KEYS.TOUR_DRAFT, draft);
}

export function getTourDraft(): TourDraft | null {
  return getLocalStorage<TourDraft>(STORAGE_KEYS.TOUR_DRAFT);
}

export function clearTourDraft(): boolean {
  return removeLocalStorage(STORAGE_KEYS.TOUR_DRAFT);
}
