module.exports = {

"[project]/src/lib/supabase/client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@supabase_node-fetch_lib_index_993f246e.js",
  "server/chunks/ssr/node_modules_7b6337d0._.js",
  "server/chunks/ssr/[root-of-the-server]__b228fc43._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/supabase/client.ts [app-ssr] (ecmascript)");
    });
});
}}),

};