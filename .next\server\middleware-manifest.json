{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "FoRf0hChU4_hIKwmKCQqi", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "uosSlAe+zE20kF4ihbL/7sjCMKPCRG7pPQpdH/qjCM8=", "__NEXT_PREVIEW_MODE_ID": "e61bf8036fc0c6c0aa277958370f89d2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "04166c258bf9e498a65ef5486d91264545051fbb42ac919f4ee4fe9165e52723", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "097d708b765ac676b025a2b646d5d84e5de18dbf538a9f1469b4ed544a3665f0"}}}, "functions": {}, "sortedMiddleware": ["/"]}