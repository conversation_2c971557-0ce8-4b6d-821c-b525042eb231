{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "1tWVuO6yFKfGEgUahX1Tg", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FUsxA80QsodgHhSbkmoI30n+EABkuyYOXKTVfSnL19M=", "__NEXT_PREVIEW_MODE_ID": "da0cd98e751a17181bbc8d593909bd30", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0d2ffeab26b6d3f825778390f56f6d10cf854347b82d9c8dc77e5799ea8b670b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9637ec7067b9ea210b4ff1f45d865229d3b333913fcf59f357908de8a79f2507"}}}, "functions": {}, "sortedMiddleware": ["/"]}