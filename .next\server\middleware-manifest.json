{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_8a5525db._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_1c0c3e1d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FUsxA80QsodgHhSbkmoI30n+EABkuyYOXKTVfSnL19M=", "__NEXT_PREVIEW_MODE_ID": "cd59c33ca52e271dfa8605c0869e684c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d20b0e9fe3e4f83b1f5ebd897500967e93448675f37732ad816783c501dbbfb8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "49e065c61ea163da5f2ba3918f03473748499acad229d686d2d6f84ee69f7556"}}}, "sortedMiddleware": ["/"], "functions": {}}