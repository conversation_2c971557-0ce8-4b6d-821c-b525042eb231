{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_8a5525db._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_1c0c3e1d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "uosSlAe+zE20kF4ihbL/7sjCMKPCRG7pPQpdH/qjCM8=", "__NEXT_PREVIEW_MODE_ID": "68cc59fce7c05224588b8e51da17f130", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0c6437a1b8d9920ddb3ba455377931e8bb19ae373912fe37ce8d26c7e6d3236e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "46d7952a4befba982b8221c79f9b2a822fe56d415b037b30d8045ed67c9278f2"}}}, "sortedMiddleware": ["/"], "functions": {}}