module.exports = {

"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/client.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "STORAGE_BUCKETS": (()=>STORAGE_BUCKETS),
    "auth": (()=>auth),
    "createClient": (()=>createClient),
    "createSupabaseClient": (()=>createSupabaseClient),
    "db": (()=>db),
    "realtime": (()=>realtime),
    "storage": (()=>storage),
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$auth$2d$helpers$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/auth-helpers-nextjs/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-ssr] (ecmascript)");
;
;
;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$auth$2d$helpers$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createClientComponentClient"])();
const createClient = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createBrowserClient"])(("TURBOPACK compile-time value", "https://maudhokdhyhspfpasnfm.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1hdWRob2tkaHloc3BmcGFzbmZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjgwMDQsImV4cCI6MjA2NDc0NDAwNH0.yaD0ot54tD5tj5Bz2SMZXgp6x6fzVXsAz2xjvB-bFFo"));
function createSupabaseClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://maudhokdhyhspfpasnfm.supabase.co");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1hdWRob2tkaHloc3BmcGFzbmZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjgwMDQsImV4cCI6MjA2NDc0NDAwNH0.yaD0ot54tD5tj5Bz2SMZXgp6x6fzVXsAz2xjvB-bFFo");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey, {
        auth: {
            persistSession: true,
            autoRefreshToken: true,
            detectSessionInUrl: true
        }
    });
}
const STORAGE_BUCKETS = {
    MEDIA: 'media',
    AVATARS: 'avatars',
    THUMBNAILS: 'thumbnails',
    TEMP: 'temp'
};
const storage = {
    /**
   * Upload file to storage bucket
   */ async uploadFile (bucket, path, file, options) {
        const { data, error } = await supabase.storage.from(bucket).upload(path, file, {
            cacheControl: options?.cacheControl || '3600',
            contentType: options?.contentType || file.type,
            upsert: options?.upsert || false
        });
        if (error) {
            throw new Error(`Upload failed: ${error.message}`);
        }
        return data;
    },
    /**
   * Get public URL for a file
   */ getPublicUrl (bucket, path) {
        const { data } = supabase.storage.from(bucket).getPublicUrl(path);
        return data.publicUrl;
    },
    /**
   * Create signed URL for private files
   */ async createSignedUrl (bucket, path, expiresIn = 3600) {
        const { data, error } = await supabase.storage.from(bucket).createSignedUrl(path, expiresIn);
        if (error) {
            throw new Error(`Failed to create signed URL: ${error.message}`);
        }
        return data.signedUrl;
    },
    /**
   * Delete file from storage
   */ async deleteFile (bucket, path) {
        const { error } = await supabase.storage.from(bucket).remove([
            path
        ]);
        if (error) {
            throw new Error(`Delete failed: ${error.message}`);
        }
    },
    /**
   * List files in a bucket
   */ async listFiles (bucket, path) {
        const { data, error } = await supabase.storage.from(bucket).list(path);
        if (error) {
            throw new Error(`List failed: ${error.message}`);
        }
        return data;
    },
    /**
   * Move file within storage
   */ async moveFile (bucket, fromPath, toPath) {
        const { error } = await supabase.storage.from(bucket).move(fromPath, toPath);
        if (error) {
            throw new Error(`Move failed: ${error.message}`);
        }
    },
    /**
   * Copy file within storage
   */ async copyFile (bucket, fromPath, toPath) {
        const { error } = await supabase.storage.from(bucket).copy(fromPath, toPath);
        if (error) {
            throw new Error(`Copy failed: ${error.message}`);
        }
    }
};
const db = {
    /**
   * Generic select with error handling
   */ async select (table, columns, filters) {
        let query = supabase.from(table).select(columns || '*');
        if (filters) {
            Object.entries(filters).forEach(([key, value])=>{
                query = query.eq(key, value);
            });
        }
        const { data, error } = await query;
        if (error) {
            throw new Error(`Select failed: ${error.message}`);
        }
        return data;
    },
    /**
   * Generic insert with error handling
   */ async insert (table, data) {
        const { data: result, error } = await supabase.from(table).insert(data).select().single();
        if (error) {
            throw new Error(`Insert failed: ${error.message}`);
        }
        return result;
    },
    /**
   * Generic update with error handling
   */ async update (table, id, data) {
        const { data: result, error } = await supabase.from(table).update(data).eq('id', id).select().single();
        if (error) {
            throw new Error(`Update failed: ${error.message}`);
        }
        return result;
    },
    /**
   * Generic delete with error handling
   */ async delete (table, id) {
        const { error } = await supabase.from(table).delete().eq('id', id);
        if (error) {
            throw new Error(`Delete failed: ${error.message}`);
        }
    },
    /**
   * Count records in a table
   */ async count (table, filters) {
        let query = supabase.from(table).select('*', {
            count: 'exact',
            head: true
        });
        if (filters) {
            Object.entries(filters).forEach(([key, value])=>{
                query = query.eq(key, value);
            });
        }
        const { count, error } = await query;
        if (error) {
            throw new Error(`Count failed: ${error.message}`);
        }
        return count || 0;
    }
};
const realtime = {
    /**
   * Subscribe to table changes
   */ subscribeToTable (table, callback, filter) {
        const channel = supabase.channel(`${table}_changes`).on('postgres_changes', {
            event: '*',
            schema: 'public',
            table,
            filter
        }, callback).subscribe();
        return ()=>{
            supabase.removeChannel(channel);
        };
    },
    /**
   * Subscribe to specific record changes
   */ subscribeToRecord (table, id, callback) {
        return this.subscribeToTable(table, callback, `id=eq.${id}`);
    },
    /**
   * Subscribe to user's records
   */ subscribeToUserRecords (table, userId, callback) {
        return this.subscribeToTable(table, callback, `user_id=eq.${userId}`);
    }
};
const auth = {
    /**
   * Get current user
   */ async getCurrentUser () {
        const { data: { user }, error } = await supabase.auth.getUser();
        if (error) {
            throw new Error(`Failed to get user: ${error.message}`);
        }
        return user;
    },
    /**
   * Get current session
   */ async getCurrentSession () {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
            throw new Error(`Failed to get session: ${error.message}`);
        }
        return session;
    },
    /**
   * Sign out user
   */ async signOut () {
        const { error } = await supabase.auth.signOut();
        if (error) {
            throw new Error(`Sign out failed: ${error.message}`);
        }
    },
    /**
   * Listen to auth state changes
   */ onAuthStateChange (callback) {
        const { data: { subscription } } = supabase.auth.onAuthStateChange(callback);
        return ()=>{
            subscription.unsubscribe();
        };
    }
};
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b228fc43._.js.map