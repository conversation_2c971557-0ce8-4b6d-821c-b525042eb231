import { useState, useCallback } from 'react';
import { validateFile } from '@/lib/utils/file';

export interface UploadFile {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
  url?: string;
}

interface UseFileUploadOptions {
  maxFiles?: number;
  maxSize?: number; // in MB
  allowedTypes?: string[];
  onUploadComplete?: (files: UploadFile[]) => void;
  onUploadError?: (error: string) => void;
}

export function useFileUpload(options: UseFileUploadOptions = {}) {
  const {
    maxFiles = 10,
    maxSize = 100,
    allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'video/mp4'],
    onUploadComplete,
    onUploadError,
  } = options;

  const [files, setFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const generateId = () => Math.random().toString(36).substring(2, 15);

  const addFiles = useCallback(
    async (newFiles: FileList | File[]) => {
      const fileArray = Array.from(newFiles);
      
      // Check file count limit
      if (files.length + fileArray.length > maxFiles) {
        onUploadError?.(`Maximum ${maxFiles} files allowed`);
        return;
      }

      const validatedFiles: UploadFile[] = [];

      for (const file of fileArray) {
        // Validate file
        const validation = await validateFile(file, {
          maxSize,
          allowedTypes,
        });

        if (!validation.isValid) {
          onUploadError?.(validation.errors.join(', '));
          continue;
        }

        validatedFiles.push({
          file,
          id: generateId(),
          progress: 0,
          status: 'pending',
        });
      }

      setFiles(prev => [...prev, ...validatedFiles]);
    },
    [files.length, maxFiles, maxSize, allowedTypes, onUploadError]
  );

  const removeFile = useCallback((id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  }, []);

  const clearFiles = useCallback(() => {
    setFiles([]);
  }, []);

  const updateFileProgress = useCallback((id: string, progress: number) => {
    setFiles(prev =>
      prev.map(f => (f.id === id ? { ...f, progress } : f))
    );
  }, []);

  const updateFileStatus = useCallback(
    (id: string, status: UploadFile['status'], error?: string, url?: string) => {
      setFiles(prev =>
        prev.map(f =>
          f.id === id ? { ...f, status, error, url } : f
        )
      );
    },
    []
  );

  const uploadFiles = useCallback(async () => {
    if (files.length === 0) return;

    setIsUploading(true);

    try {
      const uploadPromises = files
        .filter(f => f.status === 'pending')
        .map(async uploadFile => {
          updateFileStatus(uploadFile.id, 'uploading');

          try {
            // Simulate upload progress
            for (let progress = 0; progress <= 100; progress += 10) {
              updateFileProgress(uploadFile.id, progress);
              await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Here you would implement actual upload logic
            // For now, we'll simulate a successful upload
            const mockUrl = URL.createObjectURL(uploadFile.file);
            updateFileStatus(uploadFile.id, 'completed', undefined, mockUrl);

            return { ...uploadFile, status: 'completed' as const, url: mockUrl };
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Upload failed';
            updateFileStatus(uploadFile.id, 'error', errorMessage);
            throw error;
          }
        });

      const results = await Promise.allSettled(uploadPromises);
      const successful = results
        .filter((result): result is PromiseFulfilledResult<UploadFile> => 
          result.status === 'fulfilled'
        )
        .map(result => result.value);

      if (successful.length > 0) {
        onUploadComplete?.(successful);
      }

      const failed = results.filter(result => result.status === 'rejected');
      if (failed.length > 0) {
        onUploadError?.(`${failed.length} files failed to upload`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      onUploadError?.(errorMessage);
    } finally {
      setIsUploading(false);
    }
  }, [files, updateFileStatus, updateFileProgress, onUploadComplete, onUploadError]);

  const retryFile = useCallback(
    (id: string) => {
      const file = files.find(f => f.id === id);
      if (file && file.status === 'error') {
        updateFileStatus(id, 'pending');
      }
    },
    [files, updateFileStatus]
  );

  // Calculate upload statistics
  const stats = {
    total: files.length,
    pending: files.filter(f => f.status === 'pending').length,
    uploading: files.filter(f => f.status === 'uploading').length,
    completed: files.filter(f => f.status === 'completed').length,
    error: files.filter(f => f.status === 'error').length,
    progress: files.length > 0 
      ? Math.round(files.reduce((acc, f) => acc + f.progress, 0) / files.length)
      : 0,
  };

  return {
    files,
    isUploading,
    stats,
    addFiles,
    removeFile,
    clearFiles,
    uploadFiles,
    retryFile,
  };
}
