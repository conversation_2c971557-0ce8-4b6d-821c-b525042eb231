{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';\nimport { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\nimport type { Database } from '@/types/supabase';\n\n// Define protected routes that require authentication\nconst protectedRoutes = [\n  '/dashboard',\n  '/dashboard/:path*',\n  '/profile',\n  '/settings',\n];\n\n// Define auth routes that should redirect to dashboard if already authenticated\nconst authRoutes = [\n  '/auth/signin',\n  '/auth/signup',\n  '/auth/forgot-password',\n  '/auth/reset-password',\n];\n\n// Define admin routes that require admin privileges\nconst adminRoutes = [\n  '/admin',\n  '/admin/:path*',\n];\n\n// Define API routes that need authentication\nconst protectedApiRoutes = [\n  '/api/tours',\n  '/api/media',\n  '/api/users/profile',\n  '/api/analytics',\n  '/api/subscriptions',\n];\n\nfunction isRouteMatch(pathname: string, routes: string[]): boolean {\n  return routes.some(route => {\n    if (route.includes(':path*')) {\n      const baseRoute = route.replace('/:path*', '');\n      return pathname.startsWith(baseRoute);\n    }\n    return pathname === route;\n  });\n}\n\nexport async function middleware(request: NextRequest) {\n  const response = NextResponse.next();\n\n  // Check if Supabase environment variables are available\n  const hasSupabaseConfig =\n    process.env.NEXT_PUBLIC_SUPABASE_URL &&\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n  let session = null;\n\n  if (hasSupabaseConfig) {\n    try {\n      const supabase = createMiddlewareClient<Database>({ req: request, res: response });\n      const { data } = await supabase.auth.getSession();\n      session = data.session;\n    } catch (error) {\n      console.error('Error getting session in middleware:', error);\n    }\n  }\n\n  const { pathname } = request.nextUrl;\n\n  // Handle API routes\n  if (pathname.startsWith('/api/')) {\n    // Check if API route requires authentication\n    if (isRouteMatch(pathname, protectedApiRoutes)) {\n      if (!session) {\n        return NextResponse.json(\n          { error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n    }\n\n    // Check for admin API routes\n    if (pathname.startsWith('/api/admin/')) {\n      if (!session) {\n        return NextResponse.json(\n          { error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      // Check if user is admin\n      const { data: user } = await supabase\n        .from('users')\n        .select('is_admin')\n        .eq('id', session.user.id)\n        .single();\n\n      if (!user?.is_admin) {\n        return NextResponse.json(\n          { error: 'Admin access required' },\n          { status: 403 }\n        );\n      }\n    }\n\n    return response;\n  }\n\n  // Handle auth routes - redirect to dashboard if already authenticated\n  if (isRouteMatch(pathname, authRoutes)) {\n    if (hasSupabaseConfig && session) {\n      return NextResponse.redirect(new URL('/dashboard', request.url));\n    }\n    return response;\n  }\n\n  // Handle protected routes - redirect to signin if not authenticated\n  if (isRouteMatch(pathname, protectedRoutes)) {\n    if (hasSupabaseConfig && !session) {\n      const redirectUrl = new URL('/auth/signin', request.url);\n      redirectUrl.searchParams.set('redirectTo', pathname);\n      return NextResponse.redirect(redirectUrl);\n    }\n    return response;\n  }\n\n  // Handle admin routes\n  if (isRouteMatch(pathname, adminRoutes)) {\n    if (!hasSupabaseConfig || !session) {\n      const redirectUrl = new URL('/auth/signin', request.url);\n      redirectUrl.searchParams.set('redirectTo', pathname);\n      return NextResponse.redirect(redirectUrl);\n    }\n\n    // Check if user is admin\n    try {\n      const supabase = createMiddlewareClient<Database>({ req: request, res: response });\n      const { data: user } = await supabase\n        .from('users')\n        .select('is_admin')\n        .eq('id', session.user.id)\n        .single();\n\n      if (!user?.is_admin) {\n        return NextResponse.redirect(new URL('/dashboard', request.url));\n      }\n    } catch (error) {\n      console.error('Error checking admin status:', error);\n      return NextResponse.redirect(new URL('/dashboard', request.url));\n    }\n    return response;\n  }\n\n  // Handle subscription-based access\n  if (pathname.startsWith('/dashboard/') && session) {\n    // Get user's subscription status\n    const { data: subscription } = await supabase\n      .from('subscriptions')\n      .select('status, plan_name')\n      .eq('user_id', session.user.id)\n      .single();\n\n    // Check if user has access to premium features\n    const premiumRoutes = [\n      '/dashboard/analytics',\n      '/dashboard/integrations',\n      '/dashboard/api',\n    ];\n\n    if (isRouteMatch(pathname, premiumRoutes)) {\n      const hasActivePremiumPlan = subscription?.status === 'active' &&\n        ['pro', 'enterprise'].includes(subscription.plan_name?.toLowerCase() || '');\n\n      if (!hasActivePremiumPlan) {\n        const redirectUrl = new URL('/dashboard/upgrade', request.url);\n        redirectUrl.searchParams.set('feature', pathname.split('/').pop() || '');\n        return NextResponse.redirect(redirectUrl);\n      }\n    }\n  }\n\n  // Add security headers\n  const requestHeaders = new Headers(request.headers);\n  requestHeaders.set('x-pathname', pathname);\n\n  // Set CSP headers for security\n  const cspHeader = `\n    default-src 'self';\n    script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.app *.supabase.co *.stripe.com *.paystack.co;\n    style-src 'self' 'unsafe-inline';\n    img-src 'self' data: blob: *.supabase.co *.stripe.com;\n    font-src 'self';\n    connect-src 'self' *.supabase.co *.stripe.com *.paystack.co *.flutterwave.com wss:;\n    media-src 'self' *.supabase.co;\n    object-src 'none';\n    base-uri 'self';\n    form-action 'self';\n    frame-ancestors 'none';\n    upgrade-insecure-requests;\n  `.replace(/\\s{2,}/g, ' ').trim();\n\n  response.headers.set('Content-Security-Policy', cspHeader);\n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');\n\n  return response;\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAIA,sDAAsD;AACtD,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;CACD;AAED,gFAAgF;AAChF,MAAM,aAAa;IACjB;IACA;IACA;IACA;CACD;AAED,oDAAoD;AACpD,MAAM,cAAc;IAClB;IACA;CACD;AAED,6CAA6C;AAC7C,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,aAAa,QAAgB,EAAE,MAAgB;IACtD,OAAO,OAAO,IAAI,CAAC,CAAA;QACjB,IAAI,MAAM,QAAQ,CAAC,WAAW;YAC5B,MAAM,YAAY,MAAM,OAAO,CAAC,WAAW;YAC3C,OAAO,SAAS,UAAU,CAAC;QAC7B;QACA,OAAO,aAAa;IACtB;AACF;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,wDAAwD;IACxD,MAAM,oBACJ;IAGF,IAAI,UAAU;IAEd,wCAAuB;QACrB,IAAI;YACF,MAAM,YAAW,CAAA,GAAA,gLAAA,CAAA,yBAAsB,AAAD,EAAY;gBAAE,KAAK;gBAAS,KAAK;YAAS;YAChF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,UAAS,IAAI,CAAC,UAAU;YAC/C,UAAU,KAAK,OAAO;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAEA,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,oBAAoB;IACpB,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,6CAA6C;QAC7C,IAAI,aAAa,UAAU,qBAAqB;YAC9C,IAAI,CAAC,SAAS;gBACZ,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA0B,GACnC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,6BAA6B;QAC7B,IAAI,SAAS,UAAU,CAAC,gBAAgB;YACtC,IAAI,CAAC,SAAS;gBACZ,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA0B,GACnC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,yBAAyB;YACzB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,SAC1B,IAAI,CAAC,SACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;YAET,IAAI,CAAC,MAAM,UAAU;gBACnB,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAwB,GACjC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO;IACT;IAEA,sEAAsE;IACtE,IAAI,aAAa,UAAU,aAAa;QACtC,IAAI,qBAAqB,SAAS;YAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;QAChE;QACA,OAAO;IACT;IAEA,oEAAoE;IACpE,IAAI,aAAa,UAAU,kBAAkB;QAC3C,IAAI,qBAAqB,CAAC,SAAS;YACjC,MAAM,cAAc,IAAI,IAAI,gBAAgB,QAAQ,GAAG;YACvD,YAAY,YAAY,CAAC,GAAG,CAAC,cAAc;YAC3C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QACA,OAAO;IACT;IAEA,sBAAsB;IACtB,IAAI,aAAa,UAAU,cAAc;QACvC,IAAI,CAAC,qBAAqB,CAAC,SAAS;YAClC,MAAM,cAAc,IAAI,IAAI,gBAAgB,QAAQ,GAAG;YACvD,YAAY,YAAY,CAAC,GAAG,CAAC,cAAc;YAC3C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,yBAAyB;QACzB,IAAI;YACF,MAAM,YAAW,CAAA,GAAA,gLAAA,CAAA,yBAAsB,AAAD,EAAY;gBAAE,KAAK;gBAAS,KAAK;YAAS;YAChF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,UAC1B,IAAI,CAAC,SACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;YAET,IAAI,CAAC,MAAM,UAAU;gBACnB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;QAChE;QACA,OAAO;IACT;IAEA,mCAAmC;IACnC,IAAI,SAAS,UAAU,CAAC,kBAAkB,SAAS;QACjD,iCAAiC;QACjC,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,CAAC,qBACP,EAAE,CAAC,WAAW,QAAQ,IAAI,CAAC,EAAE,EAC7B,MAAM;QAET,+CAA+C;QAC/C,MAAM,gBAAgB;YACpB;YACA;YACA;SACD;QAED,IAAI,aAAa,UAAU,gBAAgB;YACzC,MAAM,uBAAuB,cAAc,WAAW,YACpD;gBAAC;gBAAO;aAAa,CAAC,QAAQ,CAAC,aAAa,SAAS,EAAE,iBAAiB;YAE1E,IAAI,CAAC,sBAAsB;gBACzB,MAAM,cAAc,IAAI,IAAI,sBAAsB,QAAQ,GAAG;gBAC7D,YAAY,YAAY,CAAC,GAAG,CAAC,WAAW,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;gBACrE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,IAAI,QAAQ,QAAQ,OAAO;IAClD,eAAe,GAAG,CAAC,cAAc;IAEjC,+BAA+B;IAC/B,MAAM,YAAY,CAAC;;;;;;;;;;;;;EAanB,CAAC,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI;IAE9B,SAAS,OAAO,CAAC,GAAG,CAAC,2BAA2B;IAChD,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAE3C,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}