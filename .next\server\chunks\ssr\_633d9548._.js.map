{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Eye } from 'lucide-react';\n\nconst footerLinks = {\n  product: [\n    { href: '/tours', label: 'Tours' },\n    { href: '/pricing', label: 'Pricing' },\n    { href: '/features', label: 'Features' },\n    { href: '/integrations', label: 'Integrations' },\n  ],\n  company: [\n    { href: '/about', label: 'About' },\n    { href: '/contact', label: 'Contact' },\n    { href: '/careers', label: 'Careers' },\n    { href: '/blog', label: 'Blog' },\n  ],\n  resources: [\n    { href: '/help', label: 'Help Center' },\n    { href: '/docs', label: 'Documentation' },\n    { href: '/api', label: 'API Reference' },\n    { href: '/status', label: 'Status' },\n  ],\n  legal: [\n    { href: '/privacy', label: 'Privacy Policy' },\n    { href: '/terms', label: 'Terms of Service' },\n    { href: '/cookies', label: 'Cookie Policy' },\n    { href: '/gdpr', label: 'GDPR' },\n  ],\n};\n\nexport function Footer() {\n  return (\n    <footer className=\"border-t bg-background\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-5 gap-8\">\n          {/* Brand */}\n          <div className=\"md:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <Eye className=\"h-6 w-6 text-primary\" />\n              <span className=\"text-lg font-bold\">VirtualRealTour</span>\n            </Link>\n            <p className=\"text-sm text-muted-foreground mb-4\">\n              Premium 360° virtual tour platform designed for the Nigerian market.\n            </p>\n            <p className=\"text-xs text-muted-foreground\">\n              Made with ❤️ in Nigeria\n            </p>\n          </div>\n\n          {/* Product */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Resources */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Resources</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div>\n            <h3 className=\"font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-sm text-muted-foreground\">\n            &copy; 2024 VirtualRealTour. All rights reserved.\n          </p>\n          <div className=\"flex items-center space-x-4 mt-4 md:mt-0\">\n            <Link\n              href=\"/privacy\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Privacy\n            </Link>\n            <Link\n              href=\"/terms\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Terms\n            </Link>\n            <Link\n              href=\"/cookies\"\n              className=\"text-sm text-muted-foreground hover:text-foreground\"\n            >\n              Cookies\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAiB,OAAO;QAAe;KAChD;IACD,SAAS;QACP;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAS,OAAO;QAAO;KAChC;IACD,WAAW;QACT;YAAE,MAAM;YAAS,OAAO;QAAc;QACtC;YAAE,MAAM;YAAS,OAAO;QAAgB;QACxC;YAAE,MAAM;YAAQ,OAAO;QAAgB;QACvC;YAAE,MAAM;YAAW,OAAO;QAAS;KACpC;IACD,OAAO;QACL;YAAE,MAAM;YAAY,OAAO;QAAiB;QAC5C;YAAE,MAAM;YAAU,OAAO;QAAmB;QAC5C;YAAE,MAAM;YAAY,OAAO;QAAgB;QAC3C;YAAE,MAAM;YAAS,OAAO;QAAO;KAChC;AACH;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAM/C,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAa1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/layout/public-layout.tsx"], "sourcesContent": ["import { Header } from './header';\nimport { Footer } from './footer';\n\ninterface PublicLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function PublicLayout({ children }: PublicLayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header variant=\"public\" />\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;gBAAC,SAAQ;;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/app/terms/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\nimport { PublicLayout } from \"@/components/layout/public-layout\";\nimport { FileText, Scale, AlertTriangle, Users } from \"lucide-react\";\n\nexport const metadata: Metadata = {\n  title: \"Terms of Service\",\n  description: \"Read VirtualRealTour's terms of service and user agreement.\",\n};\n\nexport default function TermsPage() {\n  return (\n    <PublicLayout>\n      <div className=\"py-20\">\n        <div className=\"container mx-auto px-4 max-w-4xl\">\n          {/* Header */}\n          <div className=\"text-center mb-12\">\n            <div className=\"flex justify-center mb-4\">\n              <FileText className=\"h-12 w-12 text-primary\" />\n            </div>\n            <h1 className=\"text-4xl font-bold mb-4\">Terms of Service</h1>\n            <p className=\"text-muted-foreground\">\n              Last updated: January 1, 2024\n            </p>\n          </div>\n\n          {/* Important Notice */}\n          <Card className=\"mb-8 border-orange-200 bg-orange-50\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-orange-800\">\n                <AlertTriangle className=\"h-5 w-5 mr-2\" />\n                Important Notice\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-orange-700\">\n                By using VirtualRealTour's services, you agree to be bound by these terms. \n                Please read them carefully before using our platform.\n              </p>\n            </CardContent>\n          </Card>\n\n          <div className=\"space-y-8\">\n            {/* Acceptance of Terms */}\n            <Card>\n              <CardHeader>\n                <CardTitle>1. Acceptance of Terms</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-muted-foreground\">\n                  These Terms of Service (\"Terms\") govern your use of VirtualRealTour's \n                  platform and services. By accessing or using our services, you agree \n                  to be bound by these Terms and our Privacy Policy. If you do not agree \n                  to these Terms, you may not use our services.\n                </p>\n              </CardContent>\n            </Card>\n\n            {/* Description of Service */}\n            <Card>\n              <CardHeader>\n                <CardTitle>2. Description of Service</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-muted-foreground mb-4\">\n                  VirtualRealTour provides a platform for creating, hosting, and sharing \n                  360° virtual tours. Our services include:\n                </p>\n                <ul className=\"list-disc list-inside text-muted-foreground space-y-1\">\n                  <li>Virtual tour creation and editing tools</li>\n                  <li>Cloud hosting and content delivery</li>\n                  <li>Analytics and performance tracking</li>\n                  <li>Integration with third-party services</li>\n                  <li>Customer support and documentation</li>\n                </ul>\n              </CardContent>\n            </Card>\n\n            {/* User Accounts */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Users className=\"h-5 w-5 mr-2\" />\n                  3. User Accounts\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Account Creation</h3>\n                  <p className=\"text-muted-foreground\">\n                    You must create an account to use our services. You are responsible \n                    for maintaining the confidentiality of your account credentials and \n                    for all activities that occur under your account.\n                  </p>\n                </div>\n                \n                <div>\n                  <h3 className=\"font-semibold mb-2\">Eligibility</h3>\n                  <p className=\"text-muted-foreground\">\n                    You must be at least 18 years old to create an account. By creating \n                    an account, you represent that you have the legal capacity to enter \n                    into these Terms.\n                  </p>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Account Security</h3>\n                  <p className=\"text-muted-foreground\">\n                    You must notify us immediately of any unauthorized use of your account \n                    or any other breach of security. We are not liable for any loss or \n                    damage arising from your failure to protect your account.\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Acceptable Use */}\n            <Card>\n              <CardHeader>\n                <CardTitle>4. Acceptable Use Policy</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <p className=\"text-muted-foreground\">\n                  You agree not to use our services for any unlawful or prohibited activities, including:\n                </p>\n                \n                <div>\n                  <h3 className=\"font-semibold mb-2\">Prohibited Content</h3>\n                  <ul className=\"list-disc list-inside text-muted-foreground space-y-1\">\n                    <li>Illegal, harmful, or offensive content</li>\n                    <li>Content that violates intellectual property rights</li>\n                    <li>Spam, malware, or malicious code</li>\n                    <li>Content that promotes violence or discrimination</li>\n                    <li>Adult content or material harmful to minors</li>\n                  </ul>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Prohibited Activities</h3>\n                  <ul className=\"list-disc list-inside text-muted-foreground space-y-1\">\n                    <li>Attempting to gain unauthorized access to our systems</li>\n                    <li>Interfering with or disrupting our services</li>\n                    <li>Using automated tools to access our platform</li>\n                    <li>Impersonating others or providing false information</li>\n                    <li>Violating any applicable laws or regulations</li>\n                  </ul>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Content and Intellectual Property */}\n            <Card>\n              <CardHeader>\n                <CardTitle>5. Content and Intellectual Property</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Your Content</h3>\n                  <p className=\"text-muted-foreground\">\n                    You retain ownership of the content you upload to our platform. \n                    By uploading content, you grant us a license to use, store, and \n                    display your content as necessary to provide our services.\n                  </p>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Our Content</h3>\n                  <p className=\"text-muted-foreground\">\n                    Our platform, including its design, features, and functionality, \n                    is owned by VirtualRealTour and protected by intellectual property laws. \n                    You may not copy, modify, or distribute our content without permission.\n                  </p>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Copyright Infringement</h3>\n                  <p className=\"text-muted-foreground\">\n                    We respect intellectual property rights and will respond to valid \n                    copyright infringement notices. If you believe your copyright has \n                    been infringed, please contact us with the required information.\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Payment Terms */}\n            <Card>\n              <CardHeader>\n                <CardTitle>6. Payment Terms</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Subscription Plans</h3>\n                  <p className=\"text-muted-foreground\">\n                    We offer various subscription plans with different features and pricing. \n                    Subscription fees are billed in advance and are non-refundable except \n                    as required by law.\n                  </p>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Payment Processing</h3>\n                  <p className=\"text-muted-foreground\">\n                    Payments are processed by third-party payment providers. You agree \n                    to provide accurate payment information and authorize us to charge \n                    your payment method for applicable fees.\n                  </p>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Cancellation and Refunds</h3>\n                  <p className=\"text-muted-foreground\">\n                    You may cancel your subscription at any time. Cancellations take \n                    effect at the end of your current billing period. We offer a \n                    30-day money-back guarantee for new subscriptions.\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Privacy and Data Protection */}\n            <Card>\n              <CardHeader>\n                <CardTitle>7. Privacy and Data Protection</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-muted-foreground\">\n                  Your privacy is important to us. Our Privacy Policy explains how we \n                  collect, use, and protect your information. By using our services, \n                  you consent to our data practices as described in our Privacy Policy.\n                </p>\n              </CardContent>\n            </Card>\n\n            {/* Disclaimers and Limitations */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Scale className=\"h-5 w-5 mr-2\" />\n                  8. Disclaimers and Limitations of Liability\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Service Availability</h3>\n                  <p className=\"text-muted-foreground\">\n                    We strive to provide reliable services but cannot guarantee 100% uptime. \n                    Our services are provided \"as is\" without warranties of any kind.\n                  </p>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-2\">Limitation of Liability</h3>\n                  <p className=\"text-muted-foreground\">\n                    To the maximum extent permitted by law, VirtualRealTour shall not be \n                    liable for any indirect, incidental, special, or consequential damages \n                    arising from your use of our services.\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Termination */}\n            <Card>\n              <CardHeader>\n                <CardTitle>9. Termination</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-muted-foreground\">\n                  We may terminate or suspend your account at any time for violation of \n                  these Terms or for any other reason. Upon termination, your right to \n                  use our services will cease immediately, and we may delete your content.\n                </p>\n              </CardContent>\n            </Card>\n\n            {/* Governing Law */}\n            <Card>\n              <CardHeader>\n                <CardTitle>10. Governing Law and Dispute Resolution</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-muted-foreground\">\n                  These Terms are governed by the laws of Nigeria. Any disputes arising \n                  from these Terms or your use of our services will be resolved through \n                  binding arbitration in Lagos, Nigeria, except where prohibited by law.\n                </p>\n              </CardContent>\n            </Card>\n\n            {/* Changes to Terms */}\n            <Card>\n              <CardHeader>\n                <CardTitle>11. Changes to Terms</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-muted-foreground\">\n                  We may update these Terms from time to time. We will notify you of \n                  material changes by posting the updated Terms on our website and \n                  sending you an email notification. Your continued use of our services \n                  constitutes acceptance of the updated Terms.\n                </p>\n              </CardContent>\n            </Card>\n\n            {/* Contact Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle>12. Contact Information</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-muted-foreground mb-4\">\n                  If you have any questions about these Terms, please contact us:\n                </p>\n                <div className=\"space-y-2 text-muted-foreground\">\n                  <p><strong>Email:</strong> <EMAIL></p>\n                  <p><strong>Address:</strong> 123 Victoria Island, Lagos State, Nigeria</p>\n                  <p><strong>Phone:</strong> +234 (0) ************</p>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </PublicLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC,gJAAA,CAAA,eAAY;kBACX,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAMvC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI9C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAE,WAAU;8CAAkB;;;;;;;;;;;;;;;;;kCAOnC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAUzC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAI1C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAItC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAOvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAOvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAU3C,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DAIrC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAIR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOZ,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAOvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAOvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAU3C,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAOvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAOvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAU3C,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CASzC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAItC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAMvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAU3C,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CASzC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CASzC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAUzC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAe;;;;;;;kEAC1B,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAiB;;;;;;;kEAC5B,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}]}