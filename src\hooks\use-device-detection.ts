import { useState, useEffect } from 'react';

export interface DeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop';
  os: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown';
  browser: 'chrome' | 'firefox' | 'safari' | 'edge' | 'opera' | 'unknown';
  isTouch: boolean;
  isVRCapable: boolean;
  screenSize: {
    width: number;
    height: number;
  };
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
}

export function useDeviceDetection(): DeviceInfo {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    type: 'desktop',
    os: 'unknown',
    browser: 'unknown',
    isTouch: false,
    isVRCapable: false,
    screenSize: { width: 0, height: 0 },
    orientation: 'landscape',
    pixelRatio: 1,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const detectDevice = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const width = window.innerWidth;
      const height = window.innerHeight;

      // Detect device type
      let type: DeviceInfo['type'] = 'desktop';
      if (width <= 768) {
        type = 'mobile';
      } else if (width <= 1024) {
        type = 'tablet';
      }

      // Detect OS
      let os: DeviceInfo['os'] = 'unknown';
      if (/iphone|ipad|ipod/.test(userAgent)) {
        os = 'ios';
      } else if (/android/.test(userAgent)) {
        os = 'android';
      } else if (/windows/.test(userAgent)) {
        os = 'windows';
      } else if (/macintosh|mac os x/.test(userAgent)) {
        os = 'macos';
      } else if (/linux/.test(userAgent)) {
        os = 'linux';
      }

      // Detect browser
      let browser: DeviceInfo['browser'] = 'unknown';
      if (/chrome/.test(userAgent) && !/edge/.test(userAgent)) {
        browser = 'chrome';
      } else if (/firefox/.test(userAgent)) {
        browser = 'firefox';
      } else if (/safari/.test(userAgent) && !/chrome/.test(userAgent)) {
        browser = 'safari';
      } else if (/edge/.test(userAgent)) {
        browser = 'edge';
      } else if (/opera/.test(userAgent)) {
        browser = 'opera';
      }

      // Detect touch capability
      const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

      // Detect VR capability
      const isVRCapable = !!(
        navigator.getVRDisplays ||
        navigator.xr ||
        (window as any).DeviceOrientationEvent
      );

      // Detect orientation
      const orientation = width > height ? 'landscape' : 'portrait';

      // Get pixel ratio
      const pixelRatio = window.devicePixelRatio || 1;

      setDeviceInfo({
        type,
        os,
        browser,
        isTouch,
        isVRCapable,
        screenSize: { width, height },
        orientation,
        pixelRatio,
      });
    };

    // Initial detection
    detectDevice();

    // Listen for resize events
    const handleResize = () => {
      detectDevice();
    };

    // Listen for orientation change
    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(detectDevice, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  return deviceInfo;
}

// Helper hooks for specific device checks
export function useIsMobile() {
  const { type } = useDeviceDetection();
  return type === 'mobile';
}

export function useIsTablet() {
  const { type } = useDeviceDetection();
  return type === 'tablet';
}

export function useIsDesktop() {
  const { type } = useDeviceDetection();
  return type === 'desktop';
}

export function useIsTouch() {
  const { isTouch } = useDeviceDetection();
  return isTouch;
}

export function useIsVRCapable() {
  const { isVRCapable } = useDeviceDetection();
  return isVRCapable;
}

export function useIsIOS() {
  const { os } = useDeviceDetection();
  return os === 'ios';
}

export function useIsAndroid() {
  const { os } = useDeviceDetection();
  return os === 'android';
}
