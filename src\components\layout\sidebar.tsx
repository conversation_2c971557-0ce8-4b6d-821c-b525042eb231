'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  LayoutDashboard,
  Eye,
  Image,
  BarChart3,
  Settings,
  CreditCard,
  Users,
  HelpCircle,
  Zap,
} from 'lucide-react';

interface SidebarProps {
  className?: string;
}

const sidebarNavItems = [
  {
    title: 'Overview',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Tours',
    href: '/dashboard/tours',
    icon: Eye,
  },
  {
    title: 'Media Library',
    href: '/dashboard/media',
    icon: Image,
  },
  {
    title: 'Analytics',
    href: '/dashboard/analytics',
    icon: BarChart3,
    badge: 'Pro',
  },
  {
    title: 'Integrations',
    href: '/dashboard/integrations',
    icon: Zap,
    badge: 'Pro',
  },
  {
    title: 'Team',
    href: '/dashboard/team',
    icon: Users,
    badge: 'Enterprise',
  },
];

const bottomNavItems = [
  {
    title: 'Billing',
    href: '/dashboard/billing',
    icon: CreditCard,
  },
  {
    title: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
  },
  {
    title: 'Help & Support',
    href: '/dashboard/help',
    icon: HelpCircle,
  },
];

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname();

  return (
    <div className={cn('pb-12 w-64', className)}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <div className="space-y-1">
            <nav className="grid items-start gap-2">
              {sidebarNavItems.map((item) => (
                <Link key={item.href} href={item.href}>
                  <span
                    className={cn(
                      'group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground',
                      pathname === item.href
                        ? 'bg-accent text-accent-foreground'
                        : 'transparent'
                    )}
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    <span>{item.title}</span>
                    {item.badge && (
                      <Badge
                        variant="secondary"
                        className="ml-auto text-xs"
                      >
                        {item.badge}
                      </Badge>
                    )}
                  </span>
                </Link>
              ))}
            </nav>
          </div>
        </div>

        {/* Upgrade Banner */}
        <div className="px-3">
          <div className="rounded-lg bg-gradient-to-r from-primary to-primary/80 p-4 text-primary-foreground">
            <div className="space-y-2">
              <h3 className="font-semibold">Upgrade to Pro</h3>
              <p className="text-sm opacity-90">
                Unlock advanced analytics, integrations, and unlimited tours.
              </p>
              <Button
                size="sm"
                variant="secondary"
                className="w-full"
                asChild
              >
                <Link href="/dashboard/upgrade">
                  Upgrade Now
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Navigation */}
        <div className="px-3 py-2">
          <div className="space-y-1">
            <nav className="grid items-start gap-2">
              {bottomNavItems.map((item) => (
                <Link key={item.href} href={item.href}>
                  <span
                    className={cn(
                      'group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground',
                      pathname === item.href
                        ? 'bg-accent text-accent-foreground'
                        : 'transparent'
                    )}
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    <span>{item.title}</span>
                  </span>
                </Link>
              ))}
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}
