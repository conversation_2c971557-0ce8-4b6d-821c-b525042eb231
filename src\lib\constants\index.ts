// Application constants

export const APP_CONFIG = {
  name: 'VirtualRealTour',
  description: 'Premium 360° virtual tour platform for Nigeria',
  version: '1.0.0',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  supportEmail: '<EMAIL>',
  contactEmail: '<EMAIL>',
} as const;

export const ROUTES = {
  HOME: '/',
  TOURS: '/tours',
  DASHBOARD: '/dashboard',
  DASHBOARD_TOURS: '/dashboard/tours',
  DASHBOARD_MEDIA: '/dashboard/media',
  DASHBOARD_ANALYTICS: '/dashboard/analytics',
  DASHBOARD_SETTINGS: '/dashboard/settings',
  AUTH_SIGNIN: '/auth/signin',
  AUTH_SIGNUP: '/auth/signup',
  AUTH_FORGOT_PASSWORD: '/auth/forgot-password',
  AUTH_RESET_PASSWORD: '/auth/reset-password',
  PROFILE: '/profile',
  PRICING: '/pricing',
  ABOUT: '/about',
  CONTACT: '/contact',
  PRIVACY: '/privacy',
  TERMS: '/terms',
} as const;

export const API_ROUTES = {
  AUTH: '/api/auth',
  USERS: '/api/users',
  TOURS: '/api/tours',
  SCENES: '/api/scenes',
  HOTSPOTS: '/api/hotspots',
  MEDIA: '/api/media',
  ANALYTICS: '/api/analytics',
  SUBSCRIPTIONS: '/api/subscriptions',
  WEBHOOKS: '/api/webhooks',
} as const;

export const SUBSCRIPTION_PLANS = {
  FREE: {
    id: 'free',
    name: 'Free',
    price: 0,
    currency: 'NGN',
    interval: 'month',
    features: [
      'Up to 3 tours',
      'Basic 360° viewer',
      'Standard hotspots',
      'Community support',
      '1GB storage',
    ],
    limits: {
      tours: 3,
      scenes_per_tour: 10,
      storage_gb: 1,
      monthly_views: 1000,
    },
  },
  PRO: {
    id: 'pro',
    name: 'Pro',
    price: 15000,
    currency: 'NGN',
    interval: 'month',
    features: [
      'Up to 50 tours',
      'Advanced 360° viewer',
      'All hotspot types',
      'WhatsApp integration',
      'Analytics dashboard',
      'Custom branding',
      'Priority support',
      '50GB storage',
    ],
    limits: {
      tours: 50,
      scenes_per_tour: 50,
      storage_gb: 50,
      monthly_views: 50000,
    },
  },
  ENTERPRISE: {
    id: 'enterprise',
    name: 'Enterprise',
    price: 50000,
    currency: 'NGN',
    interval: 'month',
    features: [
      'Unlimited tours',
      'VR/WebXR support',
      'White-label solution',
      'API access',
      'Advanced analytics',
      'Custom integrations',
      'Dedicated support',
      'Unlimited storage',
    ],
    limits: {
      tours: Infinity,
      scenes_per_tour: Infinity,
      storage_gb: Infinity,
      monthly_views: Infinity,
    },
  },
} as const;

export const TOUR_CATEGORIES = [
  { id: 'real-estate', name: 'Real Estate', icon: 'Home' },
  { id: 'education', name: 'Education', icon: 'GraduationCap' },
  { id: 'hospitality', name: 'Hospitality', icon: 'Hotel' },
  { id: 'retail', name: 'Retail', icon: 'ShoppingBag' },
  { id: 'events', name: 'Events', icon: 'Calendar' },
  { id: 'automotive', name: 'Automotive', icon: 'Car' },
  { id: 'healthcare', name: 'Healthcare', icon: 'Heart' },
  { id: 'entertainment', name: 'Entertainment', icon: 'Music' },
  { id: 'museums', name: 'Museums', icon: 'Building' },
  { id: 'other', name: 'Other', icon: 'MoreHorizontal' },
] as const;

export const HOTSPOT_TYPES = [
  {
    id: 'navigation',
    name: 'Navigation',
    description: 'Navigate to another scene',
    icon: 'ArrowRight',
    color: '#3B82F6',
  },
  {
    id: 'info',
    name: 'Information',
    description: 'Show information panel',
    icon: 'Info',
    color: '#10B981',
  },
  {
    id: 'media',
    name: 'Media',
    description: 'Display image or video',
    icon: 'Image',
    color: '#F59E0B',
  },
  {
    id: 'link',
    name: 'External Link',
    description: 'Open external website',
    icon: 'ExternalLink',
    color: '#8B5CF6',
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    description: 'Contact via WhatsApp',
    icon: 'MessageCircle',
    color: '#25D366',
  },
  {
    id: 'product',
    name: 'Product',
    description: 'Show product information',
    icon: 'ShoppingCart',
    color: '#EF4444',
  },
] as const;

export const MEDIA_TYPES = {
  IMAGE_360: 'image_360',
  VIDEO_360: 'video_360',
  IMAGE: 'image',
  VIDEO: 'video',
  AUDIO: 'audio',
} as const;

export const PROCESSING_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

export const TOUR_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  ARCHIVED: 'archived',
  PENDING_REVIEW: 'pending_review',
} as const;

export const TOUR_VISIBILITY = {
  PUBLIC: 'public',
  PRIVATE: 'private',
  UNLISTED: 'unlisted',
} as const;

export const FILE_UPLOAD_LIMITS = {
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_FILES_PER_UPLOAD: 10,
  ALLOWED_IMAGE_TYPES: [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/gif',
  ],
  ALLOWED_VIDEO_TYPES: [
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/avi',
    'video/mov',
    'video/quicktime',
  ],
  ALLOWED_AUDIO_TYPES: [
    'audio/mp3',
    'audio/wav',
    'audio/ogg',
    'audio/aac',
    'audio/m4a',
  ],
} as const;

export const NIGERIAN_STATES = [
  'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa',
  'Benue', 'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo',
  'Ekiti', 'Enugu', 'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna',
  'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos', 'Nasarawa',
  'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers',
  'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
] as const;

export const CURRENCIES = [
  { code: 'NGN', name: 'Nigerian Naira', symbol: '₦' },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
] as const;

export const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'ha', name: 'Hausa' },
  { code: 'ig', name: 'Igbo' },
  { code: 'yo', name: 'Yoruba' },
] as const;

export const DEVICE_TYPES = {
  DESKTOP: 'desktop',
  MOBILE: 'mobile',
  TABLET: 'tablet',
  VR: 'vr',
} as const;

export const ANALYTICS_EVENTS = {
  TOUR_VIEW: 'tour_view',
  SCENE_VIEW: 'scene_view',
  HOTSPOT_CLICK: 'hotspot_click',
  TOUR_LIKE: 'tour_like',
  TOUR_SHARE: 'tour_share',
  TOUR_EMBED: 'tour_embed',
  USER_SIGNUP: 'user_signup',
  USER_SIGNIN: 'user_signin',
  SUBSCRIPTION_CREATED: 'subscription_created',
  SUBSCRIPTION_CANCELLED: 'subscription_cancelled',
  MEDIA_UPLOAD: 'media_upload',
  TOUR_CREATED: 'tour_created',
  TOUR_PUBLISHED: 'tour_published',
} as const;

export const SOCIAL_PLATFORMS = [
  { id: 'facebook', name: 'Facebook', icon: 'Facebook', color: '#1877F2' },
  { id: 'twitter', name: 'Twitter', icon: 'Twitter', color: '#1DA1F2' },
  { id: 'linkedin', name: 'LinkedIn', icon: 'Linkedin', color: '#0A66C2' },
  { id: 'whatsapp', name: 'WhatsApp', icon: 'MessageCircle', color: '#25D366' },
  { id: 'telegram', name: 'Telegram', icon: 'Send', color: '#0088CC' },
  { id: 'email', name: 'Email', icon: 'Mail', color: '#6B7280' },
] as const;

export const THEME_OPTIONS = [
  { id: 'light', name: 'Light', icon: 'Sun' },
  { id: 'dark', name: 'Dark', icon: 'Moon' },
  { id: 'system', name: 'System', icon: 'Monitor' },
] as const;

export const QUALITY_OPTIONS = [
  { id: 'low', name: 'Low (720p)', bandwidth: 'low' },
  { id: 'medium', name: 'Medium (1080p)', bandwidth: 'medium' },
  { id: 'high', name: 'High (4K)', bandwidth: 'high' },
  { id: 'auto', name: 'Auto', bandwidth: 'auto' },
] as const;

export const ANIMATION_PRESETS = [
  { id: 'none', name: 'None', duration: 0 },
  { id: 'fade', name: 'Fade', duration: 300 },
  { id: 'slide', name: 'Slide', duration: 500 },
  { id: 'zoom', name: 'Zoom', duration: 400 },
  { id: 'bounce', name: 'Bounce', duration: 600 },
] as const;

export const ERROR_MESSAGES = {
  GENERIC: 'Something went wrong. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit.',
  INVALID_FILE_TYPE: 'Invalid file type.',
  UPLOAD_FAILED: 'File upload failed. Please try again.',
  PROCESSING_FAILED: 'Media processing failed.',
  SUBSCRIPTION_REQUIRED: 'This feature requires a subscription.',
  LIMIT_EXCEEDED: 'You have reached your plan limit.',
} as const;

export const SUCCESS_MESSAGES = {
  TOUR_CREATED: 'Tour created successfully!',
  TOUR_UPDATED: 'Tour updated successfully!',
  TOUR_PUBLISHED: 'Tour published successfully!',
  TOUR_DELETED: 'Tour deleted successfully!',
  MEDIA_UPLOADED: 'Media uploaded successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  SETTINGS_SAVED: 'Settings saved successfully!',
  EMAIL_SENT: 'Email sent successfully!',
  SUBSCRIPTION_CREATED: 'Subscription created successfully!',
  SUBSCRIPTION_CANCELLED: 'Subscription cancelled successfully!',
} as const;
