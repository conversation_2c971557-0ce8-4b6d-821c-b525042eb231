# VirtualRealTour Platform - Local Environment Variables
# Copy this file and rename to .env.local, then fill in your actual values

# Supabase Configuration (Required for full functionality)
NEXT_PUBLIC_SUPABASE_URL=https://maudhokdhyhspfpasnfm.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1hdWRob2tkaHloc3BmcGFzbmZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjgwMDQsImV4cCI6MjA2NDc0NDAwNH0.yaD0ot54tD5tj5Bz2SMZXgp6x6fzVXsAz2xjvB-bFFo
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Feature Flags
NEXT_PUBLIC_ENABLE_VR=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_PAYMENTS=true
NEXT_PUBLIC_ENABLE_WHATSAPP=true
