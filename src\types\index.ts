// Core application types

export interface User {
  id: string;
  email: string;
  fullName: string;
  avatarUrl?: string;
  phone?: string;
  company?: string;
  website?: string;
  bio?: string;
  location?: string;
  subscriptionTier: 'free' | 'pro' | 'enterprise';
  subscriptionStatus: 'active' | 'canceled' | 'past_due' | 'unpaid';
  subscriptionExpiresAt?: string;
  totalTours: number;
  totalViews: number;
  isVerified: boolean;
  isAdmin: boolean;
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  display: DisplaySettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  marketing: boolean;
  tourViews: boolean;
  tourLikes: boolean;
  tourComments: boolean;
  systemUpdates: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private';
  showEmail: boolean;
  showPhone: boolean;
  allowAnalytics: boolean;
  allowMarketing: boolean;
}

export interface DisplaySettings {
  autoplay: boolean;
  quality: 'low' | 'medium' | 'high' | 'auto';
  showTutorials: boolean;
  compactMode: boolean;
}

export interface Tour {
  id: string;
  userId: string;
  title: string;
  description?: string;
  slug: string;
  thumbnailUrl?: string;
  category: string;
  tags?: string[];
  location?: string;
  latitude?: number;
  longitude?: number;
  address?: string;
  price?: number;
  currency: string;
  status: 'draft' | 'published' | 'archived' | 'pending_review';
  visibility: 'public' | 'private' | 'unlisted';
  featured: boolean;
  featuredUntil?: string;
  totalScenes: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  settings: TourSettings;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  user?: Pick<User, 'id' | 'fullName' | 'avatarUrl'>;
  scenes?: Scene[];
}

export interface TourSettings {
  autoplay?: boolean;
  showControls?: boolean;
  showInfo?: boolean;
  allowDownload?: boolean;
  enableVR?: boolean;
  backgroundMusic?: string;
  customCSS?: string;
  watermark?: {
    enabled: boolean;
    text?: string;
    image?: string;
    position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  };
}

export interface Scene {
  id: string;
  tourId: string;
  title: string;
  description?: string;
  mediaId?: string;
  orderIndex: number;
  isStartingScene: boolean;
  position?: Vector3D;
  rotation?: Vector3D;
  settings: SceneSettings;
  createdAt: string;
  updatedAt: string;
  media?: Media;
  hotspots?: Hotspot[];
}

export interface SceneSettings {
  initialView?: {
    yaw: number;
    pitch: number;
    fov: number;
  };
  restrictions?: {
    minYaw?: number;
    maxYaw?: number;
    minPitch?: number;
    maxPitch?: number;
    minFov?: number;
    maxFov?: number;
  };
  effects?: {
    fadeIn?: boolean;
    fadeOut?: boolean;
    transition?: 'none' | 'fade' | 'slide' | 'zoom';
  };
}

export interface Hotspot {
  id: string;
  sceneId: string;
  type: 'navigation' | 'info' | 'media' | 'link' | 'whatsapp' | 'product';
  title?: string;
  description?: string;
  position: Vector3D;
  rotation?: Vector3D;
  scale: Vector3D;
  targetSceneId?: string;
  targetUrl?: string;
  mediaId?: string;
  whatsappNumber?: string;
  whatsappMessage?: string;
  productData?: ProductData;
  styleConfig: HotspotStyle;
  animationConfig: HotspotAnimation;
  isVisible: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Vector3D {
  x: number;
  y: number;
  z: number;
}

export interface ProductData {
  name?: string;
  price?: number;
  description?: string;
  image?: string;
  url?: string;
}

export interface HotspotStyle {
  color?: string;
  size?: number;
  opacity?: number;
  animation?: string;
}

export interface HotspotAnimation {
  type?: string;
  duration?: number;
  delay?: number;
  loop?: boolean;
}

export interface Media {
  id: string;
  userId: string;
  filename: string;
  originalFilename: string;
  filePath: string;
  fileSize?: number;
  mimeType?: string;
  mediaType: 'image_360' | 'video_360' | 'image' | 'video' | 'audio';
  dimensions?: {
    width: number;
    height: number;
  };
  duration?: number;
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  processingMetadata: Record<string, any>;
  thumbnailUrl?: string;
  previewUrl?: string;
  optimizedUrl?: string;
  metadata: MediaMetadata;
  tags?: string[];
  altText?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MediaMetadata {
  camera?: string;
  lens?: string;
  settings?: {
    iso?: number;
    aperture?: string;
    shutterSpeed?: string;
    focalLength?: string;
  };
  location?: {
    latitude?: number;
    longitude?: number;
    altitude?: number;
  };
  projection?: 'equirectangular' | 'cubemap' | 'cylindrical';
  stereo?: 'mono' | 'top-bottom' | 'left-right';
}

export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  planName: string;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  canceledAt?: string;
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
  paystackSubscriptionCode?: string;
  paystackCustomerCode?: string;
  paymentMethod?: 'stripe' | 'paystack' | 'flutterwave';
  amount?: number;
  currency: string;
  trialStart?: string;
  trialEnd?: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Analytics {
  id: string;
  tourId?: string;
  sceneId?: string;
  hotspotId?: string;
  userId?: string;
  sessionId?: string;
  eventType: string;
  eventData: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  referrer?: string;
  country?: string;
  city?: string;
  deviceType?: 'desktop' | 'mobile' | 'tablet' | 'vr';
  browser?: string;
  os?: string;
  timestamp: string;
}

// API Response types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: PaginationMeta;
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Form types
export interface FormState {
  isLoading: boolean;
  errors: Record<string, string>;
  success?: string;
}

// Upload types
export interface UploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
  result?: Media;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  category?: string;
  location?: string;
  state?: string;
  minPrice?: number;
  maxPrice?: number;
  featured?: boolean;
  sortBy?: 'created_at' | 'updated_at' | 'views' | 'likes' | 'title';
  sortOrder?: 'asc' | 'desc';
}

// Navigation types
export interface NavItem {
  title: string;
  href: string;
  icon?: string;
  description?: string;
  external?: boolean;
  disabled?: boolean;
}

export interface SidebarNavItem extends NavItem {
  items?: SidebarNavItem[];
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Webhook types
export interface WebhookEvent {
  id: string;
  type: string;
  data: Record<string, any>;
  timestamp: string;
  signature: string;
  provider: 'stripe' | 'paystack' | 'flutterwave' | 'supabase';
}
