import { z } from 'zod';
import { SUBSCRIPTION_PLANS } from '@/lib/constants';

// Subscription creation validation schema
export const subscriptionCreateSchema = z.object({
  planId: z
    .string()
    .min(1, 'Plan ID is required')
    .refine(
      (planId) => Object.keys(SUBSCRIPTION_PLANS).map(key => SUBSCRIPTION_PLANS[key as keyof typeof SUBSCRIPTION_PLANS].id).includes(planId),
      'Please select a valid subscription plan'
    ),
  paymentMethod: z.enum(['stripe', 'paystack', 'flutterwave']),
  billingCycle: z.enum(['monthly', 'yearly']).default('monthly'),
  couponCode: z
    .string()
    .max(50, 'Coupon code must be less than 50 characters')
    .optional(),
  autoRenew: z.boolean().default(true),
});

// Subscription update validation schema
export const subscriptionUpdateSchema = z.object({
  planId: z
    .string()
    .min(1, 'Plan ID is required')
    .refine(
      (planId) => Object.keys(SUBSCRIPTION_PLANS).map(key => SUBSCRIPTION_PLANS[key as keyof typeof SUBSCRIPTION_PLANS].id).includes(planId),
      'Please select a valid subscription plan'
    )
    .optional(),
  autoRenew: z.boolean().optional(),
  billingCycle: z.enum(['monthly', 'yearly']).optional(),
});

// Payment method validation schema
export const paymentMethodSchema = z.object({
  type: z.enum(['card', 'bank_transfer', 'mobile_money']),
  provider: z.enum(['stripe', 'paystack', 'flutterwave']),
  details: z.object({
    // Card details
    cardNumber: z
      .string()
      .optional()
      .refine(
        (cardNumber) => {
          if (!cardNumber) return true;
          const cleaned = cardNumber.replace(/\D/g, '');
          return cleaned.length >= 13 && cleaned.length <= 19;
        },
        'Please enter a valid card number'
      ),
    expiryMonth: z
      .string()
      .optional()
      .refine(
        (month) => {
          if (!month) return true;
          const monthNum = parseInt(month, 10);
          return monthNum >= 1 && monthNum <= 12;
        },
        'Please enter a valid expiry month'
      ),
    expiryYear: z
      .string()
      .optional()
      .refine(
        (year) => {
          if (!year) return true;
          const yearNum = parseInt(year, 10);
          const currentYear = new Date().getFullYear();
          return yearNum >= currentYear && yearNum <= currentYear + 20;
        },
        'Please enter a valid expiry year'
      ),
    cvv: z
      .string()
      .optional()
      .refine(
        (cvv) => {
          if (!cvv) return true;
          return /^\d{3,4}$/.test(cvv);
        },
        'Please enter a valid CVV'
      ),
    cardholderName: z
      .string()
      .max(100, 'Cardholder name must be less than 100 characters')
      .optional(),
    
    // Bank transfer details
    bankCode: z.string().optional(),
    accountNumber: z
      .string()
      .optional()
      .refine(
        (accountNumber) => {
          if (!accountNumber) return true;
          return /^\d{10}$/.test(accountNumber);
        },
        'Please enter a valid 10-digit account number'
      ),
    
    // Mobile money details
    phoneNumber: z
      .string()
      .optional()
      .refine(
        (phone) => {
          if (!phone) return true;
          const cleaned = phone.replace(/\D/g, '');
          return /^(234|0)?[789][01]\d{8}$/.test(cleaned);
        },
        'Please enter a valid Nigerian phone number'
      ),
  }),
  isDefault: z.boolean().default(false),
  saveForFuture: z.boolean().default(false),
});

// Billing address validation schema
export const billingAddressSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters'),
  email: z
    .string()
    .email('Please enter a valid email address'),
  phone: z
    .string()
    .refine(
      (phone) => {
        const cleaned = phone.replace(/\D/g, '');
        return /^(234|0)?[789][01]\d{8}$/.test(cleaned);
      },
      'Please enter a valid Nigerian phone number'
    ),
  address: z
    .string()
    .min(1, 'Address is required')
    .max(200, 'Address must be less than 200 characters'),
  city: z
    .string()
    .min(1, 'City is required')
    .max(50, 'City must be less than 50 characters'),
  state: z
    .string()
    .min(1, 'State is required')
    .max(50, 'State must be less than 50 characters'),
  postalCode: z
    .string()
    .max(10, 'Postal code must be less than 10 characters')
    .optional(),
  country: z
    .string()
    .min(1, 'Country is required')
    .default('Nigeria'),
});

// Invoice generation validation schema
export const invoiceGenerationSchema = z.object({
  subscriptionId: z.string().min(1, 'Subscription ID is required'),
  billingPeriodStart: z.string().datetime('Invalid billing period start date'),
  billingPeriodEnd: z.string().datetime('Invalid billing period end date'),
  items: z.array(z.object({
    description: z.string().min(1, 'Item description is required'),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
    unitPrice: z.number().min(0, 'Unit price must be positive'),
    total: z.number().min(0, 'Total must be positive'),
  })).min(1, 'At least one item is required'),
  subtotal: z.number().min(0, 'Subtotal must be positive'),
  tax: z.number().min(0, 'Tax must be positive'),
  total: z.number().min(0, 'Total must be positive'),
  currency: z.string().length(3, 'Currency must be a 3-letter code').default('NGN'),
});

// Coupon validation schema
export const couponSchema = z.object({
  code: z
    .string()
    .min(3, 'Coupon code must be at least 3 characters')
    .max(50, 'Coupon code must be less than 50 characters')
    .regex(/^[A-Z0-9_-]+$/, 'Coupon code can only contain uppercase letters, numbers, hyphens, and underscores'),
  type: z.enum(['percentage', 'fixed_amount']),
  value: z.number().min(0, 'Coupon value must be positive'),
  maxUses: z.number().min(1, 'Max uses must be at least 1').optional(),
  maxUsesPerUser: z.number().min(1, 'Max uses per user must be at least 1').optional(),
  minAmount: z.number().min(0, 'Minimum amount must be positive').optional(),
  validFrom: z.string().datetime('Invalid valid from date'),
  validUntil: z.string().datetime('Invalid valid until date'),
  applicablePlans: z.array(z.string()).optional(),
}).refine((data) => {
  return new Date(data.validFrom) < new Date(data.validUntil);
}, {
  message: 'Valid from date must be before valid until date',
  path: ['validUntil'],
}).refine((data) => {
  if (data.type === 'percentage') {
    return data.value <= 100;
  }
  return true;
}, {
  message: 'Percentage discount cannot exceed 100%',
  path: ['value'],
});

// Subscription analytics validation schema
export const subscriptionAnalyticsSchema = z.object({
  startDate: z
    .string()
    .datetime('Invalid start date format')
    .optional(),
  endDate: z
    .string()
    .datetime('Invalid end date format')
    .optional(),
  planId: z.string().optional(),
  status: z.enum(['active', 'canceled', 'past_due', 'unpaid']).optional(),
  paymentMethod: z.enum(['stripe', 'paystack', 'flutterwave']).optional(),
  metrics: z
    .array(z.enum(['revenue', 'subscriptions', 'churn_rate', 'mrr', 'arr']))
    .default(['revenue']),
  granularity: z
    .enum(['day', 'week', 'month', 'quarter', 'year'])
    .default('month'),
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Start date must be before end date',
  path: ['endDate'],
});

// Webhook validation schema
export const webhookSchema = z.object({
  event: z.string().min(1, 'Event type is required'),
  data: z.record(z.any()),
  timestamp: z.string().datetime('Invalid timestamp'),
  signature: z.string().min(1, 'Signature is required'),
  provider: z.enum(['stripe', 'paystack', 'flutterwave']),
});

// Usage tracking validation schema
export const usageTrackingSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  metric: z.enum(['tours_created', 'storage_used', 'bandwidth_used', 'api_calls']),
  value: z.number().min(0, 'Usage value must be positive'),
  timestamp: z.string().datetime('Invalid timestamp').optional(),
  metadata: z.record(z.any()).optional(),
});

// Export types
export type SubscriptionCreateInput = z.infer<typeof subscriptionCreateSchema>;
export type SubscriptionUpdateInput = z.infer<typeof subscriptionUpdateSchema>;
export type PaymentMethodInput = z.infer<typeof paymentMethodSchema>;
export type BillingAddressInput = z.infer<typeof billingAddressSchema>;
export type InvoiceGenerationInput = z.infer<typeof invoiceGenerationSchema>;
export type CouponInput = z.infer<typeof couponSchema>;
export type SubscriptionAnalyticsInput = z.infer<typeof subscriptionAnalyticsSchema>;
export type WebhookInput = z.infer<typeof webhookSchema>;
export type UsageTrackingInput = z.infer<typeof usageTrackingSchema>;
