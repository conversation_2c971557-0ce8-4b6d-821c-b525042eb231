import { Skeleton } from './skeleton';
import { cn } from '@/lib/utils';

interface LoadingProps {
  className?: string;
  variant?: 'default' | 'card' | 'page' | 'button';
  size?: 'sm' | 'md' | 'lg';
}

export function Loading({ className, variant = 'default', size = 'md' }: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4',
    md: 'h-6',
    lg: 'h-8',
  };

  if (variant === 'card') {
    return (
      <div className={cn('space-y-3', className)}>
        <Skeleton className="h-[200px] w-full rounded-lg" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
    );
  }

  if (variant === 'page') {
    return (
      <div className={cn('space-y-6 p-6', className)}>
        <div className="space-y-2">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="h-[200px] w-full rounded-lg" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (variant === 'button') {
    return <Skeleton className={cn('h-9 w-20', className)} />;
  }

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <div className="animate-spin rounded-full border-2 border-gray-300 border-t-primary h-4 w-4" />
      <span className="text-sm text-muted-foreground">Loading...</span>
    </div>
  );
}

// Spinner component for inline loading states
export function Spinner({ className, size = 'md' }: { className?: string; size?: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-6 w-6',
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-gray-300 border-t-primary',
        sizeClasses[size],
        className
      )}
    />
  );
}

// Page loading component
export function PageLoading() {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full border-4 border-gray-300 border-t-primary h-12 w-12 mx-auto" />
        <p className="text-muted-foreground">Loading...</p>
      </div>
    </div>
  );
}

// Full screen loading component
export function FullScreenLoading() {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full border-4 border-gray-300 border-t-primary h-16 w-16 mx-auto" />
        <p className="text-lg font-medium">Loading...</p>
      </div>
    </div>
  );
}
