(()=>{var e={};e.id=680,e.ids=[680],e.modules={1215:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(26373).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,r,t)=>{let{createProxy:s}=t(39844);e.exports=s("C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23103:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=t(65239),n=t(48088),i=t(88170),a=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87578)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\src\\app\\auth\\signin\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},24447:(e,r,t)=>{Promise.resolve().then(t.bind(t,78148)),Promise.resolve().then(t.t.bind(t,85814,23))},26373:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var s=t(61120);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),a=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),l=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:i="",children:a,iconNode:u,...c},p)=>(0,s.createElement)("svg",{ref:p,...d,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:o("lucide",i),...!a&&!l(c)&&{"aria-hidden":"true"},...c},[...u.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(a)?a:[a]])),c=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...i},l)=>(0,s.createElement)(u,{ref:l,iconNode:r,className:o(`lucide-${n(a(e))}`,`lucide-${e}`,t),...i}));return t.displayName=a(e),t}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},50662:(e,r,t)=>{"use strict";t.d(r,{F:()=>a});var s=t(75986);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.$,a=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:o}=r,l=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],s=null==o?void 0:o[e];if(null===r)return null;let i=n(r)||n(s);return a[e][i]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return i(e,l,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...d}[r]):({...o,...d})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77243:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(37413),n=t(61120),i=t(80734),a=t(50662),o=t(10974);let l=(0,a.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.Root,{ref:t,className:(0,o.cn)(l(),e),...r}));d.displayName=i.Root.displayName},78148:(e,r,t)=>{"use strict";t.d(r,{Root:()=>o});var s=t(43210),n=t(14163),i=t(60687),a=s.forwardRef((e,r)=>(0,i.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));a.displayName="Label";var o=a},78593:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var s=t(37413),n=t(61120),i=t(10974);let a=n.forwardRef(({className:e,type:r,...t},n)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...t}));a.displayName="Input"},78963:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>u,ZB:()=>l,Zp:()=>a,aR:()=>o});var s=t(37413),n=t(61120),i=t(10974);let a=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...r}));a.displayName="Card";let o=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let u=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));u.displayName="CardContent",n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80734:(e,r,t)=>{"use strict";t.d(r,{Root:()=>n});var s=t(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call Label() from the server but Label is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-label\\dist\\index.mjs","Label");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call Root() from the server but Root is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\VirtualRealTour\\virtualrealtour_platform\\node_modules\\@radix-ui\\react-label\\dist\\index.mjs","Root")},81630:e=>{"use strict";e.exports=require("http")},82473:(e,r,t)=>{"use strict";t.d(r,{$:()=>p});var s=t(37413),n=t(61120);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var a=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...s}=e;if(n.isValidElement(t)){var a;let e,o,l=(a=t,(o=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(o=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,r){let t={...r};for(let s in r){let n=e[s],i=r[s];/^on[A-Z]/.test(s)?n&&i?t[s]=(...e)=>{let r=i(...e);return n(...e),r}:n&&(t[s]=n):"style"===s?t[s]={...n,...i}:"className"===s&&(t[s]=[n,i].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==n.Fragment&&(d.ref=r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=i(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():i(e[r],null)}}}}(r,l):l),n.cloneElement(t,d)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:i,...a}=e,o=n.Children.toArray(i),d=o.find(l);if(d){let e=d.props.children,i=o.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(r,{...a,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}("Slot"),o=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var d=t(50662),u=t(10974);let c=(0,d.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),p=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...i},o)=>(0,s.jsx)(n?a:"button",{className:(0,u.cn)(c({variant:r,size:t,className:e})),ref:o,...i}));p.displayName="Button"},87578:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,metadata:()=>c});var s=t(37413),n=t(4536),i=t.n(n),a=t(82473),o=t(78963),l=t(78593),d=t(77243),u=t(1215);let c={title:"Sign In",description:"Sign in to your VirtualRealTour account"};function p(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-muted/50 px-4",children:(0,s.jsxs)(o.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(o.aR,{className:"text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("span",{className:"text-2xl font-bold",children:"VirtualRealTour"})]}),(0,s.jsx)(o.ZB,{children:"Welcome back"}),(0,s.jsx)(o.BT,{children:"Sign in to your account to continue creating amazing virtual tours"})]}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(l.p,{id:"email",type:"email",placeholder:"Enter your email",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"password",children:"Password"}),(0,s.jsx)(l.p,{id:"password",type:"password",placeholder:"Enter your password",required:!0})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{id:"remember",type:"checkbox",className:"rounded border-gray-300"}),(0,s.jsx)(d.J,{htmlFor:"remember",className:"text-sm",children:"Remember me"})]}),(0,s.jsx)(i(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:underline",children:"Forgot password?"})]}),(0,s.jsx)(a.$,{className:"w-full",children:"Sign In"}),(0,s.jsxs)("div",{className:"text-center text-sm",children:["Don't have an account?"," ",(0,s.jsx)(i(),{href:"/auth/signup",className:"text-primary hover:underline",children:"Sign up"})]})]})]})})}},91645:e=>{"use strict";e.exports=require("net")},94183:(e,r,t)=>{Promise.resolve().then(t.bind(t,80734)),Promise.resolve().then(t.t.bind(t,4536,23))},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,739,958],()=>t(23103));module.exports=s})();