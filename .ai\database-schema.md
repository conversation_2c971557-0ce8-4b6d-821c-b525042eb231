# Database Schema - VirtualRealTour Platform

## Overview
PostgreSQL database schema designed for scalability, performance, and data integrity with Row Level Security (RLS) for multi-tenant architecture.

## Core Tables

### 1. users
Extended user profiles beyond Supabase Auth
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255),
  avatar_url TEXT,
  phone VARCHAR(20),
  company VARCHAR(255),
  website TEXT,
  bio TEXT,
  location VARCHAR(255),
  subscription_tier VARCHAR(50) DEFAULT 'free',
  subscription_status VARCHAR(50) DEFAULT 'active',
  subscription_expires_at TIMESTAMPTZ,
  total_tours INTEGER DEFAULT 0,
  total_views INTEGER DEFAULT 0,
  is_verified BOOLEAN DEFAULT FALSE,
  is_admin BOOLEAN DEFAULT FALSE,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 2. tours
Virtual tour metadata and configuration
```sql
CREATE TABLE tours (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  slug VARCHAR(255) UNIQUE,
  thumbnail_url TEXT,
  category VARCHAR(100),
  tags TEXT[],
  location VARCHAR(255),
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  address TEXT,
  price DECIMAL(12, 2),
  currency VARCHAR(3) DEFAULT 'NGN',
  status VARCHAR(50) DEFAULT 'draft', -- draft, published, archived, pending_review
  visibility VARCHAR(50) DEFAULT 'public', -- public, private, unlisted
  featured BOOLEAN DEFAULT FALSE,
  featured_until TIMESTAMPTZ,
  total_scenes INTEGER DEFAULT 0,
  total_views INTEGER DEFAULT 0,
  total_likes INTEGER DEFAULT 0,
  total_shares INTEGER DEFAULT 0,
  settings JSONB DEFAULT '{}', -- tour-specific settings
  seo_title VARCHAR(255),
  seo_description TEXT,
  seo_keywords TEXT[],
  published_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 3. scenes
Individual 360° scenes within tours
```sql
CREATE TABLE scenes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tour_id UUID NOT NULL REFERENCES tours(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  media_id UUID REFERENCES media(id),
  order_index INTEGER NOT NULL,
  is_starting_scene BOOLEAN DEFAULT FALSE,
  position JSONB, -- {x, y, z} coordinates
  rotation JSONB, -- {x, y, z} rotation
  settings JSONB DEFAULT '{}', -- scene-specific settings
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(tour_id, order_index)
);
```

### 4. hotspots
Interactive elements within scenes
```sql
CREATE TABLE hotspots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL, -- navigation, info, media, link, whatsapp, product
  title VARCHAR(255),
  description TEXT,
  position JSONB NOT NULL, -- {x, y, z} 3D coordinates
  rotation JSONB, -- {x, y, z} rotation
  scale JSONB DEFAULT '{"x": 1, "y": 1, "z": 1}',
  target_scene_id UUID REFERENCES scenes(id),
  target_url TEXT,
  media_id UUID REFERENCES media(id),
  whatsapp_number VARCHAR(20),
  whatsapp_message TEXT,
  product_data JSONB, -- product information for e-commerce
  style_config JSONB DEFAULT '{}', -- visual styling
  animation_config JSONB DEFAULT '{}', -- animation settings
  is_visible BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 5. media
360° images, videos, and other assets
```sql
CREATE TABLE media (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  filename VARCHAR(255) NOT NULL,
  original_filename VARCHAR(255),
  file_path TEXT NOT NULL,
  file_size BIGINT,
  mime_type VARCHAR(100),
  media_type VARCHAR(50), -- image_360, video_360, audio, image, video
  dimensions JSONB, -- {width, height}
  duration INTEGER, -- for videos/audio in seconds
  processing_status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, failed
  processing_metadata JSONB DEFAULT '{}',
  thumbnail_url TEXT,
  preview_url TEXT,
  optimized_url TEXT,
  metadata JSONB DEFAULT '{}', -- EXIF, camera info, etc.
  tags TEXT[],
  alt_text TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 6. subscriptions
User subscription plans and billing
```sql
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  plan_id VARCHAR(100) NOT NULL,
  plan_name VARCHAR(100) NOT NULL,
  status VARCHAR(50) NOT NULL, -- active, canceled, past_due, unpaid
  current_period_start TIMESTAMPTZ NOT NULL,
  current_period_end TIMESTAMPTZ NOT NULL,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  canceled_at TIMESTAMPTZ,
  stripe_subscription_id VARCHAR(255),
  stripe_customer_id VARCHAR(255),
  paystack_subscription_code VARCHAR(255),
  paystack_customer_code VARCHAR(255),
  payment_method VARCHAR(50), -- stripe, paystack, flutterwave
  amount DECIMAL(12, 2),
  currency VARCHAR(3) DEFAULT 'NGN',
  trial_start TIMESTAMPTZ,
  trial_end TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id)
);
```

### 7. analytics
Tour views and user interaction tracking
```sql
CREATE TABLE analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tour_id UUID REFERENCES tours(id) ON DELETE CASCADE,
  scene_id UUID REFERENCES scenes(id) ON DELETE CASCADE,
  hotspot_id UUID REFERENCES hotspots(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  session_id VARCHAR(255),
  event_type VARCHAR(100) NOT NULL, -- view, click, share, like, time_spent
  event_data JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  referrer TEXT,
  country VARCHAR(2),
  city VARCHAR(100),
  device_type VARCHAR(50), -- desktop, mobile, tablet
  browser VARCHAR(50),
  os VARCHAR(50),
  timestamp TIMESTAMPTZ DEFAULT NOW()
);
```

## Supporting Tables

### 8. categories
Tour categories for organization
```sql
CREATE TABLE categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) UNIQUE NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  icon VARCHAR(100),
  color VARCHAR(7), -- hex color
  parent_id UUID REFERENCES categories(id),
  order_index INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 9. tour_likes
User likes for tours
```sql
CREATE TABLE tour_likes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tour_id UUID NOT NULL REFERENCES tours(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(tour_id, user_id)
);
```

### 10. tour_shares
Tour sharing tracking
```sql
CREATE TABLE tour_shares (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tour_id UUID NOT NULL REFERENCES tours(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  platform VARCHAR(50), -- whatsapp, facebook, twitter, email, link
  shared_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 11. notifications
User notifications system
```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  type VARCHAR(100) NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT,
  data JSONB DEFAULT '{}',
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Indexes for Performance

```sql
-- Tours
CREATE INDEX idx_tours_user_id ON tours(user_id);
CREATE INDEX idx_tours_status ON tours(status);
CREATE INDEX idx_tours_category ON tours(category);
CREATE INDEX idx_tours_featured ON tours(featured) WHERE featured = TRUE;
CREATE INDEX idx_tours_location ON tours USING GIST(ll_to_earth(latitude, longitude));

-- Scenes
CREATE INDEX idx_scenes_tour_id ON scenes(tour_id);
CREATE INDEX idx_scenes_order ON scenes(tour_id, order_index);

-- Hotspots
CREATE INDEX idx_hotspots_scene_id ON hotspots(scene_id);
CREATE INDEX idx_hotspots_type ON hotspots(type);

-- Media
CREATE INDEX idx_media_user_id ON media(user_id);
CREATE INDEX idx_media_type ON media(media_type);
CREATE INDEX idx_media_status ON media(processing_status);

-- Analytics
CREATE INDEX idx_analytics_tour_id ON analytics(tour_id);
CREATE INDEX idx_analytics_timestamp ON analytics(timestamp);
CREATE INDEX idx_analytics_event_type ON analytics(event_type);
```

## Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE tours ENABLE ROW LEVEL SECURITY;
ALTER TABLE scenes ENABLE ROW LEVEL SECURITY;
ALTER TABLE hotspots ENABLE ROW LEVEL SECURITY;
ALTER TABLE media ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Users can only see and edit their own data
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Tours visibility based on status and ownership
CREATE POLICY "Users can view own tours" ON tours
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Public can view published tours" ON tours
  FOR SELECT USING (status = 'published' AND visibility = 'public');

CREATE POLICY "Users can manage own tours" ON tours
  FOR ALL USING (auth.uid() = user_id);
```

## Functions and Triggers

```sql
-- Update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tours_updated_at BEFORE UPDATE ON tours
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

This schema provides a robust foundation for the VirtualRealTour platform with proper relationships, indexing, and security measures.
