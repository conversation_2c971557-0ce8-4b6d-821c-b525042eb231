# Supabase Setup Guide for VirtualRealTour Platform

This guide will walk you through setting up Supabase for the VirtualRealTour platform using the **App Frameworks (Next.js Integration)** approach.

## 🎯 Why App Frameworks Integration?

After analyzing all Supabase connection options, the **App Frameworks (Next.js Integration)** is the optimal choice because:

✅ **Perfect Architecture Match**: Designed specifically for Next.js 14 App Router  
✅ **Complete Feature Set**: Authentication, database, storage, and real-time in one package  
✅ **Type Safety**: Auto-generated TypeScript types from your database schema  
✅ **Performance**: Optimized for SSR/SSG with proper caching  
✅ **Developer Experience**: Minimal boilerplate, maximum functionality  

## 🚀 Step-by-Step Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/login
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: VirtualRealTour Platform
   - **Database Password**: Use a strong password (save it!)
   - **Region**: Choose closest to Nigeria (Europe West recommended)
5. Click "Create new project"
6. Wait for project initialization (2-3 minutes)

### 2. Get Project Credentials

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Project API Keys** → `anon` `public` key
   - **Project API Keys** → `service_role` `secret` key

### 3. Configure Environment Variables

Update your `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Feature Flags
NEXT_PUBLIC_ENABLE_VR=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_PAYMENTS=true
NEXT_PUBLIC_ENABLE_WHATSAPP=true
```

### 4. Set Up Database Schema

1. Go to **SQL Editor** in your Supabase dashboard
2. Copy and paste the contents of `supabase/schema.sql`
3. Click "Run" to execute the schema
4. Verify tables are created in **Table Editor**

### 5. Set Up Storage Buckets

1. Go to **SQL Editor** in your Supabase dashboard
2. Copy and paste the contents of `supabase/storage-setup.sql`
3. Click "Run" to execute the storage setup
4. Verify buckets are created in **Storage**

### 6. Configure Authentication

1. Go to **Authentication** → **Settings**
2. Configure **Site URL**: `http://localhost:3000` (for development)
3. Add **Redirect URLs**:
   - `http://localhost:3000/auth/callback`
   - `https://yourdomain.com/auth/callback` (for production)

#### Enable Auth Providers (Optional)

**Google OAuth:**
1. Go to **Authentication** → **Providers**
2. Enable Google
3. Add your Google OAuth credentials

**GitHub OAuth:**
1. Enable GitHub
2. Add your GitHub OAuth credentials

### 7. Generate TypeScript Types

Run the type generation script:

```bash
npm run db:generate-types
```

Or manually:
```bash
supabase gen types typescript --project-id YOUR_PROJECT_ID --schema public > src/types/supabase.ts
```

### 8. Test the Connection

Create a simple test to verify everything works:

```typescript
// test-supabase.ts
import { supabase } from '@/lib/supabase/client';

async function testConnection() {
  try {
    const { data, error } = await supabase.from('users').select('count');
    if (error) throw error;
    console.log('✅ Supabase connection successful!');
  } catch (error) {
    console.error('❌ Supabase connection failed:', error);
  }
}

testConnection();
```

## 🔧 Configuration Details

### Row Level Security (RLS)

The schema includes comprehensive RLS policies:

- **Users**: Can only view/edit their own profile
- **Tours**: Public tours visible to all, private tours only to owners
- **Scenes/Hotspots**: Inherit permissions from parent tour
- **Media Files**: Users can only manage their own files
- **Analytics**: Insert-only for tracking, read-only for tour owners

### Storage Buckets

| Bucket | Purpose | Size Limit | Public |
|--------|---------|------------|--------|
| `media` | 360° images/videos | 100MB | Yes |
| `avatars` | User profile pictures | 5MB | Yes |
| `thumbnails` | Generated thumbnails | 2MB | Yes |
| `temp` | Processing files | 100MB | No |

### Database Functions

The schema includes helpful functions:

- `increment_tour_views()` - Safely increment view counts
- `increment_tour_likes()` - Safely increment like counts
- `generate_unique_slug()` - Generate unique tour slugs
- `handle_new_user()` - Auto-create user profile on signup

## 📝 Usage Examples

### Authentication

```typescript
import { useAuth } from '@/hooks/use-supabase';

function MyComponent() {
  const { user, profile, loading, signOut } = useAuth();
  
  if (loading) return <div>Loading...</div>;
  if (!user) return <div>Please sign in</div>;
  
  return (
    <div>
      <h1>Welcome, {profile?.full_name}!</h1>
      <button onClick={signOut}>Sign Out</button>
    </div>
  );
}
```

### Database Operations

```typescript
import { clientOperations } from '@/lib/supabase/operations';

// Get published tours
const tours = await clientOperations.tours.getPublished();

// Create a new tour
const newTour = await clientOperations.tours.create({
  title: 'My Amazing Tour',
  description: 'A beautiful virtual tour',
  category: 'Real Estate',
});

// Upload media file
const { uploadFile } = useFileUpload();
const mediaFile = await uploadFile(file, { tourId: tour.id });
```

### Real-time Subscriptions

```typescript
import { useRealtimeSubscription } from '@/hooks/use-supabase';

function TourList() {
  const tours = useRealtimeSubscription('tours', 'status=eq.published');
  
  return (
    <div>
      {tours.map(tour => (
        <div key={tour.id}>{tour.title}</div>
      ))}
    </div>
  );
}
```

## 🔒 Security Best Practices

1. **Never expose service role key** in client-side code
2. **Use RLS policies** for all data access
3. **Validate all inputs** on both client and server
4. **Use signed URLs** for private file access
5. **Implement rate limiting** for API endpoints
6. **Regular security audits** of your policies

## 🚀 Production Deployment

### Environment Variables for Production

```env
# Production Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key

# Production App Configuration
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NODE_ENV=production
```

### Additional Production Setup

1. **Configure custom domain** in Supabase settings
2. **Set up SSL certificates** for your domain
3. **Configure CORS** for your production domain
4. **Set up monitoring** and error tracking
5. **Configure backup policies** for your database

## 🆘 Troubleshooting

### Common Issues

**Connection Errors:**
- Verify environment variables are correct
- Check if Supabase project is active
- Ensure network connectivity

**Authentication Issues:**
- Check redirect URLs configuration
- Verify auth provider settings
- Clear browser cache/cookies

**Permission Errors:**
- Review RLS policies
- Check user roles and permissions
- Verify table ownership

**File Upload Issues:**
- Check storage bucket policies
- Verify file size limits
- Ensure correct MIME types

### Getting Help

- 📧 **Email**: <EMAIL>
- 📚 **Supabase Docs**: [supabase.com/docs](https://supabase.com/docs)
- 💬 **Discord**: [Supabase Community](https://discord.supabase.com)

---

**Next Steps**: Once Supabase is configured, you can start building the advanced tour builder, payment integration, and analytics dashboard! 🎉
