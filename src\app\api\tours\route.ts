import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { z } from 'zod';
import type { Database } from '@/types/supabase';
import { handleAPIError } from '@/lib/api/error-handler';

// Helper function to create SSR client for API routes
async function createAPIClient() {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
}

// Validation schema for tour creation
const createTourSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().optional(),
  category: z.string().optional(),
  location: z.string().optional(),
  address: z.string().optional(),
  price: z.number().optional(),
  currency: z.string().default('NGN'),
  is_public: z.boolean().default(true),
  password_protected: z.boolean().default(false),
  password: z.string().optional(),
});

// GET /api/tours - Get published tours
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const category = searchParams.get('category');
    const featured = searchParams.get('featured') === 'true';
    const search = searchParams.get('search');

    // Use new SSR client
    const supabase = await createAPIClient();

    let query = supabase
      .from('tours')
      .select(`
        *,
        users:user_id (
          full_name,
          avatar_url,
          company
        ),
        scenes:scenes (
          id,
          title,
          thumbnail_url
        )
      `)
      .eq('status', 'published')
      .eq('is_public', true);

    // Apply filters
    if (category) {
      query = query.eq('category', category);
    }

    if (featured) {
      query = query.eq('is_featured', true);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(from, to);

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      data: {
        tours: data,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      },
    });
  } catch (error) {
    return handleAPIError(error);
  }
}

// POST /api/tours - Create new tour
export async function POST(request: NextRequest) {
  try {
    // Use new SSR client
    const supabase = await createAPIClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: { message: 'Authentication required' } },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createTourSchema.parse(body);

    // Generate unique slug
    const baseSlug = validatedData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    const { data: slugData, error: slugError } = await supabase
      .rpc('generate_unique_slug', { base_slug: baseSlug });

    if (slugError) {
      throw slugError;
    }

    // Hash password if provided
    let passwordHash = null;
    if (validatedData.password_protected && validatedData.password) {
      const bcrypt = await import('bcryptjs');
      passwordHash = await bcrypt.hash(validatedData.password, 10);
    }

    // Create tour
    const { data: tour, error: createError } = await supabase
      .from('tours')
      .insert({
        ...validatedData,
        user_id: user.id,
        slug: slugData,
        password_hash: passwordHash,
        status: 'draft',
      })
      .select()
      .single();

    if (createError) {
      throw createError;
    }

    return NextResponse.json({
      success: true,
      data: tour,
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'Validation failed',
            details: error.errors,
          },
        },
        { status: 400 }
      );
    }

    return handleAPIError(error);
  }
}
