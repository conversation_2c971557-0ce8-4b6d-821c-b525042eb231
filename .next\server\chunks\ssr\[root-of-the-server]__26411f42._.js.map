{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/VirtualRealTour/virtualrealtour_platform/src/components/providers.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs';\nimport type { User } from '@supabase/auth-helpers-nextjs';\nimport type { Database } from '@/types/supabase';\n\n// Theme Provider\ninterface ThemeContextType {\n  theme: 'light' | 'dark' | 'system';\n  setTheme: (theme: 'light' | 'dark' | 'system') => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\nfunction ThemeProvider({ children }: { children: React.ReactNode }) {\n  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system');\n\n  useEffect(() => {\n    const stored = localStorage.getItem('theme') as 'light' | 'dark' | 'system';\n    if (stored) {\n      setTheme(stored);\n    }\n  }, []);\n\n  useEffect(() => {\n    localStorage.setItem('theme', theme);\n    \n    const root = window.document.documentElement;\n    root.classList.remove('light', 'dark');\n\n    if (theme === 'system') {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches\n        ? 'dark'\n        : 'light';\n      root.classList.add(systemTheme);\n    } else {\n      root.classList.add(theme);\n    }\n  }, [theme]);\n\n  return (\n    <ThemeContext.Provider value={{ theme, setTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\n// Auth Provider\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  signOut: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\nfunction AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const supabase = createClientComponentClient<Database>();\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser();\n      setUser(user);\n      setLoading(false);\n    };\n\n    getUser();\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null);\n        setLoading(false);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, [supabase.auth]);\n\n  const signOut = async () => {\n    await supabase.auth.signOut();\n  };\n\n  return (\n    <AuthContext.Provider value={{ user, loading, signOut }}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\n// Toast Provider (placeholder for now)\nfunction ToastProvider({ children }: { children: React.ReactNode }) {\n  return <>{children}</>;\n}\n\n// Main Providers component\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <ThemeProvider>\n      <AuthProvider>\n        <ToastProvider>\n          {children}\n        </ToastProvider>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAaA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEA,SAAS,cAAc,EAAE,QAAQ,EAAiC;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,SAAS;QACX;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,SAAS;QAE9B,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;QAC5C,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;QAE/B,IAAI,UAAU,UAAU;YACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GACzE,SACA;YACJ,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB;IACF,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAS;kBAC7C;;;;;;AAGP;AASA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEA,SAAS,aAAa,EAAE,QAAQ,EAAiC;IAC/D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,wKAAA,CAAA,8BAA2B,AAAD;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,QAAQ;YACR,WAAW;QACb;QAEA;QAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC,SAAS,IAAI;KAAC;IAElB,MAAM,UAAU;QACd,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7B;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;QAAQ;kBACnD;;;;;;AAGP;AAEA,uCAAuC;AACvC,SAAS,cAAc,EAAE,QAAQ,EAAiC;IAChE,qBAAO;kBAAG;;AACZ;AAGO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBACC,cAAA,8OAAC;0BACE;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}