import { z } from 'zod';
import { NIGERIAN_STATES, LANGUAGES, THEME_OPTIONS } from '@/lib/constants';

// User preferences validation schema
export const userPreferencesSchema = z.object({
  theme: z
    .enum(['light', 'dark', 'system'])
    .default('system'),
  language: z
    .string()
    .refine(
      (lang) => LANGUAGES.some(l => l.code === lang),
      'Please select a valid language'
    )
    .default('en'),
  notifications: z.object({
    email: z.boolean().default(true),
    push: z.boolean().default(true),
    marketing: z.boolean().default(false),
    tourViews: z.boolean().default(true),
    tourLikes: z.boolean().default(true),
    tourComments: z.boolean().default(true),
    systemUpdates: z.boolean().default(true),
  }).default({}),
  privacy: z.object({
    profileVisibility: z.enum(['public', 'private']).default('public'),
    showEmail: z.boolean().default(false),
    showPhone: z.boolean().default(false),
    allowAnalytics: z.boolean().default(true),
    allowMarketing: z.boolean().default(false),
  }).default({}),
  display: z.object({
    autoplay: z.boolean().default(false),
    quality: z.enum(['low', 'medium', 'high', 'auto']).default('auto'),
    showTutorials: z.boolean().default(true),
    compactMode: z.boolean().default(false),
  }).default({}),
});

// User profile update validation schema
export const userProfileUpdateSchema = z.object({
  fullName: z
    .string()
    .min(2, 'Full name must be at least 2 characters long')
    .max(100, 'Full name must be less than 100 characters'),
  phone: z
    .string()
    .optional()
    .refine(
      (phone) => {
        if (!phone) return true;
        const cleaned = phone.replace(/\D/g, '');
        return /^(234|0)?[789][01]\d{8}$/.test(cleaned);
      },
      'Please enter a valid Nigerian phone number'
    ),
  company: z
    .string()
    .max(100, 'Company name must be less than 100 characters')
    .optional(),
  website: z
    .string()
    .url('Please enter a valid website URL')
    .optional()
    .or(z.literal('')),
  bio: z
    .string()
    .max(500, 'Bio must be less than 500 characters')
    .optional(),
  location: z
    .string()
    .max(100, 'Location must be less than 100 characters')
    .optional(),
  state: z
    .string()
    .optional()
    .refine(
      (state) => {
        if (!state) return true;
        return NIGERIAN_STATES.includes(state as any);
      },
      'Please select a valid Nigerian state'
    ),
  socialLinks: z.object({
    facebook: z.string().url().optional().or(z.literal('')),
    twitter: z.string().url().optional().or(z.literal('')),
    linkedin: z.string().url().optional().or(z.literal('')),
    instagram: z.string().url().optional().or(z.literal('')),
    youtube: z.string().url().optional().or(z.literal('')),
  }).optional(),
});

// Avatar upload validation schema
export const avatarUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine(
      (file) => file.size <= 5 * 1024 * 1024, // 5MB
      'Avatar file size must be less than 5MB'
    )
    .refine(
      (file) => ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(file.type),
      'Avatar must be a JPEG, PNG, or WebP image'
    ),
});

// User search validation schema
export const userSearchSchema = z.object({
  query: z
    .string()
    .max(100, 'Search query must be less than 100 characters')
    .optional(),
  state: z
    .string()
    .optional()
    .refine(
      (state) => {
        if (!state) return true;
        return NIGERIAN_STATES.includes(state as any);
      },
      'Please select a valid Nigerian state'
    ),
  verified: z
    .boolean()
    .optional(),
  subscriptionTier: z
    .enum(['free', 'pro', 'enterprise'])
    .optional(),
  sortBy: z
    .enum(['created_at', 'updated_at', 'full_name', 'total_tours', 'total_views'])
    .default('created_at'),
  sortOrder: z
    .enum(['asc', 'desc'])
    .default('desc'),
  page: z
    .number()
    .min(1, 'Page must be at least 1')
    .default(1),
  limit: z
    .number()
    .min(1, 'Limit must be at least 1')
    .max(50, 'Limit must be at most 50')
    .default(20),
});

// User verification request validation schema
export const userVerificationSchema = z.object({
  type: z.enum(['email', 'phone', 'identity']),
  documents: z
    .array(z.instanceof(File))
    .max(5, 'Maximum 5 documents allowed')
    .optional(),
  notes: z
    .string()
    .max(500, 'Notes must be less than 500 characters')
    .optional(),
});

// User blocking/reporting validation schema
export const userReportSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  reason: z.enum([
    'spam',
    'harassment',
    'inappropriate_content',
    'copyright_violation',
    'fake_profile',
    'other'
  ]),
  description: z
    .string()
    .min(10, 'Please provide more details about the report')
    .max(1000, 'Description must be less than 1000 characters'),
  evidence: z
    .array(z.instanceof(File))
    .max(5, 'Maximum 5 evidence files allowed')
    .optional(),
});

// User follow/unfollow validation schema
export const userFollowSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  action: z.enum(['follow', 'unfollow']),
});

// User analytics validation schema
export const userAnalyticsSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  startDate: z
    .string()
    .datetime('Invalid start date format')
    .optional(),
  endDate: z
    .string()
    .datetime('Invalid end date format')
    .optional(),
  metrics: z
    .array(z.enum(['profile_views', 'tour_views', 'followers', 'following', 'engagement']))
    .default(['profile_views']),
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Start date must be before end date',
  path: ['endDate'],
});

// User notification settings validation schema
export const notificationSettingsSchema = z.object({
  email: z.object({
    tourViews: z.boolean().default(true),
    tourLikes: z.boolean().default(true),
    tourComments: z.boolean().default(true),
    newFollowers: z.boolean().default(true),
    systemUpdates: z.boolean().default(true),
    marketing: z.boolean().default(false),
    weeklyDigest: z.boolean().default(true),
  }),
  push: z.object({
    tourViews: z.boolean().default(true),
    tourLikes: z.boolean().default(true),
    tourComments: z.boolean().default(true),
    newFollowers: z.boolean().default(true),
    systemUpdates: z.boolean().default(true),
  }),
  frequency: z.object({
    email: z.enum(['immediate', 'daily', 'weekly', 'never']).default('daily'),
    push: z.enum(['immediate', 'daily', 'never']).default('immediate'),
  }),
});

// User privacy settings validation schema
export const privacySettingsSchema = z.object({
  profileVisibility: z.enum(['public', 'private']).default('public'),
  showEmail: z.boolean().default(false),
  showPhone: z.boolean().default(false),
  showLocation: z.boolean().default(true),
  showSocialLinks: z.boolean().default(true),
  allowDirectMessages: z.boolean().default(true),
  allowFollowing: z.boolean().default(true),
  allowAnalytics: z.boolean().default(true),
  allowMarketing: z.boolean().default(false),
  dataRetention: z.enum(['1year', '2years', '5years', 'forever']).default('2years'),
});

// Export types
export type UserPreferencesInput = z.infer<typeof userPreferencesSchema>;
export type UserProfileUpdateInput = z.infer<typeof userProfileUpdateSchema>;
export type AvatarUploadInput = z.infer<typeof avatarUploadSchema>;
export type UserSearchInput = z.infer<typeof userSearchSchema>;
export type UserVerificationInput = z.infer<typeof userVerificationSchema>;
export type UserReportInput = z.infer<typeof userReportSchema>;
export type UserFollowInput = z.infer<typeof userFollowSchema>;
export type UserAnalyticsInput = z.infer<typeof userAnalyticsSchema>;
export type NotificationSettingsInput = z.infer<typeof notificationSettingsSchema>;
export type PrivacySettingsInput = z.infer<typeof privacySettingsSchema>;
