import { z } from 'zod';

// Define the schema for environment variables
const envSchema = z.object({
  // App Configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:3000'),
  NEXT_PUBLIC_API_URL: z.string().url().optional(),

  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: z.string().url().optional(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1).optional(),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1).optional(),
  DATABASE_URL: z.string().url().optional(),

  // Authentication
  NEXTAUTH_SECRET: z.string().min(32).optional(),
  NEXTAUTH_URL: z.string().url().optional(),

  // File Storage
  NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET: z.string().default('media'),
  SUPABASE_STORAGE_URL: z.string().url().optional(),

  // Payment Providers - Stripe
  STRIPE_SECRET_KEY: z.string().optional(),
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),

  // Payment Providers - Paystack
  PAYSTACK_SECRET_KEY: z.string().optional(),
  NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY: z.string().optional(),

  // Payment Providers - Flutterwave
  FLUTTERWAVE_SECRET_KEY: z.string().optional(),
  NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY: z.string().optional(),

  // WhatsApp Business API
  WHATSAPP_ACCESS_TOKEN: z.string().optional(),
  WHATSAPP_PHONE_NUMBER_ID: z.string().optional(),
  WHATSAPP_WEBHOOK_VERIFY_TOKEN: z.string().optional(),

  // Google Services
  GOOGLE_MAPS_API_KEY: z.string().optional(),
  GOOGLE_ANALYTICS_ID: z.string().optional(),
  GOOGLE_SITE_VERIFICATION: z.string().optional(),

  // Email Services
  RESEND_API_KEY: z.string().optional(),
  SENDGRID_API_KEY: z.string().optional(),

  // Analytics & Monitoring
  SENTRY_DSN: z.string().url().optional(),
  NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),

  // Feature Flags
  NEXT_PUBLIC_ENABLE_VR: z.string().transform(val => val === 'true').default('true'),
  NEXT_PUBLIC_ENABLE_ANALYTICS: z.string().transform(val => val === 'true').default('true'),
  NEXT_PUBLIC_ENABLE_PAYMENTS: z.string().transform(val => val === 'true').default('true'),
  NEXT_PUBLIC_ENABLE_WHATSAPP: z.string().transform(val => val === 'true').default('true'),

  // Rate Limiting
  UPSTASH_REDIS_REST_URL: z.string().url().optional(),
  UPSTASH_REDIS_REST_TOKEN: z.string().optional(),

  // File Upload Limits
  MAX_FILE_SIZE: z.string().default('100MB'),
  ALLOWED_FILE_TYPES: z.string().default('image/jpeg,image/png,image/webp,video/mp4,video/webm'),

  // Tour Limits by Plan
  FREE_PLAN_TOUR_LIMIT: z.string().transform(Number).default('3'),
  PRO_PLAN_TOUR_LIMIT: z.string().transform(Number).default('50'),
  ENTERPRISE_PLAN_TOUR_LIMIT: z.string().default('unlimited'),
});

// Parse and validate environment variables
function validateEnv() {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors
        .filter(err => err.code === 'invalid_type' && err.received === 'undefined')
        .map(err => err.path.join('.'));

      const invalidVars = error.errors
        .filter(err => err.code !== 'invalid_type' || err.received !== 'undefined')
        .map(err => `${err.path.join('.')}: ${err.message}`);

      let errorMessage = 'Environment validation failed:\n';

      if (missingVars.length > 0) {
        errorMessage += `\nMissing required variables:\n${missingVars.map(v => `  - ${v}`).join('\n')}`;
      }

      if (invalidVars.length > 0) {
        errorMessage += `\nInvalid variables:\n${invalidVars.map(v => `  - ${v}`).join('\n')}`;
      }

      // In development, warn but don't fail for missing optional variables
      if (process.env.NODE_ENV === 'development') {
        console.warn(errorMessage);
        console.warn('Running in development mode with partial configuration.');
        // Return a safe default configuration
        return envSchema.parse({
          ...process.env,
          NODE_ENV: 'development',
          NEXT_PUBLIC_APP_URL: 'http://localhost:3000',
        });
      }

      throw new Error(errorMessage);
    }
    throw error;
  }
}

// Export validated environment variables
export const env = validateEnv();

// Type for the validated environment
export type Env = z.infer<typeof envSchema>;

// Helper functions for environment checks
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';

// Service availability helpers
export const services = {
  supabase: {
    available: !!(env.NEXT_PUBLIC_SUPABASE_URL && env.NEXT_PUBLIC_SUPABASE_ANON_KEY),
    url: env.NEXT_PUBLIC_SUPABASE_URL,
    anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,
  },
} as const;

// Feature flag helpers
export const features = {
  vr: env.NEXT_PUBLIC_ENABLE_VR,
  analytics: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
  payments: env.NEXT_PUBLIC_ENABLE_PAYMENTS,
  whatsapp: env.NEXT_PUBLIC_ENABLE_WHATSAPP,
} as const;

// Payment provider availability
export const paymentProviders = {
  stripe: {
    available: !!(env.STRIPE_SECRET_KEY && env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY),
    secretKey: env.STRIPE_SECRET_KEY,
    publishableKey: env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    webhookSecret: env.STRIPE_WEBHOOK_SECRET,
  },
  paystack: {
    available: !!(env.PAYSTACK_SECRET_KEY && env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY),
    secretKey: env.PAYSTACK_SECRET_KEY,
    publicKey: env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,
  },
  flutterwave: {
    available: !!(env.FLUTTERWAVE_SECRET_KEY && env.NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY),
    secretKey: env.FLUTTERWAVE_SECRET_KEY,
    publicKey: env.NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY,
  },
} as const;

// Email provider availability
export const emailProviders = {
  resend: {
    available: !!env.RESEND_API_KEY,
    apiKey: env.RESEND_API_KEY,
  },
  sendgrid: {
    available: !!env.SENDGRID_API_KEY,
    apiKey: env.SENDGRID_API_KEY,
  },
} as const;

// WhatsApp availability
export const whatsapp = {
  available: !!(env.WHATSAPP_ACCESS_TOKEN && env.WHATSAPP_PHONE_NUMBER_ID),
  accessToken: env.WHATSAPP_ACCESS_TOKEN,
  phoneNumberId: env.WHATSAPP_PHONE_NUMBER_ID,
  webhookVerifyToken: env.WHATSAPP_WEBHOOK_VERIFY_TOKEN,
} as const;

// Google services availability
export const googleServices = {
  maps: {
    available: !!env.GOOGLE_MAPS_API_KEY,
    apiKey: env.GOOGLE_MAPS_API_KEY,
  },
  analytics: {
    available: !!env.GOOGLE_ANALYTICS_ID,
    id: env.GOOGLE_ANALYTICS_ID,
  },
} as const;

// Monitoring availability
export const monitoring = {
  sentry: {
    available: !!env.SENTRY_DSN,
    dsn: env.SENTRY_DSN,
    publicDsn: env.NEXT_PUBLIC_SENTRY_DSN,
  },
} as const;

// Rate limiting availability
export const rateLimiting = {
  available: !!(env.UPSTASH_REDIS_REST_URL && env.UPSTASH_REDIS_REST_TOKEN),
  url: env.UPSTASH_REDIS_REST_URL,
  token: env.UPSTASH_REDIS_REST_TOKEN,
} as const;

// File upload configuration
export const fileUpload = {
  maxSize: env.MAX_FILE_SIZE,
  allowedTypes: env.ALLOWED_FILE_TYPES.split(','),
} as const;

// Plan limits
export const planLimits = {
  free: {
    tours: env.FREE_PLAN_TOUR_LIMIT,
  },
  pro: {
    tours: env.PRO_PLAN_TOUR_LIMIT,
  },
  enterprise: {
    tours: env.ENTERPRISE_PLAN_TOUR_LIMIT === 'unlimited' ? Infinity : Number(env.ENTERPRISE_PLAN_TOUR_LIMIT),
  },
} as const;

// Validate critical environment variables on startup
if (isProduction) {
  const criticalVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  ];

  const missing = criticalVars.filter(varName => !env[varName as keyof typeof env]);

  if (missing.length > 0) {
    throw new Error(
      `Critical environment variables missing in production: ${missing.join(', ')}`
    );
  }
}
