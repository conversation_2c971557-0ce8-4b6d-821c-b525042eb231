import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';
import { handleAPIError } from '@/lib/api/error-handler';
import { validateFile } from '@/lib/utils/file';

// Helper function to create SSR client for API routes
async function createAPIClient() {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
}

// POST /api/upload - Upload media files
export async function POST(request: NextRequest) {
  try {
    // Use new SSR client
    const supabase = await createAPIClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: { message: 'Authentication required' } },
        { status: 401 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const tourId = formData.get('tourId') as string;
    const bucket = formData.get('bucket') as string || 'media';

    if (!file) {
      return NextResponse.json(
        { success: false, error: { message: 'No file provided' } },
        { status: 400 }
      );
    }

    // Validate file
    const validation = await validateFile(file, {
      maxSize: 100, // 100MB
      allowedTypes: [
        'image/jpeg',
        'image/png',
        'image/webp',
        'video/mp4',
        'video/webm',
      ],
    });

    if (!validation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'File validation failed',
            details: validation.errors,
          },
        },
        { status: 400 }
      );
    }

    // Generate unique filename
    const fileExt = file.name.split('.').pop();
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2);
    const fileName = `${timestamp}-${randomId}.${fileExt}`;

    // Create file path
    const filePath = tourId
      ? `${user.id}/${tourId}/${fileName}`
      : `${user.id}/${fileName}`;

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (uploadError) {
      throw uploadError;
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath);

    // Save media record to database
    const { data: mediaRecord, error: dbError } = await supabase
      .from('media_files')
      .insert({
        user_id: user.id,
        filename: fileName,
        original_filename: file.name,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        width: validation.metadata?.width,
        height: validation.metadata?.height,
        duration: validation.metadata?.duration,
        is_360: validation.metadata?.width && validation.metadata?.height
          ? (validation.metadata.width / validation.metadata.height) >= 1.8
          : false,
        processing_status: 'completed',
        metadata: {
          tour_id: tourId,
          bucket,
          upload_date: new Date().toISOString(),
          validation_warnings: validation.warnings,
        },
      })
      .select()
      .single();

    if (dbError) {
      // If database insert fails, clean up uploaded file
      await supabase.storage.from(bucket).remove([filePath]);
      throw dbError;
    }

    return NextResponse.json({
      success: true,
      data: {
        ...mediaRecord,
        public_url: publicUrl,
        validation_warnings: validation.warnings,
      },
    }, { status: 201 });
  } catch (error) {
    return handleAPIError(error);
  }
}

// DELETE /api/upload - Delete media file
export async function DELETE(request: NextRequest) {
  try {
    // Use new SSR client
    const supabase = await createAPIClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: { message: 'Authentication required' } },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const mediaId = searchParams.get('id');

    if (!mediaId) {
      return NextResponse.json(
        { success: false, error: { message: 'Media ID required' } },
        { status: 400 }
      );
    }

    // Get media record
    const { data: media, error: fetchError } = await supabase
      .from('media_files')
      .select('*')
      .eq('id', mediaId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: { message: 'Media file not found' } },
          { status: 404 }
        );
      }
      throw fetchError;
    }

    // Delete from storage
    const bucket = media.metadata?.bucket || 'media';
    const { error: storageError } = await supabase.storage
      .from(bucket)
      .remove([media.file_path]);

    if (storageError) {
      console.error('Storage deletion failed:', storageError);
      // Continue with database deletion even if storage fails
    }

    // Delete from database
    const { error: dbError } = await supabase
      .from('media_files')
      .delete()
      .eq('id', mediaId)
      .eq('user_id', user.id);

    if (dbError) {
      throw dbError;
    }

    return NextResponse.json({
      success: true,
      message: 'Media file deleted successfully',
    });
  } catch (error) {
    return handleAPIError(error);
  }
}

// GET /api/upload - Get user's media files
export async function GET(request: NextRequest) {
  try {
    // Use new SSR client
    const supabase = await createAPIClient();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: { message: 'Authentication required' } },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const tourId = searchParams.get('tourId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    let query = supabase
      .from('media_files')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id);

    if (tourId) {
      query = query.eq('metadata->>tour_id', tourId);
    }

    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(from, to);

    if (error) {
      throw error;
    }

    // Add public URLs
    const mediaWithUrls = data?.map(media => {
      const bucket = media.metadata?.bucket || 'media';
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(media.file_path);

      return {
        ...media,
        public_url: publicUrl,
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        media: mediaWithUrls,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      },
    });
  } catch (error) {
    return handleAPIError(error);
  }
}
