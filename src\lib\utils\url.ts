/**
 * Build URL with query parameters
 */
export function buildUrl(
  baseUrl: string,
  params: Record<string, string | number | boolean | undefined>
): string {
  const url = new URL(baseUrl);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      url.searchParams.set(key, String(value));
    }
  });
  
  return url.toString();
}

/**
 * Parse query parameters from URL
 */
export function parseQueryParams(url: string): Record<string, string> {
  const urlObj = new URL(url);
  const params: Record<string, string> = {};
  
  urlObj.searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  return params;
}

/**
 * Get current page URL
 */
export function getCurrentUrl(): string {
  if (typeof window !== 'undefined') {
    return window.location.href;
  }
  return '';
}

/**
 * Get base URL from environment
 */
export function getBaseUrl(): string {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  
  return process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
}

/**
 * Generate tour URL
 */
export function getTourUrl(slug: string): string {
  return `${getBaseUrl()}/tours/${slug}`;
}

/**
 * Generate tour embed URL
 */
export function getTourEmbedUrl(tourId: string): string {
  return `${getBaseUrl()}/embed/${tourId}`;
}

/**
 * Generate user profile URL
 */
export function getUserProfileUrl(userId: string): string {
  return `${getBaseUrl()}/profile/${userId}`;
}

/**
 * Generate share URLs for social platforms
 */
export function getShareUrls(url: string, title: string, description?: string) {
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description || '');

  return {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    whatsapp: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
    telegram: `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`,
    email: `mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A%0A${encodedUrl}`,
  };
}

/**
 * Generate WhatsApp message URL
 */
export function getWhatsAppUrl(
  phoneNumber: string,
  message: string
): string {
  const cleanedNumber = phoneNumber.replace(/\D/g, '');
  const encodedMessage = encodeURIComponent(message);
  
  return `https://wa.me/${cleanedNumber}?text=${encodedMessage}`;
}

/**
 * Check if URL is external
 */
export function isExternalUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const currentDomain = typeof window !== 'undefined' 
      ? window.location.hostname 
      : new URL(getBaseUrl()).hostname;
    
    return urlObj.hostname !== currentDomain;
  } catch {
    return false;
  }
}

/**
 * Sanitize URL to prevent XSS
 */
export function sanitizeUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return '#';
    }
    
    return urlObj.toString();
  } catch {
    return '#';
  }
}

/**
 * Extract domain from URL
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

/**
 * Check if URL is valid
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Generate API endpoint URL
 */
export function getApiUrl(endpoint: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || getBaseUrl();
  return `${baseUrl}/api${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;
}

/**
 * Generate Supabase storage URL
 */
export function getStorageUrl(bucket: string, path: string): string {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!supabaseUrl) {
    throw new Error('Supabase URL not configured');
  }
  
  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${path}`;
}

/**
 * Generate signed URL for private storage
 */
export function getSignedStorageUrl(
  bucket: string,
  path: string,
  expiresIn: number = 3600
): string {
  // This would typically be generated server-side
  // For now, return the public URL
  return getStorageUrl(bucket, path);
}

/**
 * Parse file path from storage URL
 */
export function parseStorageUrl(url: string): {
  bucket: string;
  path: string;
} | null {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    
    if (pathParts.includes('storage') && pathParts.includes('object')) {
      const storageIndex = pathParts.indexOf('storage');
      const bucket = pathParts[storageIndex + 4]; // /storage/v1/object/public/{bucket}
      const path = pathParts.slice(storageIndex + 5).join('/');
      
      return { bucket, path };
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Generate tour preview image URL
 */
export function getTourPreviewUrl(tourId: string): string {
  return getApiUrl(`/tours/${tourId}/preview`);
}

/**
 * Generate QR code URL for tour
 */
export function getQRCodeUrl(tourUrl: string): string {
  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(tourUrl)}`;
}
