jo-dev.md

# Jo's Development Guide

## Overview

This document outlines my personal development preferences, design principles, and technical decisions that can be applied across various projects regardless of the technology stack. It serves as a reference for both human developers and AI assistants working on my projects.

---

## 🧠 Development Philosophy

### Core Principles

- **User-Centric Design**: Always prioritize the end-user experience over technical preferences
- **Pragmatic Minimalism**: Choose the simplest solution that solves the problem effectively
- **Performance First**: Optimize for speed and responsiveness from the beginning
- **Maintainable Code**: Write code that is easy to understand, modify, and extend
- **Continuous Improvement**: Regularly refactor and improve existing code

### Development Approach

- **Agile Methodology**: Prefer iterative development with regular feedback cycles
- **Test-Driven Development**: Write tests before implementing features when possible
- **Documentation-Driven**: Document decisions, APIs, and architecture as you build
- **Component-Based**: Build systems from modular, reusable components
- **Accessibility-Focused**: Ensure applications are usable by people of all abilities

---

## 🎨 Design System

### Brand Identity

- **Primary Color**: Orange `#FF9A23` (iWalkTech signature color)
- **Secondary Colors**:
  - Purple: `#7e69ab`
  - Navy: `#1a1f2c`
  - Navy Dark: `#151a27`
- **Accent Colors**:
  - Success: `#22c55e`
  - Warning: `#eab308`
  - Danger: `#ef4444`
  - Info: `#3b82f6`

### Typography

- **Primary Font**: Inter (sans-serif)
- **Heading Font**: DM Sans (sans-serif)
- **Code Font**: JetBrains Mono (monospace)
- **Font Sizes**:
  - Base: 16px
  - Scale: 1.25 ratio

### UI Components

- **Component Library**: ShadCN UI v4
- **Icons**: Lucide Icons
- **Styling**: TailwindCSS with custom configuration
- **Dark Mode**: Always implement with thoughtful color choices

### Visual Effects

- **Glass Effect**: Subtle backdrop blur with transparency
  ```css
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }
  
  .dark .glass {
    background: rgba(26, 31, 44, 0.25);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(26, 31, 44, 0.18);
  }
  ```
- **Shadows**: Subtle, layered shadows for depth
- **Animations**: Smooth, purposeful animations (300ms duration)
- **Border Radius**: Consistent 0.5rem (8px) for most elements

---

## 🛠️ Technical Stack Preferences

### Frontend

- **Framework Preferences** (in order):
  1. Next.js (App Router)
  2. React
  3. Vue.js
  4. Vanilla JS with Web Components
  5. WordPress (Headless)

- **State Management**:
  - React: TanStack Query + Context API
  - Complex apps: Zustand or Redux Toolkit

- **Form Handling**:
  - React Hook Form
  - Zod for validation

- **CSS Approach**:
  - TailwindCSS with custom configuration
  - CSS Modules for component-specific styles
  - CSS Variables for theming

### Backend

- **Framework Preferences** (in order):
  1. Node.js + Express
  2. NestJS
  3. Laravel (PHP)
  4. Django (Python)
  5. Strapi (Headless CMS)

- **Database Preferences** (in order):
  1. PostgreSQL
  2. MySQL
  3. MongoDB
  4. SQLite (for small projects)
  5. Supabase or Firebase (for rapid development)

- **API Design**:
  - RESTful for simplicity
  - GraphQL for complex data requirements
  - tRPC for type-safe APIs

- **Authentication**:
  - JWT with proper security measures
  - OAuth 2.0 for third-party authentication
  - Session-based for traditional applications

### DevOps & Deployment

- **Hosting Preferences** (in order):
  1. VPS (DigitalOcean, Linode)
  2. Vercel (for Next.js)
  3. Netlify (for static sites)
  4. AWS (for enterprise)
  5. Traditional hosting with cPanel

- **CI/CD**:
  - GitHub Actions
  - Simple automated testing and deployment

- **Containerization**:
  - Docker for consistent environments
  - Docker Compose for local development

---

## 📋 Project Structure

### Directory Organization

```
project-root/
├── docs/                    # Documentation
│   ├── dev-checklist.md     # Development checklist
│   ├── spec.adoc            # Project specifications
│   └── screenshots/         # UI screenshots
├── frontend/                # Frontend code
│   ├── components/          # Reusable UI components
│   ├── pages/               # Page components
│   ├── lib/                 # Utility functions
│   ├── hooks/               # Custom hooks
│   ├── context/             # Context providers
│   ├── styles/              # Global styles
│   └── public/              # Static assets
├── backend/                 # Backend code
│   ├── controllers/         # Request handlers
│   ├── models/              # Data models
│   ├── routes/              # API routes
│   ├── services/            # Business logic
│   ├── middleware/          # Request middleware
│   └── utils/               # Utility functions
├── scripts/                 # Build and utility scripts
└── config/                  # Configuration files
```

### Naming Conventions

- **Files**: Descriptive, kebab-case for most files
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Functions**: camelCase (e.g., `getUserData()`)
- **CSS Classes**: kebab-case (e.g., `user-card`)
- **Database**: snake_case for tables and columns

---

## 🚀 Performance Optimization

### Frontend Performance

- Implement code splitting and lazy loading
- Optimize images with next-gen formats (WebP, AVIF)
- Use proper caching strategies
- Minimize JavaScript bundle size
- Implement critical CSS
- Use Intersection Observer for lazy loading
- Optimize fonts with font-display: swap

### Backend Performance

- Implement proper database indexing
- Use caching for expensive operations
- Optimize database queries
- Implement rate limiting
- Use connection pooling
- Implement pagination for large datasets

---

## 🔒 Security Practices

- Always use HTTPS
- Implement proper authentication and authorization
- Sanitize user inputs
- Use parameterized queries
- Implement CSRF protection
- Set proper security headers
- Regular security audits
- Keep dependencies updated

---

## 🧪 Testing Strategy

- **Unit Tests**: For individual functions and components
- **Integration Tests**: For component interactions
- **E2E Tests**: For critical user flows
- **Visual Regression**: For UI components
- **Accessibility Tests**: To ensure WCAG compliance
- **Performance Tests**: For critical paths

---

## 📱 Responsive Design

- Mobile-first approach
- Fluid typography and spacing
- Breakpoints:
  - Small: 640px
  - Medium: 768px
  - Large: 1024px
  - XL: 1280px
  - 2XL: 1536px
- Touch-friendly UI elements
- Optimize for various device capabilities

---

## 🤖 AI Integration

### AI Assistant Profile

- **Name**: Jo (Iwalk Tech)
- **Role**: Senior Full-Stack Developer & AI Specialist
- **Experience**: 10+ years in software development
- **Company**: iWalkTech
- **Specialties**: Web development, AI integration, UX/UI design
- **Communication Style**: Clear, concise, and technical

### AI Development Preferences

- Use AI for code generation, refactoring, and documentation
- Implement AI features that enhance user experience
- Focus on practical AI applications rather than bleeding-edge
- Ensure AI systems are explainable and transparent
- Prioritize user privacy and data security

---

## 📊 Analytics & Monitoring

- Implement privacy-friendly analytics
- Track key performance metrics
- Monitor error rates and performance
- Set up alerting for critical issues
- Regular performance audits
- User behavior analysis for UX improvements

---

## 🌐 Internationalization

- Design with i18n in mind from the start
- Use translation keys instead of hardcoded strings
- Support RTL languages when possible
- Consider cultural differences in design
- Implement proper date, time, and number formatting

---

## 📝 Documentation Standards

- README.md with clear setup instructions
- API documentation with examples
- Architecture diagrams
- Component documentation
- Regular screenshots of UI changes
- Decision logs for major architectural choices

---

*This document is a living guide and should be updated as preferences and best practices evolve.*