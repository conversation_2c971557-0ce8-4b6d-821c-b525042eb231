import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import type { Database } from '@/types/supabase';

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/dashboard/:path*',
  '/profile',
  '/settings',
];

// Define auth routes that should redirect to dashboard if already authenticated
const authRoutes = [
  '/auth/signin',
  '/auth/signup',
  '/auth/forgot-password',
  '/auth/reset-password',
];

// Define admin routes that require admin privileges
const adminRoutes = [
  '/admin',
  '/admin/:path*',
];

// Define API routes that need authentication
const protectedApiRoutes = [
  '/api/tours',
  '/api/media',
  '/api/users/profile',
  '/api/analytics',
  '/api/subscriptions',
];

function isRouteMatch(pathname: string, routes: string[]): boolean {
  return routes.some(route => {
    if (route.includes(':path*')) {
      const baseRoute = route.replace('/:path*', '');
      return pathname.startsWith(baseRoute);
    }
    return pathname === route;
  });
}

export async function middleware(request: NextRequest) {
  // Check if Supabase environment variables are available
  const hasSupabaseConfig =
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  let session = null;
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  if (hasSupabaseConfig) {
    try {
      // Create SSR middleware client
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            getAll() {
              return request.cookies.getAll();
            },
            setAll(cookiesToSet) {
              cookiesToSet.forEach(({ name, value, options }) => {
                request.cookies.set(name, value);
              });
              response = NextResponse.next({
                request,
              });
              cookiesToSet.forEach(({ name, value, options }) =>
                response.cookies.set(name, value, options)
              );
            },
          },
        }
      );

      const { data } = await supabase.auth.getSession();
      session = data.session;
    } catch (error) {
      console.error('Error getting session in middleware:', error);
    }
  }

  const { pathname } = request.nextUrl;

  // Handle API routes
  if (pathname.startsWith('/api/')) {
    // Check if API route requires authentication
    if (isRouteMatch(pathname, protectedApiRoutes)) {
      if (!session) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }
    }

    // Check for admin API routes
    if (pathname.startsWith('/api/admin/')) {
      if (!session) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Check if user is admin
      const { data: user } = await supabase
        .from('users')
        .select('is_admin')
        .eq('id', session.user.id)
        .single();

      if (!user?.is_admin) {
        return NextResponse.json(
          { error: 'Admin access required' },
          { status: 403 }
        );
      }
    }

    return response;
  }

  // Handle auth routes - redirect to dashboard if already authenticated
  if (isRouteMatch(pathname, authRoutes)) {
    if (hasSupabaseConfig && session) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
    return response;
  }

  // Handle protected routes - redirect to signin if not authenticated
  if (isRouteMatch(pathname, protectedRoutes)) {
    if (hasSupabaseConfig && !session) {
      const redirectUrl = new URL('/auth/signin', request.url);
      redirectUrl.searchParams.set('redirectTo', pathname);
      return NextResponse.redirect(redirectUrl);
    }
    return response;
  }

  // Handle admin routes
  if (isRouteMatch(pathname, adminRoutes)) {
    if (!hasSupabaseConfig || !session) {
      const redirectUrl = new URL('/auth/signin', request.url);
      redirectUrl.searchParams.set('redirectTo', pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // Check if user is admin
    try {
      const supabase = createMiddlewareClient<Database>({ req: request, res: response });
      const { data: user } = await supabase
        .from('users')
        .select('is_admin')
        .eq('id', session.user.id)
        .single();

      if (!user?.is_admin) {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
    return response;
  }

  // Handle subscription-based access
  if (pathname.startsWith('/dashboard/') && session) {
    // Get user's subscription status
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('status, plan_name')
      .eq('user_id', session.user.id)
      .single();

    // Check if user has access to premium features
    const premiumRoutes = [
      '/dashboard/analytics',
      '/dashboard/integrations',
      '/dashboard/api',
    ];

    if (isRouteMatch(pathname, premiumRoutes)) {
      const hasActivePremiumPlan = subscription?.status === 'active' &&
        ['pro', 'enterprise'].includes(subscription.plan_name?.toLowerCase() || '');

      if (!hasActivePremiumPlan) {
        const redirectUrl = new URL('/dashboard/upgrade', request.url);
        redirectUrl.searchParams.set('feature', pathname.split('/').pop() || '');
        return NextResponse.redirect(redirectUrl);
      }
    }
  }

  // Add security headers
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-pathname', pathname);

  // Set CSP headers for security
  const cspHeader = `
    default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.app *.supabase.co *.stripe.com *.paystack.co;
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: blob: *.supabase.co *.stripe.com;
    font-src 'self';
    connect-src 'self' *.supabase.co *.stripe.com *.paystack.co *.flutterwave.com wss:;
    media-src 'self' *.supabase.co;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
  `.replace(/\s{2,}/g, ' ').trim();

  response.headers.set('Content-Security-Policy', cspHeader);
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
