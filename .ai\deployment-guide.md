# Deployment Guide - VirtualRealTour Platform

## Overview
Comprehensive deployment guide for the VirtualRealTour platform using Vercel for frontend hosting and Supabase for backend infrastructure.

## Prerequisites

### Required Accounts
- [Vercel Account](https://vercel.com) - Frontend hosting
- [Supabase Account](https://supabase.com) - Backend infrastructure
- [GitHub Account](https://github.com) - Code repository
- [Stripe Account](https://stripe.com) - International payments
- [Paystack Account](https://paystack.com) - Nigerian payments
- [Google Cloud Account](https://cloud.google.com) - Maps API and analytics

### Required Tools
- Node.js 18+ and npm/yarn
- Git
- Supabase CLI
- Vercel CLI (optional)

## Environment Setup

### Development Environment Variables
Create `.env.local` file in project root:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Database
DATABASE_URL=your_supabase_database_url

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# File Storage
NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET=media
SUPABASE_STORAGE_URL=your_supabase_storage_url

# Payment Providers
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

PAYSTACK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_...

FLUTTERWAVE_SECRET_KEY=FLWSECK_TEST-...
NEXT_PUBLIC_FLUTTERWAVE_PUBLIC_KEY=FLWPUBK_TEST-...

# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token

# Google Services
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Email Service
RESEND_API_KEY=re_...
SENDGRID_API_KEY=SG...

# Analytics & Monitoring
SENTRY_DSN=your_sentry_dsn
NEXT_PUBLIC_SENTRY_DSN=your_public_sentry_dsn

# Feature Flags
NEXT_PUBLIC_ENABLE_VR=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_PAYMENTS=true
```

### Production Environment Variables
Same structure but with production values and additional security configurations.

## Supabase Backend Deployment

### 1. Create Supabase Project
```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Initialize project
supabase init

# Link to remote project
supabase link --project-ref your-project-ref
```

### 2. Database Setup
```bash
# Run database migrations
supabase db push

# Generate TypeScript types
supabase gen types typescript --local > src/types/supabase.ts
```

### 3. Storage Configuration
```bash
# Create storage buckets
supabase storage create media --public
supabase storage create avatars --public
supabase storage create thumbnails --public
```

### 4. Edge Functions Deployment
```bash
# Deploy Edge Functions
supabase functions deploy media-processor
supabase functions deploy analytics-tracker
supabase functions deploy whatsapp-webhook
```

### 5. Authentication Configuration
Configure in Supabase Dashboard:
- Enable email/password authentication
- Configure OAuth providers (Google, Facebook, etc.)
- Set up email templates
- Configure redirect URLs

## Vercel Frontend Deployment

### 1. Connect Repository
1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your GitHub repository
4. Configure build settings

### 2. Build Configuration
Create `vercel.json` in project root:
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/api/webhooks/stripe",
      "destination": "/api/webhooks/stripe"
    }
  ]
}
```

### 3. Environment Variables Setup
In Vercel Dashboard:
1. Go to Project Settings → Environment Variables
2. Add all production environment variables
3. Set appropriate environments (Production, Preview, Development)

### 4. Domain Configuration
1. Add custom domain in Vercel Dashboard
2. Configure DNS records
3. Enable SSL certificate
4. Set up redirects if needed

## Database Migration Strategy

### 1. Migration Files Structure
```
supabase/
├── migrations/
│   ├── 20240101000000_initial_schema.sql
│   ├── 20240102000000_add_tours_table.sql
│   ├── 20240103000000_add_scenes_table.sql
│   └── ...
├── seed.sql
└── config.toml
```

### 2. Running Migrations
```bash
# Development
supabase db reset

# Production
supabase db push --linked
```

### 3. Rollback Strategy
```bash
# Rollback to specific migration
supabase db reset --to 20240101000000
```

## CI/CD Pipeline

### GitHub Actions Workflow
Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm run test
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## Monitoring & Analytics Setup

### 1. Sentry Error Tracking
```bash
# Install Sentry
npm install @sentry/nextjs

# Configure in next.config.js
const { withSentryConfig } = require('@sentry/nextjs');

module.exports = withSentryConfig(
  {
    // Your Next.js config
  },
  {
    silent: true,
    org: 'your-org',
    project: 'virtualrealtour',
  }
);
```

### 2. Vercel Analytics
```bash
# Install Vercel Analytics
npm install @vercel/analytics

# Add to app layout
import { Analytics } from '@vercel/analytics/react';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  );
}
```

### 3. Google Analytics
```typescript
// lib/gtag.ts
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID;

export const pageview = (url: string) => {
  window.gtag('config', GA_TRACKING_ID, {
    page_path: url,
  });
};

export const event = ({ action, category, label, value }) => {
  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  });
};
```

## Security Configuration

### 1. Content Security Policy
```typescript
// next.config.js
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: `
      default-src 'self';
      script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.app *.supabase.co;
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: blob: *.supabase.co;
      font-src 'self';
      connect-src 'self' *.supabase.co *.stripe.com *.paystack.co;
    `.replace(/\s{2,}/g, ' ').trim()
  }
];
```

### 2. Rate Limiting
```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(10, '10 s'),
});

export async function middleware(request: NextRequest) {
  const ip = request.ip ?? '127.0.0.1';
  const { success } = await ratelimit.limit(ip);

  if (!success) {
    return new NextResponse('Too Many Requests', { status: 429 });
  }

  return NextResponse.next();
}
```

## Backup & Recovery

### 1. Database Backups
```bash
# Manual backup
supabase db dump --linked > backup.sql

# Automated backups (configured in Supabase Dashboard)
# - Daily backups for 7 days
# - Weekly backups for 4 weeks
# - Monthly backups for 12 months
```

### 2. File Storage Backups
```typescript
// scripts/backup-storage.ts
import { createClient } from '@supabase/supabase-js';
import AWS from 'aws-sdk';

const supabase = createClient(url, key);
const s3 = new AWS.S3();

async function backupStorage() {
  const { data: files } = await supabase.storage
    .from('media')
    .list();

  for (const file of files) {
    const { data } = await supabase.storage
      .from('media')
      .download(file.name);

    await s3.upload({
      Bucket: 'backup-bucket',
      Key: file.name,
      Body: data
    }).promise();
  }
}
```

## Performance Optimization

### 1. Image Optimization
```typescript
// next.config.js
module.exports = {
  images: {
    domains: ['your-supabase-project.supabase.co'],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
};
```

### 2. Bundle Analysis
```bash
# Install bundle analyzer
npm install --save-dev @next/bundle-analyzer

# Analyze bundle
npm run analyze
```

### 3. Caching Strategy
```typescript
// lib/cache.ts
import { unstable_cache } from 'next/cache';

export const getCachedTours = unstable_cache(
  async () => {
    // Fetch tours from database
  },
  ['tours'],
  { revalidate: 3600 } // 1 hour
);
```

## Troubleshooting

### Common Issues
1. **Build Failures**: Check environment variables and dependencies
2. **Database Connection**: Verify Supabase configuration
3. **Authentication Issues**: Check redirect URLs and providers
4. **File Upload Problems**: Verify storage bucket permissions
5. **Payment Integration**: Check webhook endpoints and keys

### Debug Commands
```bash
# Check Supabase status
supabase status

# View logs
vercel logs your-deployment-url

# Test database connection
supabase db ping
```

### Support Resources
- [Vercel Documentation](https://vercel.com/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)

This deployment guide ensures a smooth and secure deployment process for the VirtualRealTour platform with proper monitoring, security, and backup strategies.
