{"name": "virtualrealtour_platform", "version": "0.1.0", "private": true, "scripts": {"dev": "bun --bun next dev --turbo", "build": "next build", "start": "next start", "lint": "bun --bun next lint", "lint:fix": "bun --bun next lint --fix", "format": "bun --bun prettier --write .", "format:check": "bun --bun prettier --check .", "type-check": "bun --bun tsc --noEmit", "test": "bun test", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage", "analyze": "cross-env ANALYZE=true next build", "db:generate-types": "supabase gen types typescript --local > src/types/supabase.ts", "db:reset": "supabase db reset", "db:push": "supabase db push", "install:bun": "bun install", "dev:npm": "next dev --turbopack", "build:bun": "bun --bun next build"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@stripe/stripe-js": "^7.3.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.10", "@types/three": "^0.177.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "recharts": "^2.15.3", "stripe": "^18.2.1", "tailwind-merge": "^3.3.0", "three": "^0.177.0", "zod": "^3.25.51"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.58", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}